package com.digiwin.escloud.aiomail.services.Impl;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiomail.Mail;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.aiomail.amqp.MqConstant;
import com.digiwin.escloud.aiomail.mail.ReadHtmlFile;
import com.digiwin.escloud.aiomail.mail.SimpleMailSender;
import com.digiwin.escloud.aiomail.model.MailLog;
import com.digiwin.escloud.aiomail.model.SimpleMail;
import com.digiwin.escloud.aiomail.services.IMailService;
import com.digiwin.escloud.aiomail.services.factory.MailContentFactoryService;
import com.digiwin.escloud.aiomail.util.MessageUtils;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSFindIterable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ResourceUtils;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RefreshScope
public class MailService implements IMailService {
    @Value("${smtpHostName}")
    private String SmtpHostName;

    @Value("${smtpHostPort}")
    private String SmtpHostPort;

    @Value("${mailSender}")
    private String mailSender;

    @Value("${mailSenderPwd}")
    private String mailSenderPwd;

    @Value("${noSendMail:false}")
    private boolean noSendMail;

    @Value("${language}")
    private String language;

    private String unknownMail = "<EMAIL>";

    private int TRY_NUM = 3;

    @Autowired
    @Qualifier(value = "primaryMongoTemplate")
    MongoTemplate mongoTemplate;

    @Autowired
    @Qualifier(value = "secondaryGridFsTemplate")
    protected GridFsTemplate gridFsTemplate;
    @Value("${shipmentUrl}")
    private String shipmentUrl;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    GridFSBucket gridFSBucket;

    @Autowired
    MessageUtils messageUtils;

    @Autowired
    MailContentFactoryService mailContentFactoryService;

    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;

    @Override
    public BaseResponse SendMail(Mail mail) {
        if (log.isInfoEnabled()) {
            log.info("--------SendMail-----" + new Gson().toJson(mail));
        }
        boolean res = false;
        int i = 1;
        try {
            List<String> receivers = mail.getReceivers();
            if (CollectionUtils.isEmpty(receivers)) {
                log.warn("receivers original is empty! ");
                //出货管理的邮件收件人可能是空，因为有抄送人不为空的情况，需要发给抄送人
                if(!MailSourceType.ShipmentInfo.equals(mail.getMailSourceType())){
                    return BaseResponse.ok(false);
                }
            }
            //判断收件人为空，则退出
            receivers = mail.getReceivers().stream()
                    .filter(s -> !StringUtils.isEmpty(s))
                    .filter(s -> !unknownMail.equalsIgnoreCase(s))
                    .collect(Collectors.toList());

            mail.setReceivers(receivers);

            if (CollectionUtils.isEmpty(receivers)) {
                log.warn("receivers filter is empty! ");
                //出货管理的邮件收件人可能是空，因为有抄送人不为空的情况，需要发给抄送人
                if(!MailSourceType.ShipmentInfo.equals(mail.getMailSourceType())){
                    return BaseResponse.ok(false);
                }
            }
            SimpleMail sm = new SimpleMail();
            HashMap<String, InputStream> imgData = new HashMap<>();
            //如果语言别为空自动修正语言别
            if (StringUtils.isBlank(mail.getLanguage())) {
                mail.setLanguage(language);
            }
            String language = mail.getLanguage();
//            Resource resource = new PathMatchingResourcePatternResolver()
//                    .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + MessageUtils.getLanguage(language) + "/mailHeader.jpg");

            imgData.put("imageMailHeader", new PathMatchingResourcePatternResolver()
                    .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + MessageUtils.getLanguage(language) + "/mailHeader.jpg").getInputStream());
            imgData.put("imageMailFooter", new PathMatchingResourcePatternResolver()
                    .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + MessageUtils.getLanguage(language) + "/mailFooter.jpg").getInputStream());
            Boolean isSalesOrder = mail.getMailSourceType().equals(MailSourceType.SalesOrder);
            if (mail.getMailSourceType().equals(MailSourceType.Issue) || mail.getMailSourceType().equals(MailSourceType.CmdbReport) || mail.getMailSourceType().equals(MailSourceType.EdrReport) || isSalesOrder) {
                //避免邮件夹带没有使用的附件，造成客户困扰，增加判断为SalesOrder或语言别为简体才放入QRCode附件
                if (isSalesOrder || "zh_CN".equals(language) || "zh-CN".equals(language)) {
                    imgData.put("imageQrcode", new PathMatchingResourcePatternResolver()
                            .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + MessageUtils.getLanguage(language) + "/mailQrcode.jpg").getInputStream());
                }
            }
            if (mail.getMailSourceType().equals(MailSourceType.EmployeeInviteV2)) {
                if ("zh-CN".equals(language)) {
                imgData.put("imageEmployee", new PathMatchingResourcePatternResolver()
                        .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + MessageUtils.getLanguage(language) + "/employeeInvitation.png").getInputStream());
                imgData.put("imageEmployeeInvitation", new PathMatchingResourcePatternResolver()
                        .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + MessageUtils.getLanguage(language) + "/employeeInvitationPicture.png").getInputStream());
                }
            }
            if (mail.getMailSourceType().equals(MailSourceType.ActivateUser)) {
                imgData.put("imageActivateUserBackground", new PathMatchingResourcePatternResolver()
                        .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + MessageUtils.getLanguage(language) + "/activateUserBackground.png").getInputStream());
                imgData.put("imageServiceRight", new PathMatchingResourcePatternResolver()
                        .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + MessageUtils.getLanguage(language) + "/serviceRight.png").getInputStream());
            }
            if (mail.getMailSourceType().equals(MailSourceType.VersionUpdateAssessmentReport)) {
                imgData.put("imageTitleBackground", new PathMatchingResourcePatternResolver()
                        .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + MessageUtils.getLanguage(language) + "/version-evaluation-report/title-background.png").getInputStream());
                imgData.put("imageTitleWhale", new PathMatchingResourcePatternResolver()
                        .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + MessageUtils.getLanguage(language) + "/version-evaluation-report/title-whale.png").getInputStream());
            }
            String mailSubject = mail.getSubject();
            try {
                mailSubject = messageUtils.get(mailSubject, language);
//                mailSubject = EsCloudMailSubject.valueOf(mail.getSubject()).getSubject(mail.getLanguage());
                Map<String, String> subjectParams = mail.getSubjectParams();
                if (!CollectionUtils.isEmpty(subjectParams)) {
                    for (String key : mail.getSubjectParams().keySet()) {
                        mailSubject.replaceAll("#\\{" + key + "}", mail.getSubjectParams().get(key));
                    }
                }
                mail.setSubject(mailSubject);
            } catch (Exception ex) {
                log.error(String.format("mailSubject get or replaceAll subject:{} language:{} exception:",
                        mailSubject, language), ex);
            }
            sm.setSubject(PrepareMailSubject(mail));
            sm.setContent(PrepareMailMessage(mail));
            sm.setMailAttachmentInfoList(mail.getMailAttachmentInfoList());
            SimpleMailSender sms = determineSimpleMailSender(mail);
//            SimpleMailSender sms = new SimpleMailSender(SmtpHostName, SmtpHostPort, mailSender, mailSenderPwd);//("<EMAIL>","a123456789");
            while (!res && i <= TRY_NUM) {
                try {
                    if (log.isInfoEnabled()) {
                        log.info("receivers:{}", receivers.stream().collect(Collectors.joining(";")));
                    }
                    if (noSendMail) {
                        log.info("properties config setting not send mail!");
                    } else {
                        if (mail.isHasAttachement()) {
                            //新增出货的邮件，里面包含附件（外显）以及密送人
                            if(MailSourceType.ShipmentInfo.equals(mail.getMailSourceType())){
                                sms.send(mail.getReceivers(), mail.getCcs(), mail.getBccs(), sm.getSubject(), sm.getContent(), imgData, shipmentUrl);
                            }else{
                                List<String> attachementNameList = getAttachementFileList(mail.getSourceId());
                                sms.send(mail.getReceivers(), mail.getCcs(), sm.getSubject(), sm.getContent(),
                                        attachementNameList, imgData, gridFsTemplate, gridFSBucket);
                            }

                        } else {
                            List<String> ccs = mail.getCcs();
                            if (!CollectionUtils.isEmpty(ccs)) {
                                if (log.isInfoEnabled()) {
                                    log.info("ccs:{}", ccs.stream().collect(Collectors.joining(";")));
                                }
                                sms.send(mail.getReceivers(), ccs, sm, imgData);
                            } else {
                                sms.send(mail.getReceivers(), sm, imgData);
                            }
                        }
                    }
                    res = true;
                } catch (Exception ex) {
                    log.error("sms.send exception:", ex);
                    //ex.printStackTrace();
                    res = false;
                    i = i + 1;
                }
            }
        } catch (Exception ex) {
            log.error("SendMail exception:", ex);
            return BaseResponse.error(ex);
            //ex.printStackTrace();
        } finally {
            SaveMailSendLog(mail, res, i);
        }
        return BaseResponse.ok(res);
    }

    private SimpleMailSender determineSimpleMailSender(Mail mail) {
        if (mail.getMailSourceType().equals(MailSourceType.Warning)) {
//            List<String> receivers = mail.getReceivers();
//            if (!CollectionUtils.isEmpty(receivers) && receivers.contains("<EMAIL>")) {
            return new SimpleMailSender("true", "smtpdm.aliyun.com", "465", "<EMAIL>", "DigiwinCloud238");
//            }
        }
        return new SimpleMailSender(SmtpHostName, SmtpHostPort, mailSender, mailSenderPwd);
    }

    private String PrepareMailSubject(Mail mail) {
        return mailContentFactoryService.getMailSubject(mail);
    }

    private String PrepareMailMessage(Mail mail) {
        String content = mailContentFactoryService.getMailContent(mail);
        String url = mail.getUrl();
        if (StringUtils.isNotBlank(url)) {
            System.out.println("souce url " + url);
            String str = ReadHtmlFile.getSource(url);//"https://escloud.digiwin.biz:6443/Login.html");
            //String str = ReadHtmlFile.readFile("这里填写你需要发送的本地文件路径");
            content = content + "/r/n" + "<h1>This is a test</h1>"
                    + "<img src=\"http://************:9997/001220587.jpg\">";
        }
        return content;
    }

    private void SaveMailSendLog(Mail mail, boolean successed, int trynum) {
        try {
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String receivers = "";
            for (String one : mail.getReceivers()) {
                receivers = receivers + (receivers.isEmpty() ? "" : ";") + one;
            }
            String ccs = "";
            if (mail.getCcs() != null) {
                for (String one : mail.getCcs()) {
                    ccs = ccs + (ccs.isEmpty() ? "" : ";") + one;
                }
            }

//            MailLog mailLog = new MailLog();
//            if (mail.getMailSourceType() != null) {
//                mailLog.setSourceType(mail.getMailSourceType().toString());
//            }
//            mailLog.setSourceId(mail.getSourceId());
//            mailLog.setReceivers(receivers);
//            mailLog.setCcs(ccs);
//            mailLog.setSubject(mail.getSubject());
//            mailLog.setMessage(mail.getMessage());
//            mailLog.setUrl(mail.getUrl());
//            mailLog.setPriority(mail.getPriority());
//            mailLog.setMailStatus(successed ? "S" : "E");
//            mailLog.setProcesser("");
//            mailLog.setSendTime(sdf.format(new Date()));
//            mailLog.setTryNums(trynum);
            //mailDao.InsertMailLog(mailLog);
            //maillog存到mongodb
            //mongoTemplate.insert(mailLog);

            Map<String, Object> mapMail = new LinkedHashMap<>();
            mapMail.put("_id", String.valueOf(SnowFlake.getInstance().newId()));
            String sourceType = "";
            if (mail.getMailSourceType() != null) {
                sourceType = mail.getMailSourceType().toString();
            }
            mapMail.put("sourceType", sourceType);
            mapMail.put("sourceId", mail.getSourceId());
            mapMail.put("receivers", receivers);
            mapMail.put("ccs", ccs);
            mapMail.put("subject", mail.getSubject());
            mapMail.put("message", mail.getMessage());
            mapMail.put("url", mail.getUrl());
            mapMail.put("priority", mail.getPriority());
            mapMail.put("mailStatus", successed ? "S" : "E");
            mapMail.put("sendTime", sdf.format(new Date()));
            mapMail.put("processer", "");
            mapMail.put("tryNums", trynum);
            mapMail.put("wechatStatus","");
            mapMail.put("wechatMsg", "");
            mapMail.put("smsStatus", "");
            mapMail.put("smsMsg", "");
            log.info("mapMailLog1: " + mapMail);
            this.mailSave(mapMail);
        } catch (Exception ex) {
            log.error("SaveMailSendLog exception:", ex);
            //ex.printStackTrace();
        }
    }

    //获取T案件的附件名称(MongoDB存储的filename)
    private List<String> getAttachementFileList(String issueId) throws RuntimeException {
        //todu
        if (StringUtils.isEmpty(issueId)) {
            throw new NullPointerException("issueId为null或空字符串");
        }
        try {
            GridFSFindIterable iterable = gridFsTemplate.find(
                    new Query(Criteria.where("metadata.issueId").is(Long.parseLong(issueId))));
            List<String> filenames = new ArrayList<>();
            iterable.forEach(p -> {
                filenames.add(p.getFilename());
            });

            if (CollectionUtils.isEmpty(filenames)) {
                throw new NullPointerException("没有获取到附件名列表");
            } else {
                return filenames;
            }
        } catch (RuntimeException e) {
            log.error("获取案件附件列表出错，issueId:" + issueId, e);
            throw e;
        }
    }

    @Override
    public BaseResponse SendMailByQueue(Mail mail) {
        rabbitTemplate.convertAndSend(MqConstant.API_MAIL_EXCHANGE, MqConstant.API_MAIL_ROUTINGKEY, mail);
        return BaseResponse.ok();
    }

    @Override
    public ResponseBase mailSave(Map<String, Object> mapMail) throws JsonProcessingException {
        try {
            List<Map<String, Object>> dataContentList = new ArrayList<>();
            dataContentList.add(mapMail);
            ObjectMapper mapper = new ObjectMapper();
            // 轉成JSON字串
            String dataContentJson = mapper.writeValueAsString(dataContentList);
            Map<String, Object> bodyMap = new LinkedHashMap<>();
            bodyMap.put("deviceId", "");
            bodyMap.put("eid", RequestUtil.getHeaderEid());
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            bodyMap.put("collectedTime", sdf.format(new Date()));
            bodyMap.put("collectConfigId", "");
            bodyMap.put("uploadDataModelCode", "MailLogs");
            bodyMap.put("deviceCollectDetailId", "");
            bodyMap.put("aiId", "");
            bodyMap.put("aiopsItem", "");
            bodyMap.put("flumeTimestamp", "");
            bodyMap.put("execWorkflow", "");
            bodyMap.put("dataContent", dataContentJson);
            // 將bodyMap轉成JSON字串
            String bodyJson = mapper.writeValueAsString(bodyMap);
            // 建立 headers，固定
            Map<String, Object> headers = new LinkedHashMap<>();
            headers.put("namenode", "namenode.example.com");
            headers.put("datanode", "random_datanode.example.com");
            headers.put("SourceType", "JSON");
            // 建立最外層 map
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("headers", headers);
            map.put("body", bodyJson);
            // 放入list
            List<Map<String, Object>> mailList = new ArrayList<>();
            mailList.add(map);
            // 存進sr表
            log.info("mailListLog3: {}" , JSONObject.toJSONString(mailList));
            ResponseBase responseBase = aioItmsFeignClient.webLogAggQuery(mailList);
            return responseBase;
        } catch (Exception ex) {
            log.error("SaveMailSendLog exception:", ex);
            throw ex;
        }
    }
}
