@startuml
title 网安稽查报告生成新流程时序图 (数据库报告方案)

participant "后台服务(网安稽查服务)" as NetworkService
participant "后台服务(一键体检服务)" as ExamService  
participant "后台服务(数据库报告服务)" as DbService
participant "大数据平台" as BigData
participant "Mysql" as MySQL
participant "ES" as ElasticSearch
participant "后台服务(AI服务)" as AIService

== 异步报告生成流程 ==

NetworkService -> NetworkService: CompletableFuture.runAsync(() -> saveNetworkSecurityExamReport2Es(data))
activate NetworkService

note right of NetworkService: 【变化点1】组织类数据在新流程中会删除
NetworkService -> NetworkService: 查询组织类数据 (将被删除)
note right: getProjectType(NETWORK_SECURITY_EXAM_PORJECT_ORGANIZATIONAL, ...)
NetworkService -> BigData: queryModelDetail(modelCodeList, whereClause)
BigData --> NetworkService: organizationalDataList
NetworkService -> NetworkService: doModelData(organizationalData, modelDetail, record)

== 1. 查询体检记录分数数据 ==

NetworkService -> ExamService: getReportScore(aerId, scale)
note right: 发起查询体检记录分数请求
activate ExamService

ExamService -> MySQL: 查询体检记录数据
note right: selectAiopsExamRecordReport(aerId)\nselectIndexTypeByAeid(aeId)\nselectAiopsExamItem(aerId)
MySQL --> ExamService: 返回分数数据

ExamService -> ExamService: 计算各指标类型分数
ExamService --> NetworkService: BaseResponse<AiopsExamRecordsReportRecord>
deactivate ExamService

== 2. 查询指标检测值数据 (新流程重点) ==

note over NetworkService, DbService: 【变化点2】使用数据库报告方案查询指标检测值和参考值

NetworkService -> DbService: getDbReportData(id, dbType, productCode)
note right: 发起查询指标检测值数据
activate DbService

DbService -> ElasticSearch: 查询数据库报告数据
note right: esService.findById(id, clazz)
ElasticSearch --> DbService: DefaultOracleDbReport

DbService -> BigData: 查询指标检测值数据
note right: 使用bigDataUtil查询StarRocks\n获取实时检测值数据
BigData --> DbService: 返回指标检测值数据

DbService -> MySQL: 查询指标参考值数据
note right: 从report_reference_value表查询
MySQL --> DbService: 返回指标参考值数据

DbService -> DbService: processActualValue.processValue()
note right: 【变化点3】整理参考值、检测值数据\n按规则拼接检测值 比如 c: 10% d: 20%\n需重写ProcessActualValue接口的行为

DbService --> NetworkService: 返回指标检测值数据
deactivate DbService

== 3. 查询预警数据 ==

NetworkService -> BigData: warningService.getDailyWarningInfo()
note right: 发起查询预警数据
BigData --> NetworkService: 返回预警数据

== 4. 组合数据并生成报告结论 (新流程重点) ==

NetworkService -> NetworkService: 组合报告数据和分数数据、预警数据

note over NetworkService, AIService: 【变化点4】调用AI服务生成报告结论

NetworkService -> AIService: 发起报告结论生成请求
note right: 调用aioai项目的AI服务
activate AIService

AIService -> AIService: 发起生成报告结论API
note right: 调用IndepthAI服务

AIService -> AIService: 阻塞等待报告结论
note right: 类似ChatGptService.EvaluationConclusion\ncountDownLatch.await(5, TimeUnit.MINUTES)

AIService --> NetworkService: 返回报告结论
deactivate AIService

== 5. 存储报告详情 ==

NetworkService -> ElasticSearch: networkSecurityExamEsReport.generateReport()
note right: 【变化点5】存储报告详情\n现在不再存储组织类数据\n报告指标分数数据实时查询，因检测值的不同查询逻辑需存储ES
ElasticSearch --> NetworkService: 存储完成

== 6. 更新报告记录 ==

NetworkService -> MySQL: updateReportStatus()
note right: 更新报告记录状态
MySQL --> NetworkService: 更新完成

deactivate NetworkService

== 新流程关键变化总结 ==

note over NetworkService, AIService
【主要变化点】
1. 组织类数据(organizationalData)在新流程中会删除
2. 使用数据库报告服务查询指标检测值和参考值
3. 需重写ProcessActualValue接口处理检测值拼接
4. 新增AI服务调用生成报告结论
5. ES存储策略调整，不再存储组织类数据

【技术实现】
- 数据库报告方案：查询StarRocks获取检测值，MySQL获取参考值
- AI服务集成：调用aioai项目，阻塞等待IndepthAI响应
- 存储优化：实时查询分数数据，ES存储报告详情
end note

@enduml
