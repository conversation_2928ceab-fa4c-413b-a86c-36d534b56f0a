package com.digiwin.escloud.aiobasic.sync.dao;

import com.digiwin.escloud.aiobasic.sync.model.supplier.SupplierTenantMap;
import com.digiwin.escloud.aiobasic.sync.model.tenant.*;
import com.digiwin.escloud.aiobasic.sync.model.user.EsUser;
import com.digiwin.escloud.aiobasic.sync.model.user.UserPersonalInfo;
import com.digiwin.escloud.aiobasic.sync.model.user.UserTenantMap;
import com.digiwin.escloud.aiobasic.sync.model.user.UserThirdParty;
import com.digiwin.escloud.aiouser.model.tenant.TenantContract;
import com.digiwin.escloud.aiouser.model.tenant.TenantContractEx;
import com.digiwin.escloud.aiouser.model.user.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface SyncMapper {
    List<EsUser> getEsUsers(HashMap<String, Object> map);

    List<EsCustomer> getEsCustomers(HashMap<String, Object> map);

    int updateEsCustomer(HashMap<String, Object> map);

    int updateSupplierCustomerEid(HashMap<String, Object> map);

    List<EsCustomerContract> getEsCustomerContracts(Map<String, Object> map);

    List<String> getAioAllTenantIds();

    int batchInsertTenant(List<Tenant> tenants);

    int insertTenant(Tenant tenant);

    int insertSupplierTenantMap(SupplierTenantMap supplierTenantMap);

    Integer getUserExist(long userSid);

    Integer getUserTenantMapExist(@Param("userSid") long userSid, @Param("eid") long eid);

    int insertUser(User user);

    int updateUser(User user);

    int insertUserTenantMap(UserTenantMap userTenantMap);

    int insertUserPersonalInfo(UserPersonalInfo userPersonalInfo);

    int insertTpUser(UserThirdParty userThirdParty);

    int insertTenantContract(TenantContract tenantContract);

    /**
     * 依据条件字典查询租户列表
     * @param map 条件字典
     * @return 租户列表
     */
    List<Tenant> selectTenantListByMap(Map<String, Object> map);

    /**
     * 依据条件字典查询租户合约列表
     * @param map 条件字典
     * @return 租户列表
     */
    List<TenantModeContract> selectTenantContractListByMap(Map<String, Object> map);


    int updateTenantModuleContractsBySync(
            @Param("tenantModuleContracts") Collection<TenantModeContract> tenantModuleContracts);

    int insertTenantModuleContractsBySync(
            @Param("inserttenantModuleContracts") Collection<TenantModeContract> inserttenantModuleContracts);

    int insertTenantModuleContractsDetailBySync(
            @Param("inserttenantModuleContractsDetail") Collection<TenantModeContract> inserttenantModuleContractsDetail);


    /**
     * 依据运维商模组类别代号获取运维商模组类别Id
     * @param map 条件字典
     * @return 运维商模组类别Id
     */
    Long selectSamcIdByMap(Map<String, Object> map);

    int selectTmcExistByMap(Map<String, Object> map);


    /**
     * 依据工号查询员工信息字典列表
     * @param workNo 工号
     * @return 员工信息字典列表
     */
    List<Map<String, Object>> selectSupplierEmployeeByWorkNo(String workNo);

    List<String> getSyncServcieCodeByRecentTime(HashMap<String, Object> map);

    /**
     * 查询合约范围内数据
     *
     * @param params
     * @return
     */
    List<String> queryServiceCodeByContractTime(HashMap<String, Object> params);

    /**
     * for 穩態的清單
     * *擷取配置為自動更新的應用程式的租用戶模組合約清單。
     *
     * @return TenantModeContract 物件列表，表示具有自動更新配置的租用戶模組合約
     */
    List<TenantModeContract> getAppAutoUpdate();

    /**
     * for 穩態的清單
     * 檢索租戶模組合約列表以及詳細信息
     * 與配置為自動更新的應用程式相關。
     *
     * @return 包含詳細資訊的 TenantModeContract 物件列表
     * 關於具有自動更新配置的應用程式。
     */
    List<TenantModeContract> getAppAutoUpdateDetail();

    /**
     * * 檢索用於復原應用程式的授權 ID 列表
     * 配置自動更新。
     *
     * @return 代表授權 ID 的 Long 物件列表
     * 用於恢復自動更新的應用程式。
     */
    List<Long> getAppAutoUpdateRecoverAuthIds();

    /**
     * 檢索包含現有 EID（實體 ID）和服務代碼的對應資訊清單。
     *
     * @return 映射列表，其中每個映射表示現有 EID 與其關聯服務代碼之間的映射。
     * 每個映射都包含鍵值對，其中包含有關 EID 和服務代碼的資訊。
     */
    List<EidAndProductOwner> getExistEidAndServiceCodeMapping();

    List<TenantContractEx> select147TenantContractByDatetime(@Param("serviceCodeList") List<String> serviceCodeList, @Param("escloudDBName") String escloudDBName);
    List<TenantContractEx> select147TenantContractByInstalled(@Param("escloudDBName") String escloudDBName);

    /**
     * 线上修复问题使用，只使用一次
     *
     * @param escloudDBName
     * @return
     */
    List<TenantContractEx> selectAll147TenantContract(@Param("escloudDBName") String escloudDBName,
                                                      @Param("serviceCodeList") List<String> serviceCodeList,
                                                      @Param("updateTime") String updateTime);

    int updateTenantInstalled(
            @Param("tenantSids") Collection<Long> tenantSids);

}
