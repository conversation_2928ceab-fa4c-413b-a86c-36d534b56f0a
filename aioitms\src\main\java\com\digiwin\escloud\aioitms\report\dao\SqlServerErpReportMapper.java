package com.digiwin.escloud.aioitms.report.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface SqlServerErpReportMapper {
    List<Map<String, String>> getProductCode(@Param("escloudDBName") String escloudDBName ,
                                             @Param("customerCode") String customerCode,
                                             @Param("productCodeList") List<String> productCodeList);
    Integer getLicensesCount(@Param("escloudDBName") String escloudDBName,
                             @Param("customerCode") String customerCode,
                             @Param("productCode") String productCode);
    List<Map<String, Object>> getDgwModules(@Param("escloudDBName") String escloudDBName,
                                            @Param("customerCode") String customerCode,
                                            @Param("productCode") String productCode);
    List<Map<String, Object>> matchErpModules(@Param("escloudDBName") String escloudDBName,
                                              @Param("productCode") String productCode,
                                              @Param("moduleCodeList") List<String> moduleCodeList);
}
