package com.digiwin.escloud.aiocmdb.asset.model;

import com.digiwin.escloud.aiocmdb.asset.utils.AssetNoProduceType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AssetCategoryCodingRuleSimple {

    @ApiModelProperty("规则编号")
    private AssetNoProduceType ruleNumber;

    @ApiModelProperty("规则设定值")
    private String ruleSettingValue;

    private Long assetCategoryId; //資產類別id
    private String modelCode;
    private String sinkName;
    private long classificationId = 0;

    private String mainCode = ""; // 大類別的編號
    private String ruleKey = ""; // 編碼規則識別碼, 主要用來識別, 是否為相同的資產編碼規則

    // 由程式填入
    private String currentFlowNumber = "";

    public String getCurrentFlowNumber() {
        // 去掉所有的 '-', 且規則的文字, 不能有 '-', 前端需卡控掉
        return currentFlowNumber.replaceAll("-", "");
    }

    public String toString() {
        return "AssetCategoryCodingRuleSimple{" +
                "ruleNumber=" + ruleNumber +
                ", ruleSettingValue='" + ruleSettingValue + '\'' +
                ", assetCategoryId=" + assetCategoryId +
                ", modelCode='" + modelCode + '\'' +
                ", sinkName='" + sinkName + '\'' +
                ", classificationId=" + classificationId +
                ", mainCode='" + mainCode + '\'' +
                ", currentFlowNumber='" + currentFlowNumber + '\'' +
                '}';
    }

}
