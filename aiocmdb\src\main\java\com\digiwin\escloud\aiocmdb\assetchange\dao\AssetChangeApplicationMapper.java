package com.digiwin.escloud.aiocmdb.assetchange.dao;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 资产变更申请单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Mapper
public interface AssetChangeApplicationMapper {

    /**
     * 插入资产变更申请单
     *
     * @param application 资产变更申请单
     * @return 影响行数
     */
    int insert(AssetChangeApplication application);

    /**
     * 更新资产变更申请单
     *
     * @param application 资产变更申请单
     * @return 影响行数
     */
    int updateById(AssetChangeApplication application);

    /**
     * 根据ID查询资产变更申请单
     *
     * @param id 主键ID
     * @return 资产变更申请单
     */
    AssetChangeApplication selectById(@Param("id") Long id);

    /**
     * 根据申请单编号查询资产变更申请单
     *
     * @param applicationNumber 申请单编号
     * @return 资产变更申请单
     */
    AssetChangeApplication selectByApplicationNumber(@Param("applicationNumber") String applicationNumber);

    /**
     * 检查申请单编号是否存在
     *
     * @param applicationNumber 申请单编号
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在的数量
     */
    int checkApplicationNumberExists(@Param("applicationNumber") String applicationNumber, @Param("excludeId") Long excludeId);

    /**
     * 根据ID删除资产变更申请单
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
}
