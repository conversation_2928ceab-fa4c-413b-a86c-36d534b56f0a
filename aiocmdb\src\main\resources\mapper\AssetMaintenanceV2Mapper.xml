<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.asset.dao.AssetMaintenanceV2Mapper">

    <resultMap id="AssetRelatedCategoryClassificationResultMap" type="com.digiwin.escloud.aiocmdb.asset.model.AssetRelatedCategoryClassification">
        <result property="classificationName" column="classificationName"/>
        <result property="classificationId" column="classificationId"/>
        <result property="classificationIconUrl" column="classificationIconUrl"/>
        <collection property="listAssetCategory" ofType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategorySimple">
            <result property="classificationId" column="classificationId"/>
            <result property="categoryNumber" column="categoryNumber"/>
            <result property="categoryName" column="categoryName"/>
            <result property="modelCode" column="modelCode"/>
            <result property="sinkName" column="sinkName"/>
            <result property="categoryIconUrl" column="categoryIconUrl"/>
        </collection>
    </resultMap>

    <resultMap id="AssetCategoryCodingRuleSimpleResultMap"
               type="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSimple">
        <result property="ruleNumber" column="ruleNumber"/>
        <result property="ruleSettingValue" column="ruleSettingValue"/>
        <result property="modelCode" column="modelCode"/>
        <result property="sinkName" column="sinkName"/>
        <result property="classificationId" column="classificationId"/>
    </resultMap>


    <select id="selectAssetCategory" resultMap="AssetRelatedCategoryClassificationResultMap">
        select acc.categoryName as classificationName, ac.classificationId, ifnull(acc.iconUrl, '') as classificationIconUrl
               ,ac.categoryNumber, ac.categoryName, ac.modelCode, ac.sinkName, ifnull(ac.iconUrl, '') as categoryIconUrl
        from asset_category ac
        inner join asset_category_classification acc on acc.id = ac.classificationId
        WHERE 1=1
        <if test="categoryNumbers != null and categoryNumbers.size() > 0">
            <foreach collection="categoryNumbers" item="number" separator=" OR " open=" and (" close=")">
                ac.categoryNumber = #{number}
            </foreach>
        </if>
        <if test="modelCodes != null and modelCodes.size() > 0">
            <foreach collection="modelCodes" item="code" separator=" OR " open=" and (" close=")">
                ac.modelCode = #{code}
            </foreach>
        </if>
        <if test="sinkNames != null and sinkNames.size() > 0">
            <foreach collection="sinkNames" item="sinkName" separator=" OR " open=" and (" close=")">
                ac.sinkName = #{sinkName}
            </foreach>
        </if>
    </select>

    <select id="selectAssetCodingRuleSetting" resultMap="AssetCategoryCodingRuleSimpleResultMap">
        select
            ac.id as assetCategoryId, accr.ruleNumber, accrsr.ruleSettingValue,
            ac.modelCode, ac.sinkName, ac.classificationId,
            ruleKeyGroup.categoryClassificationCode as mainCode, /*大類別的編號*/
            concat(ruleKeyGroup.categoryClassificationCode, ruleKeyGroup.ruleKey) as ruleKey /*資產編碼規則 group key */
        from asset_category ac
                 inner join asset_category_coding_rule_setting_result accrsr on ac.id = accrsr.objId
                 inner join asset_category_coding_rule accr on accr.id = accrsr.ruleId
                 inner join (
            select
                categoryClassificationCode, objId,
                GROUP_CONCAT(ruleKey ORDER BY ruleId asc SEPARATOR '_') as ruleKey,
                GROUP_CONCAT(ruleId SEPARATOR '_') as ruleId
            from (
                     select categoryClassificationCode, objId, ruleId, concat(ruleId, ruleSettingValue) as ruleKey
                     from asset_category_coding_rule_setting_result
                     where 1=1
                       and categoryClassificationCode is not null
                       and length(categoryClassificationCode) > 0
                 ) a
            group by categoryClassificationCode, objId
        ) ruleKeyGroup on ruleKeyGroup.objId = ac.id
        where 1=1
          <if test="modelCode !=null and modelCode!=''">
              and ac.modelCode = #{modelCode}
          </if>
          <if test="sinkName !=null and sinkName!=''">
              and ac.sinkName = #{sinkName}
          </if>
          <if test="ruleKey !=null and ruleKey!=''">
              and concat(ruleKeyGroup.categoryClassificationCode, ruleKeyGroup.ruleKey) = #{ruleKey}
          </if>
    </select>

    <select id="selectAssetPrefixCoding" resultType="java.lang.String">
        SELECT DISTINCT root.code
        FROM asset_category_classification root
        WHERE root.parentId IS NULL
          AND EXISTS (
            SELECT 1
            FROM asset_category_classification c1
                     LEFT JOIN asset_category_classification c2 ON c1.parentId = c2.id
                     LEFT JOIN asset_category_classification c3 ON c2.parentId = c3.id
                     LEFT JOIN asset_category_classification c4 ON c3.parentId = c4.id
                     LEFT JOIN asset_category_classification c5 ON c4.parentId = c5.id
            WHERE c1.id = #{classificationId}
              AND (c1.parentId IS NULL AND c1.id = root.id
                OR c2.parentId IS NULL AND c2.id = root.id
                OR c3.parentId IS NULL AND c3.id = root.id
                OR c4.parentId IS NULL AND c4.id = root.id
                OR c5.parentId IS NULL AND c5.id = root.id)
        );
    </select>

    <select id="selectAssetCategoryClassification" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetRelatedCategoryClassificationTree">
        select id as treeId,
               categoryName as treeName,
               ifnull(iconUrl, '') as treeIconUrl,
               parentId as treeParentId
        from asset_category_classification
    </select>
    
</mapper>