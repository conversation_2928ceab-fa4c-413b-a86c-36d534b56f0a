package com.digiwin.escloud.userv2.chatheader.controller;

import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.userv2.chatheader.model.ChatHeaderSetting;
import com.digiwin.escloud.userv2.chatheader.service.ISettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date: 2025-08-11
 * @Description
 */
@Api(value = "智能客服视窗头部说明", tags = {"智能客服视窗头部说明接口"})
@RestController
@RequestMapping("/api/chat/header")
@Slf4j
public class SettingController extends ControllerBase {

    @Resource
    private ISettingService service;

    @ApiOperation(value = "获取设定列表")
    @GetMapping(value = "/setting/list")
    public BaseResponse getChatHeaderSettings(@ApiParam(required = false, value = "serviceRegion") @RequestParam(value = "serviceRegion", required = false) String serviceRegion,
                                      @ApiParam(required = false, value = "产品代号") @RequestParam(value = "productCode", required = false) String productCode,
                                      @ApiParam(required = true, value = "语言别") @RequestParam(value = "lang", required = true) String lang,
                                      @ApiParam(required = false, value = "页码") @RequestParam(value = "page", required = false, defaultValue = "1") int page,
                                      @ApiParam(required = false, value = "条数") @RequestParam(value = "size", required = false, defaultValue = "0") int size) {
        return getBaseResponse(() -> service.getChatHeaderSettings(serviceRegion, productCode, lang, page, size),
                false, false, null);
    }

    @ApiOperation(value = "获取设定")
    @GetMapping(value = "/setting")
    public BaseResponse getChatHeaderSetting(@ApiParam(required = false, value = "serviceRegion") @RequestParam(value = "serviceRegion", required = false) String serviceRegion,
                                              @ApiParam(required = false, value = "产品代号") @RequestParam(value = "productCode", required = false) String productCode,
                                              @ApiParam(required = true, value = "语言别") @RequestParam(value = "lang", required = true) String lang) {
        return getBaseResponse(() -> service.getChatHeaderSetting(serviceRegion, productCode, lang),
                false, false, null);
    }

    @ApiOperation(value = "获取明细")
    @GetMapping(value = "/setting/{id}/detail")
    public BaseResponse getChatHeaderSettingDetail(@ApiParam(required = true, value = "id") @PathVariable(value = "id", required = true) long id) {
        return getBaseResponse(() -> service.getChatHeaderSettingDetail(id),
                false, false, null);
    }

    @ApiOperation(value = "保存")
    @PostMapping(value = "/setting")
    public BaseResponse saveChatHeaderSetting(@ApiParam(required = true, value = "") @RequestBody ChatHeaderSetting chatHeader) {
        return getBaseResponse(() -> service.saveChatHeaderSetting(chatHeader),
                false, false, null);
    }

    @ApiOperation(value = "编辑")
    @PutMapping(value = "/setting")
    public BaseResponse updateChatHeaderSetting(@ApiParam(required = true, value = "") @RequestBody ChatHeaderSetting chatHeader) {
        return getBaseResponse(() -> service.updateChatHeaderSetting(chatHeader),
                false, false, null);
    }

    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/setting/{id}")
    public BaseResponse deleteChatHeaderSetting(@ApiParam(required = true, value = "id") @PathVariable(value = "id", required = true) long id) {
        return getBaseResponse(() -> service.deleteChatHeaderSetting(id),
                false, false, null);
    }

}
