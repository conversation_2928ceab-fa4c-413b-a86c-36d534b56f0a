package com.digiwin.escloud.aiocmdb.asset.service;

import com.digiwin.escloud.aiocmdb.asset.model.*;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 资产类别分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface IAssetCategoryService  {

    // AssetCategoryClassification 相关方法
    BaseResponse saveAssetCategoryClassification(AssetCategoryClassification classification);

    BaseResponse updateAssetCategoryClassification(AssetCategoryClassification classification);

    ResponseBase deleteAssetCategoryClassification(Long id);

    BaseResponse getAssetCategoryClassificationList(String categoryType);

    // AssetCategory 相关方法
    BaseResponse saveAssetCategory(AssetCategory category);

    BaseResponse updateAssetCategory(AssetCategory category);

    ResponseBase updateAssetCategoryStatus(Long id, String status);

    ResponseBase deleteAssetCategory(Long id);

    BaseResponse getAssetCategoryList(AssetCategoryQueryParam queryParam);

    // CmdbModelDataFieldRelationMapping 相关方法
    BaseResponse saveCmdbModelDataFieldRelationMapping(List<CmdbModelDataFieldRelationMapping> mappingList,String targetModelCode);

    ResponseBase<List<CmdbModelDataFieldRelationMapping>> getCmdbModelDataFieldRelationMappingList(String targetModelCode);

    // AssetCategoryCodingRule 相关方法
    ResponseBase getAllAssetCategoryCodingRule();

    ResponseBase batchUpsertAiopsCollectSink(List<AiopsCollectSink> sinkList);

    ResponseBase getAllModel(String modelGroupCode, String modelCode);

    /**
     * 根据modelCode填充模型显示字段配置
     *
     * @param modelCode 模型编码
     * @return 操作结果
     */
    ResponseBase populateModelShowFields(String modelCode,Long sid);

    /**
     * 根据用户ID删除用户字段配置
     *
     * @param userId    用户ID
     * @param modelCode
     * @return 操作结果
     */
    ResponseBase deleteCmdbModelShowFieldUser(String userId,String modelCode);

    ResponseBase deleteDsProcessByCategoryId(Long processId);

    /**
     * 支持传入查询列和过滤条件，因为这是union all所以查询列和过滤条件列每个表都需要有
     *
     * @param request 查询请求参数
     * @return 查询结果
     */
    ResponseBase<List<Map<String, Object>>> queryStarRocksData(StarRocksQueryRequest request);

    /**
     * 根据aiopsItemList查询StarRocks数据的包装方法
     * 先根据aiopsItemList查询modelCodeList，然后调用queryStarRocksData方法
     *
     * @param request 查询请求参数，包含aiopsItemList
     * @return 查询结果
     */
    ResponseBase<List<Map<String, Object>>> queryStarRocksDataByAiopsItems(StarRocksQueryRequest request);

    /**
     * 修复asset_category_coding_rule_setting_result表中categoryClassificationCode为空的数据
     * 通过关联查询找到最顶级的分类代码并更新
     *
     * @return 修复结果
     */
    ResponseBase fixEmptyCategoryClassificationCode();

    /**
     * 更新资产类别的流程ID
     * @param id 资产类别ID
     * @param processId 流程ID
     * @return 更新结果
     */
    ResponseBase updateAssetCategoryProcessId(Long id, Long processId);
}
