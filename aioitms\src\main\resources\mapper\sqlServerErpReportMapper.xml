<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aioitms.report.dao.SqlServerErpReportMapper">
    <select id="getProductCode" resultType="java.util.Map">
        SELECT AT002
        FROM
        (SELECT s1.AT002, MAX(s1.AT004) AS AT004
        FROM `${escloudDBName}`.serat s1
        INNER JOIN
        (SELECT AT002,
        MAX(AT010) AS maxAT010
        FROM `${escloudDBName}`.serat
        WHERE AT000 = #{customerCode}
        <if test="productCodeList != null and productCodeList.size() > 0">
            <foreach collection="productCodeList" item="item" open=" AND AT002 IN(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY AT002) s2
        ON s1.AT002 = s2.AT002 AND s1.AT010 = s2.maxAT010
        WHERE s1.AT000 = #{customerCode}
        <if test="productCodeList != null and productCodeList.size() > 0">
            <foreach collection="productCodeList" item="item" open=" AND s1.AT002 IN(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY s1.AT002) serat_productCode;
    </select>

    <select id="getLicensesCount" resultType="java.lang.Integer">
        SELECT CONVERT(MAX(s1.AT004), UNSIGNED) AS AT004
        FROM `${escloudDBName}`.serat s1
                 INNER JOIN (SELECT AT002, MAX(AT010) AS maxAT010
                             FROM `${escloudDBName}`.serat
                             WHERE AT000 = #{customerCode}
                               AND AT002 = #{productCode}
                             GROUP BY AT002) s2 ON s1.AT002 = s2.AT002 AND s1.AT010 = s2.maxAT010
        WHERE s1.AT000 = #{customerCode}
          AND s1.AT002 = #{productCode}
        GROUP BY s1.AT002;
    </select>

    <select id="getDgwModules" resultType="java.util.Map">
        SELECT name AS title, AT003 AS code
        FROM (SELECT AT003
              FROM `${escloudDBName}`.serat
              WHERE AT000 = #{customerCode}
                AND AT002 = #{productCode}
                AND AT003 != 'ALL'
              GROUP BY AT003) s
                 LEFT JOIN `${escloudDBName}`.erp_product_module epm ON s.AT003 = epm.moduleCode AND epm.productCode = #{productCode}
        WHERE name IS NOT NULL;
    </select>

    <select id="matchErpModules" resultType="java.util.Map">
        SELECT name AS title, moduleCode AS code
        FROM `${escloudDBName}`.erp_product_module
        WHERE productCode = #{productCode}
        <if test="moduleCodeList != null and moduleCodeList.size() > 0">
            <foreach collection="moduleCodeList" item="item" open=" AND moduleCode IN(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>