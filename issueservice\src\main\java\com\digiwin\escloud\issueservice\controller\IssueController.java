package com.digiwin.escloud.issueservice.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.issueservice.model.*;
import com.digiwin.escloud.issueservice.services.IIssueDetailService;
import com.digiwin.escloud.issueservice.services.IIssueService;
import com.digiwin.escloud.issueservice.services.IUserService;
import com.digiwin.escloud.issueservice.t.model.common.BaseResponse;
import com.digiwin.escloud.issueservice.t.model.common.PageInfo;
import com.digiwin.escloud.issueservice.t.model.common.ResponseStatus;
import com.digiwin.escloud.issueservice.utils.DgwFileUtils;
import com.google.gson.*;
import com.mongodb.gridfs.GridFSDBFile;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

//import org.springframework.util.StringUtils;

/**
 * Created by zhanghje on 2018-01-02.
 */
@RefreshScope
@RestController
public class IssueController {
    private static Log log = LogFactory.getLog(IssueController.class);

    @Value("${digiwin.visitor.public.user.service.code}")
    private String visitorServiceCode;
    @Value("${digiwin.visitor.public.user.userId}")
    private String userId;

    @Autowired
    private IIssueService issueService;
    @Autowired
    private IIssueDetailService issueDetailService;
    @Autowired
    private IUserService userService;

    /**
     * 访客提单，2021-07-27 如果body里面传了userSid serviceCode 以body为准，没有传取服务云默认的设置
     * 访客提单，2021-07-27 如果header里面传了eid 以header为准，没有传取服务云默认的设置
     *
     * @param issue
     * @return
     */
    @ApiOperation(value = "A1联系我们 访客提交案件")
    @PostMapping(value = "/submitIssueForVisitor")
    public IssueSubmitResponse SubmitIssue(@RequestBody Issue issue) {
        IssueSubmitResponse issueSubmitResponse = new IssueSubmitResponse();
        try {

            issue.setIssueType(IssueType.Issue.toString());
            issue.setIssueSubmitMode(IssueSubmitMode.Normal.toString());
            //2021-07-27 如果body里面传了serviceCode 以body为准，没有传取服务云默认的设置
            if (StringUtils.isEmpty(issue.getServiceCode())) {
                issue.setServiceCode(visitorServiceCode);
            }

            //2021-07-27 如果body里面传了userSid 以body为准，没有传取服务云默认的设置
            if (StringUtils.isEmpty(issue.getUserId())) {
                issue.setUserId(userId);//访客
            }
            if (issue.getUserContact() != null) {
                issue.getUserContact().setUserId(issue.getUserId());
            }
            issue.setSubmitWay(SubmitWay.CONTACTUS.getSymbol());
            //1.组织issueCode
            int res = issueService.SubmitIssue(issue);
            if (res == 0) {
                Long issueId = issue.getIssueId();
                System.out.print("Before do SubmitIssueAsync" + issueId);
                issueService.SubmitIssueAsync(issue, issue.getIssueStatus(), false);
                System.out.print("Before do SubmitIssueAsync" + issueId);

                issueSubmitResponse.setCode("0");
                issueSubmitResponse.setData(Long.toString(issueId));
            } else {
                issueSubmitResponse.setCode(Integer.toString(res));
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("问题提交失败");
            }
        } catch (Exception ex) {
            issueSubmitResponse.setCode("1");
            issueSubmitResponse.setData("-1");
            issueSubmitResponse.setErrMsg("问题提交失败：" + ex.getMessage());
            log.error(ex);
            ex.printStackTrace();
        }
        return issueSubmitResponse;
    }

    @ApiOperation(value = "验证edr的rawId是否立案（支持批量）")
    @PostMapping(value = "/edr/event/raw/issue")
    public com.digiwin.escloud.common.model.ResponseBase checkEdrEventIssue(@ApiParam(value = "查询参数", required = true) @RequestBody List<Map<String, String>> list) {
        try {
            return issueService.checkEdrEventIssue(list);
        } catch (Exception ex) {
            log.error("checkEdrEventIssue", ex);
            return com.digiwin.escloud.common.model.ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "根据事件id查询案件列表")
    @GetMapping(value = "/api/event/issues")
    public BaseResponse getEventIssues(@RequestParam("pageNum") int pageNum,
                                       @RequestParam("pageSize") int pageSize,
                                       @RequestParam String eventId) {
        return issueService.getEventIssues(pageNum, pageSize, eventId);
    }

    @RequestMapping(value = "/api/issuesnew", method = RequestMethod.POST)
    public IssueSubmitResponse SubmitIssueNew(@RequestParam(value = "userId", required = false) String userId,
                                              @RequestParam(value = "way", required = false, defaultValue = "") String way,
                                              @RequestParam(value = "issuestr") String issuestr,
                                              @RequestParam(value = "file") MultipartFile[] multipartFiles) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        IssueSubmitResponse issueSubmitResponse = new IssueSubmitResponse();
        try {
            Issue issue = new Issue();
            if (way.equals("SCP") || way.equals("ITMS") || way.equals("ITMS_Service")) {//way為ITMS表示為IT智能運維平台(客戶端)中的立案   way為ITMS_Service表示為[運維平台(客服端)中的立案
                Gson gson = new Gson();
                issue = gson.fromJson(issuestr, Issue.class);
                //派別產品線是否為科維，是的話檢查雲管家版本是否>3.3.6.0429，是的話檢查合約日期，不在合約日期內就回饋告知無法提交案件
                if ("15".equals(issue.getProductCode())) {
                    //舊版本雲管家之科維產品線允許提交案件
                    if (issue.getSubmitWay() != null) {
                        String submitVersionStr = java.util.Optional.ofNullable(issue.getSubmitWay()).orElse("0");
                        String submitWay = issue.getSubmitWay();
                        long submitVersion = 0;
                        if (org.apache.commons.lang.StringUtils.isNotBlank(submitWay)) {
                            if (submitWay.equals("ITMS") == true || submitWay.equals("ITMS_Service") == true) {
                                submitVersion = 0;
                            } else {
                                int startIndex = submitVersionStr.indexOf("_");
                                String versionString = submitVersionStr.substring(startIndex, submitWay.length());
                                submitVersion = tryParse(versionString);
                            }
                        }
                        if (submitVersion > 3360429L || issue.getSubmitWay().equals("ITMS") || issue.getSubmitWay().equals("ITMS_Service")) {
                            boolean isValidContract = issueService.CheckCustomerContract(issue.getServiceCode(), issue.getProductCode());
                            if (isValidContract == false) {
                                //表示合約日期不在範圍內，所以不允許提交案件
                                issueSubmitResponse.setCode("2");
                                issueSubmitResponse.setData("-1");
                                issueSubmitResponse.setErrMsg("不在合約日期範圍內，故無法提交案件");
                                return issueSubmitResponse;
                            }
                        }
                    }
                }
                if (way.equals("ITMS") || way.equals("ITMS_Service")) {
                    //20200629 由客戶mail找出其UserId若找不到就不管
                    String newUserId = issueService.findUserIdbyMail(issue.getUserContact().getEmail(), issue.getServiceCode());
                    if (StringUtils.isNotBlank(newUserId)) { //huly: 修复漏洞/bug StringUtils.isNotBlank(newUserId)
                        issue.setUserId(newUserId);
                        UserContact uc = issue.getUserContact();
                        uc.setUserId(newUserId);
                        issue.setUserContact(uc);
                    }
                }
            } else {
                Gson customGson = new GsonBuilder().registerTypeHierarchyAdapter(byte[].class,
                        new ByteArrayToBase64TypeAdapter()).create();
                issue = customGson.fromJson(issuestr, Issue.class);
            }
            //20191224 huly:验证是否走审核,并且是否设置审核人，易聊不用走审核
            boolean isCheck = issueService.checkConfirm(issue);
            if (isCheck) {
                issueSubmitResponse.setCode("1");
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("请先设置案件审核人");
                return issueSubmitResponse;
            }
            String issueStaus = "";
            if (org.apache.commons.lang.StringUtils.isNotEmpty(issue.getIssueStatus())) {
//                if(issue.getIssueStatus().equals("Y")) {
                issueStaus = issue.getIssueStatus();//mis工单提交案件，issueStatus = ‘N’
//                }
            }
            //校验文件扩展名是否符合规范，不可上傳這些副檔名.js、.jar、.bat、.cpl、.scr、.com、.pif、.vbs、.exe、.cmd、.reg、.lnk、.hta
            if(DgwFileUtils.checkFileName(multipartFiles)) {
                issueSubmitResponse.setCode("1");
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("问题提交失败：附件不符合要求");
                return issueSubmitResponse;
            }
            int res = issueService.SubmitIssue(issue); //這邊還沒有 crmid
            if (res == 0) {
                issueSubmitResponse.setCode("0");
                Long issueId = issue.getIssueId();
                issueSubmitResponse.setData(Long.toString(issueId));
                System.out.print("Before do SubmitIssueAsync" + issueId);
                dealMultipartFiles(multipartFiles, issue);
                issueService.SubmitIssueAsync(issue, issueStaus, true); // 這邊才會產生 CRMID
                System.out.print("Before do SubmitIssueAsync" + issueId);
            } else {
                issueSubmitResponse.setCode(Integer.toString(res));
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("问题提交失败");
            }
        } catch (Exception ex) {
            issueSubmitResponse.setCode("1");
            issueSubmitResponse.setData("-1");
            issueSubmitResponse.setErrMsg("问题提交失败：" + ex.getMessage());
            log.error(ex);
            ex.printStackTrace();
        }
        return issueSubmitResponse;
    }

    /**
     * 授權自動化 ，提供下面api给SFT、小助手调用，会自动产生出货信息、序号组、案件等
     *
     * @param maderWorkNo     鼎新員工編號(誰製作序號)
     * @param businessWorkNo  业务人员工号
     * @param secretaryWorkNo 秘书工号
     * @param serviceCode     客服代號
     * @param productCode     產品線
     * @param shipmentNum     出貨單號
     * @param orderName       訂購人姓名
     * @param description     案件描述(加購或加人數等內容)
     * @param way             产生案件的方式
     * @param multipartFile   ini檔案(序號檔)
     * @param updateType      上传类型 uploadService 更新作业 | AUTHINSTALL 授权安装 |  SFT_AUTHINSTALL  加购序号授权|  SFT_RE_AUTHINSTALL 序号重查
     * @return
     */
    @RequestMapping(value = "/api/authorize/auto/issuesnew", method = RequestMethod.POST)
    public IssueSubmitResponse SubmitAutoIssue(@RequestParam(value = "maderWorkNo", required = false) String maderWorkNo,
                                               @RequestParam(value = "businessWorkNo", required = false) String businessWorkNo,
                                               @RequestParam(value = "secretaryWorkNo", required = false) String secretaryWorkNo,
                                               @RequestParam(value = "serviceCode", required = false) String serviceCode,
                                               @RequestParam(value = "productCode", required = false) String productCode,
                                               @RequestParam(value = "shipmentNum", required = false) String shipmentNum,
                                               @RequestParam(value = "orderName", required = false) String orderName,
                                               @RequestParam(value = "description", required = false) String description,
                                               @RequestParam(value = "way", required = false, defaultValue = "SHIPMENTAUTHORIZE") String way,
                                               @RequestParam(value = "file", required = false) MultipartFile multipartFile,
                                               @RequestParam(value = "updateType", required = false, defaultValue = "AUTHINSTALL") String updateType,
                                               @RequestParam(value = "serial", required = false) String serial) {

        IssueSubmitResponse issueSubmitResponse = new IssueSubmitResponse();
        try {
            System.out.println("description: " + description);
            //校验文件扩展名是否符合规范，不可上傳這些副檔名.js、.jar、.bat、.cpl、.scr、.com、.pif、.vbs、.exe、.cmd、.reg、.lnk、.hta
            if(multipartFile != null){
                if(DgwFileUtils.checkFileName(new MultipartFile[]{multipartFile})){
                    issueSubmitResponse.setCode("1");
                    issueSubmitResponse.setData("-1");
                    issueSubmitResponse.setErrMsg("问题提交失败：附件不符合要求");
                    return issueSubmitResponse;
                }
            }
            
            Issue issue = new Issue();

            issue.setServiceCode(serviceCode);
            switch (updateType) {
                case "AUTHINSTALL": {
                    issue.setProductCode(productCode);
                    //新增出货信息，上传授权档案
                    long shipmentSid = issueService.saveShipment(serviceCode, productCode, shipmentNum, orderName, multipartFile);
                    issue.setShipmentSid(shipmentSid);
                    issue.setIssueDescription("【客服上傳新產品序號，請您依序執行更新序號->啟用序號->同意開通，謝謝】\n" +
                            "出貨單號：" + shipmentNum + "\n" +
                            description + "\n" +
                            "注意事項：在執行『啟用序號』程序時，請確認是否無人使用ERP，將於點選『啟用序號』後，強制踢除人員離開，否則請點選『取消』，另行安排時間執行，如未執行『啟用序號』，將無法啟用更新後的授權模組或人數。");
                    break;
                }
                case "SFT_AUTHINSTALL": {
                    issue.setProductCode(issueService.changeSFTProductCode(serviceCode)); //SFT产品线会转成02 或者 10 产品线提单
                    //新增出货信息，保存序号组
                    long shipmentSid = issueService.saveShipment_SFT(serviceCode, productCode, shipmentNum, orderName, serial);
                    issue.setShipmentSid(shipmentSid);
                    issue.setIssueDescription("【客服上傳SFT新產品序號，請您依序執行更新序號->同意開通，謝謝】\n" +
                            "出貨單號：" + shipmentNum + "\n" +
                            description);
                    break;
                }
                case "SFT_RE_AUTHINSTALL": {
                    issue.setProductCode(issueService.changeSFTProductCode(serviceCode));//SFT产品线会转成02 或者 10 产品线提单
                    issue.setShipmentSid(0L);
                    issue.setIssueDescription("【SFT重新授權案件，請等待審核同意後，請您依序執行:申請重查序號->更新序號->同意開通，謝謝】\n" +
                            description);
                    break;
                }
                default:
            }
            UserPersonalInfo userPersonalInfo = issueService.getUserInfoForShipment(serviceCode, orderName);
            // 案件單頭之userId存空字串
            issue.setUserId("");
            //issue.setUserId((userPersonalInfo != null && StringUtils.isNotBlank(userPersonalInfo.getUserId())) ? userPersonalInfo.getUserId() : "");
            issue.setIssueSubmitMode(way);
            issue.setSubmitWay(way);
            issue.setMaderWorkNo(maderWorkNo);//制作人员工号
            issue.setBusinessWorkNo(businessWorkNo);//业务人员工号
            issue.setSecretaryWorkNo(secretaryWorkNo);//秘书工号
            issue.setUpdateType(updateType);
            issue.setIssueType(IssueType.Issue.toString());
            UserContact contact = new UserContact();
            contact.setUserId(issue.getUserId());
            //案件單頭之email存*******************
            contact.setEmail("<EMAIL>");
            contact.setPhone01((userPersonalInfo != null && StringUtils.isNotBlank(userPersonalInfo.getTelephone())) ? userPersonalInfo.getTelephone() : "");
            contact.setName(orderName);

            issue.setUserContact(contact);
            IssueCasedetail issueCasedetail = new IssueCasedetail();
            issue.setIssueCasedetail(issueCasedetail);
            int res = issueService.SubmitIssue(issue); //這邊還沒有 crmid
            if (res == 0) {
                issueSubmitResponse.setCode("0");
                Long issueId = issue.getIssueId();
                BaseResponse customerInfoBR = issueService.getIssueCustomerInfo(issue.getIssueId());
                Map<String, String> result = new HashMap<>();
                result.put("crmId", IssueCodeRoleStart.ESN.toString() + String.format("%09d", issueId));
                result.put("serviceCode", serviceCode);
                if (ResponseStatus.OK.getCode() == customerInfoBR.getStatus()) {
                    CustomerServiceInfo customerServiceInfo = JSONObject.parseObject(JSONObject.toJSONString(customerInfoBR.getResponse()), CustomerServiceInfo.class);
                    result.put("customerName", customerServiceInfo.getCustomerName());
                }
                issueSubmitResponse.setData(JSON.toJSONString(result));
                System.out.print("Before do SubmitIssueAsync" + issueId);
                //聯繫人資訊還是存原本查到的email
                contact.setEmail((userPersonalInfo != null && StringUtils.isNotBlank(userPersonalInfo.getEmail())) ? userPersonalInfo.getEmail() : "<EMAIL>");
                issue.setUserContact(contact);
                issueService.SubmitIssueAsync(issue, "", true);
                System.out.print("after do SubmitIssueAsync" + issueId);
            } else {
                issueSubmitResponse.setCode(Integer.toString(res));
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("问题提交失败");
            }

        } catch (Exception ex) {
            issueSubmitResponse.setCode("1");
            issueSubmitResponse.setData("-1");
            issueSubmitResponse.setErrMsg("WF/SM/SFT序號授權自動化失败：" + ex.getMessage());
            log.error(ex);
            ex.printStackTrace();
        }
        return issueSubmitResponse;
    }

    public void dealMultipartFiles(MultipartFile[] multipartFiles, Issue issue) {
        ////20200214 huly:网页版，附件是传到multipartFiles里的;网页版不能将字节流才能到json传到后台
        try {
            if (!StringUtils.isEmpty(issue.getSubmitWay()) && (issue.getSubmitWay().equals("WEB") || issue.getSubmitWay().equals("ITMS") || issue.getSubmitWay().equals("ITMS_Service") || issue.getSubmitWay().equals("EASY_TALK") || issue.getSubmitWay().equals("SCB_DT") || issue.getSubmitWay().startsWith("AIOSSM"))) {
                if (multipartFiles != null && multipartFiles.length != 0) {
                    List<IssueAttachment> issueAttachments = new ArrayList<>();

                    int sequeeNum = 0;
                    //遍历并保存文件
                    for (MultipartFile file : multipartFiles) {
                        IssueAttachment issueAttachment = new IssueAttachment();

                        String name = file.getOriginalFilename();
                        log.info("++++++++++++++++++++++" + name);
                        //因为可能会有windows完整路径上传上来，所以将filename做个转换
                        String endFilename = name.replaceAll("\\\\", "").replace(":", "").replace("%", "");

                        issueAttachment.setAttachment(file.getBytes());
                        issueAttachment.setFileType(endFilename.substring(endFilename.lastIndexOf(".")));
                        issueAttachment.setFileName(endFilename.substring(0, endFilename.lastIndexOf(".")));
                        issueAttachment.setSequeceNum(sequeeNum++);
                        issueAttachments.add(issueAttachment);

                    }

                    issue.setIssueAttachments(issueAttachments);
                }
            }
        } catch (Exception e) {
            log.error(e.toString());
        }

    }

    /**
     * 东南亚T的案件单独写api，因为同步逻辑不一样，立案时不需要同步，结案的时候才会同步
     *
     * @param userId
     * @param way
     * @param issuestr
     * @return
     */
    @RequestMapping(value = "/api/issuesForDNYT", method = RequestMethod.POST)
    public IssueSubmitResponse issuesForDNYT(@RequestParam(value = "userId", required = false) String userId,
                                             @RequestParam(value = "way", required = false, defaultValue = "") String way,
                                             @RequestBody String issuestr) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        IssueSubmitResponse issueSubmitResponse = new IssueSubmitResponse();
        try {
            Issue issue = new Issue();
            if (way.equals("SCP")) {
                Gson gson = new Gson();
                issue = gson.fromJson(issuestr, Issue.class);
            } else {
                Gson customGson = new GsonBuilder().registerTypeHierarchyAdapter(byte[].class,
                        new ByteArrayToBase64TypeAdapter()).create();
                issue = customGson.fromJson(issuestr, Issue.class);
            }
            //20191224 huly:验证是否走审核,并且是否设置审核人，易聊不用走审核
            boolean isCheck = issueService.checkConfirm(issue);
            if (isCheck) {
                issueSubmitResponse.setCode("1");
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("请先设置案件审核人");
                return issueSubmitResponse;
            }
            String issueStaus = "";
            if (org.apache.commons.lang.StringUtils.isNotEmpty(issue.getIssueStatus())) {
                if (issue.getIssueStatus().equals("Y")) {
                    issueStaus = issue.getIssueStatus();
                }
            }
            //校验文件扩展名是否符合规范，不可上傳這些副檔名.js、.jar、.bat、.cpl、.scr、.com、.pif、.vbs、.exe、.cmd、.reg、.lnk、.hta
            if(DgwFileUtils.checkFileName(issue.getIssueAttachments())) {
                issueSubmitResponse.setCode("1");
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("问题提交失败：附件不符合要求");
                return issueSubmitResponse;
            }
            int res = issueService.SubmitIssue(issue);
            if (res == 0) {
                issueSubmitResponse.setCode("0");
                Long issueId = issue.getIssueId();
                issueSubmitResponse.setData(Long.toString(issueId));
                System.out.print("Before do SubmitIssueAsync" + issueId);
                //东南亚T的案件，有个特殊的同步逻辑，立案的时候不同步
                //20230728柬南T不比照大陆服务中心T的处理，改同台湾T原案件提交逻辑 云管家需修改但卡在云管家需发版故先在后台判断处理
                if ("CN".equals(issue.getServiceRegion())) {
                    issueService.SubmitIssueAsyncNew(issue, issueStaus, true);
                } else {
                    issueService.SubmitIssueAsync(issue, issueStaus, true);
                }
                System.out.print("Before do SubmitIssueAsync" + issueId);
            } else {
                issueSubmitResponse.setCode(Integer.toString(res));
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("问题提交失败");
            }
        } catch (Exception ex) {
            issueSubmitResponse.setCode("1");
            issueSubmitResponse.setData("-1");
            issueSubmitResponse.setErrMsg("问题提交失败：" + ex.getMessage());
            log.error(ex);
        }
        return issueSubmitResponse;
    }

    @RequestMapping(value = "/api/issues", method = RequestMethod.POST)
    public IssueSubmitResponse SubmitIssue(@RequestParam(value = "userId", required = false) String userId,
                                           @RequestParam(value = "way", required = false, defaultValue = "") String way,
                                           @RequestBody String issuestr) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        IssueSubmitResponse issueSubmitResponse = new IssueSubmitResponse();
        try {
            Issue issue = new Issue();
            if (way.equals("SCP") || way.equals("ITMS") || way.equals("ITMS_Service")) {//way為ITMS表示為IT智能運維平台(客戶端)中的立案   way為ITMS_Service表示為[運維平台(客服端)中的立案
                Gson gson = new Gson();
                issue = gson.fromJson(issuestr, Issue.class);
                //派別產品線是否為科維，是的話檢查雲管家版本是否>3.3.6.0429，是的話檢查合約日期，不在合約日期內就回饋告知無法提交案件
                if ("15".equals(issue.getProductCode())) {
                    //舊版本雲管家之科維產品線允許提交案件
                    if (issue.getSubmitWay() != null) {
                        String submitVersionStr = java.util.Optional.ofNullable(issue.getSubmitWay()).orElse("0");
                        String submitWay = issue.getSubmitWay();
                        long submitVersion = 0;
                        if (org.apache.commons.lang.StringUtils.isNotBlank(submitWay)) {
                            if (submitWay.equals("ITMS") == true || submitWay.equals("ITMS_Service") == true) {
                                submitVersion = 0;
                            } else {
                                int startIndex = submitVersionStr.indexOf("_");
                                String versionString = submitVersionStr.substring(startIndex, submitWay.length());
                                submitVersion = tryParse(versionString);
                            }
                        }
                        if (submitVersion > 3360429L || issue.getSubmitWay().equals("ITMS") || issue.getSubmitWay().equals("ITMS_Service")) {
                            boolean isValidContract = issueService.CheckCustomerContract(issue.getServiceCode(), issue.getProductCode());
                            if (isValidContract == false) {
                                //表示合約日期不在範圍內，所以不允許提交案件
                                issueSubmitResponse.setCode("2");
                                issueSubmitResponse.setData("-1");
                                issueSubmitResponse.setErrMsg("不在合約日期範圍內，故無法提交案件");
                                return issueSubmitResponse;
                            }
                        }
                    }
                }
                if (way.equals("ITMS") || way.equals("ITMS_Service")) {
                    //20200629 由客戶mail找出其UserId若找不到就不管
                    String newUserId = issueService.findUserIdbyMail(issue.getUserContact().getEmail(), issue.getServiceCode());
                    if (StringUtils.isNotBlank(newUserId)) { //huly: 修复漏洞/bugStringUtils.isNotBlank(newUserId)
                        issue.setUserId(newUserId);
                        UserContact uc = issue.getUserContact();
                        uc.setUserId(newUserId);
                        issue.setUserContact(uc);
                    }
                }
            } else {
                Gson customGson = new GsonBuilder().registerTypeHierarchyAdapter(byte[].class,
                        new ByteArrayToBase64TypeAdapter()).create();
                issue = customGson.fromJson(issuestr, Issue.class);
            }
            //20191224 huly:验证是否走审核,并且是否设置审核人，易聊不用走审核
            boolean isCheck = issueService.checkConfirm(issue);
            if (isCheck) {
                issueSubmitResponse.setCode("1");
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("请先设置案件审核人");
                return issueSubmitResponse;
            }
            //提交案件都要校验合约
            boolean isValidContract = issueService.CheckCustomerContractExist(issue.getServiceCode(), issue.getProductCode());
            if (isValidContract == false) {
                //表示無合約，所以不允許提交案件
                issueSubmitResponse.setCode("2");
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("不在合约內，故无法提交案件");
                return issueSubmitResponse;
            }

            String issueStaus = "";
            if (org.apache.commons.lang.StringUtils.isNotEmpty(issue.getIssueStatus())) {
                //add by dukun 大陆客服要求客服易聊立案时，案件状态需为处理中N=======BEGIN=========
                if (issue.getSubmitWay().startsWith("SIM_"))
                    issueStaus = issue.getIssueStatus();
                //add by dukun 大陆客服要求客服易聊立案时，案件状态需为处理中N=======END=========

                if (issue.getIssueStatus().equals("Y")) {
                    issueStaus = issue.getIssueStatus();
                }
            }
            //校验文件扩展名是否符合规范，不可上傳這些副檔名.js、.jar、.bat、.cpl、.scr、.com、.pif、.vbs、.exe、.cmd、.reg、.lnk、.hta
            if(DgwFileUtils.checkFileName(issue.getIssueAttachments())) {
                issueSubmitResponse.setCode("1");
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("问题提交失败：附件不符合要求");
                return issueSubmitResponse;
            }
            int res = issueService.SubmitIssue(issue);
            if (res == 0) {
                issueSubmitResponse.setCode("0");
                Long issueId = issue.getIssueId();
                issueSubmitResponse.setData(Long.toString(issueId));
                System.out.print("Before do SubmitIssueAsync" + issueId);
                issueService.SubmitIssueAsync(issue, issueStaus, true);
                System.out.print("Before do SubmitIssueAsync" + issueId);
            } else {
                issueSubmitResponse.setCode(Integer.toString(res));
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("问题提交失败");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            issueSubmitResponse.setCode("1");
            issueSubmitResponse.setData("-1");
            issueSubmitResponse.setErrMsg("问题提交失败：" + ex.getMessage());
            log.error(ex);
        }
        return issueSubmitResponse;
    }


    @RequestMapping(value = "/api/issues/UpdateIssueProgressAndFile", method = RequestMethod.POST)
    public SimpleResponse UpdateIssueProgressAndFile(@RequestBody String issuestr) {
        SimpleResponse response = new SimpleResponse();
        try {
            Issue issue = new Issue();
            Gson gson = new Gson();
            issue = gson.fromJson(issuestr, Issue.class);
            // issue Progress 增加
            // 更新附件
            issueService.updateIssueProgressAndFile(issue);
            response.setCode("0");
            response.setData("關聯立案提交成功");
        } catch (Exception ex) {
            log.error(ex);
            response.setCode("1");
            response.setData("關聯立案提交失敗");
            response.setErrMsg(ex.getMessage());
        }
        return response;
    }

    private long tryParse(String str) {
        try {
            return Long.parseLong(str.replace(".", ""));
        } catch (Exception ex) {
            return 0L;
        }
    }

    @RequestMapping(value = "/api/callmeissues", method = RequestMethod.POST)
    public IssueSubmitResponse SubmitCallMeIssue(@RequestParam(value = "userId", required = false) String userId,
                                                 @RequestParam(value = "way", required = false, defaultValue = "") String way,
                                                 @RequestBody String issuestr) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        IssueSubmitResponse issueSubmitResponse = new IssueSubmitResponse();
        try {
            Issue issue = new Issue();
            if (way.equals("SCP")) {
                Gson gson = new Gson();
                issue = gson.fromJson(issuestr, Issue.class);
            } else {
                Gson customGson = new GsonBuilder().registerTypeHierarchyAdapter(byte[].class,
                        new ByteArrayToBase64TypeAdapter()).create();
                issue = customGson.fromJson(issuestr, Issue.class);
            }
            int res = issueService.SubmitIssue(issue);
            if (res == 0) {
                issueSubmitResponse.setCode("0");
                Long issueId = issue.getIssueId();
                issueSubmitResponse.setData(Long.toString(issueId));
                System.out.print("Before do SubmitIssueAsync" + issueId);
                issueService.SubmitIssueAsync(issue, "", false);
                System.out.print("After do SubmitIssueAsync" + issueId);
            } else {
                issueSubmitResponse.setCode(Integer.toString(res));
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("与我联系提交失败");
            }
        } catch (Exception ex) {
            issueSubmitResponse.setCode("1");
            issueSubmitResponse.setData("-1");
            issueSubmitResponse.setErrMsg("与我联系提交失败：" + ex.getMessage());
            log.error(ex);
        }
        return issueSubmitResponse;
    }

    @RequestMapping(value = "/api/couponissues", method = RequestMethod.POST)
    public IssueSubmitResponse SubmitCouponIssue(@RequestParam(value = "userId", required = false) String userId,
                                                 @RequestParam(value = "way", required = false, defaultValue = "") String way,
                                                 @RequestBody String issuestr) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        IssueSubmitResponse issueSubmitResponse = new IssueSubmitResponse();
        try {
            Issue issue = new Issue();
            if (way.equals("SCP")) {
                Gson gson = new Gson();
                issue = gson.fromJson(issuestr, Issue.class);
            } else {
                Gson customGson = new GsonBuilder().registerTypeHierarchyAdapter(byte[].class,
                        new ByteArrayToBase64TypeAdapter()).create();
                issue = customGson.fromJson(issuestr, Issue.class);
            }
            int res = issueService.SubmitIssue(issue);
            if (res == 0) {
                issueSubmitResponse.setCode("0");
                Long issueId = issue.getIssueId();
                issueSubmitResponse.setData(Long.toString(issueId));
                System.out.print("Before do SubmitIssueAsync " + issueId);
                issueService.SubmitIssueAsync(issue, "", false);
                System.out.print("After do SubmitIssueAsync " + issueId);
                System.out.print("Before do UseCouponAsync " + issueId + " " + issue.getCouponNo() + " " + issue.getUserId());
                ResponseBase couponUseRes = issueService.UseCouponAsync(issue.getCouponNo(), Long.toString(issueId), issue.getUserId());
                System.out.print("After do UseCouponAsync Code " + issueId);
            } else {
                issueSubmitResponse.setCode(Integer.toString(res));
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("使用券案件提交失败");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            issueSubmitResponse.setCode("1");
            issueSubmitResponse.setData("-1");
            issueSubmitResponse.setErrMsg("使用券案件提交失败：" + ex.getMessage());
            log.error(ex);
        }
        return issueSubmitResponse;
    }

    @GetMapping(value = "/v2/api/issues/{issueId}/getCrmIdByIssueId")
    public SimpleResponse getCrmIdByIssueId(@PathVariable("issueId") long issueId) {
        SimpleResponse response = new SimpleResponse();
        try {
            response.setCode("0");
            response.setData(issueService.getCrmIdByIssueId(issueId));

        } catch (Exception ex) {
            log.error(ex);
            response.setCode("1");
            response.setErrMsg(ex.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/api/issues/AdditionalExplanation", method = RequestMethod.POST)
    public IssueSubmitResponse SendAdditionalExplanation(@RequestParam("authuserid") String userId,
                                                         @RequestParam("authuserdept") String deptId,
                                                         @RequestBody String issuestr) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        IssueSubmitResponse issueSubmitResponse = new IssueSubmitResponse();
        Date date = new Date();
        String dt = sdf.format(date);
        try {
            Issue issue = new Issue();
            Gson gson = new Gson();
            issue = gson.fromJson(issuestr, Issue.class);
            issue.setSubmitTime(dt);
            issueSubmitResponse.setCode("0");
            issueSubmitResponse.setData(Long.toString(issue.getIssueId()));
            issueService.SubmitIssueAdditionalExplanationAsync(issue);
        } catch (Exception ex) {
            issueSubmitResponse.setCode("1");
            issueSubmitResponse.setData("-1");
            issueSubmitResponse.setErrMsg("问题补充说明提交失败：" + ex.getMessage());
            log.error(ex);
        }
        return issueSubmitResponse;
    }

    @RequestMapping(value = "/api/issues/AdditionalExplanation_new", method = RequestMethod.POST)
    public IssueSubmitResponse SendAdditionalExplanationNew(@RequestParam("authuserid") String userId,
                                                            @RequestBody String issuestr) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        IssueSubmitResponse issueSubmitResponse = new IssueSubmitResponse();
        Date date = new Date();
        String dt = sdf.format(date);
        try {
            Issue issue = new Issue();
            Issue old_issue = new Issue();
            Gson gson = new Gson();
            issue = gson.fromJson(issuestr, Issue.class);

            //校验文件扩展名是否符合规范，不可上傳這些副檔名.js、.jar、.bat、.cpl、.scr、.com、.pif、.vbs、.exe、.cmd、.reg、.lnk、.hta
            if(DgwFileUtils.checkFileName(issue.getIssueAttachments())) {
                issueSubmitResponse.setCode("1");
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("问题提交失败：附件不符合要求");
                return issueSubmitResponse;
            }
            old_issue = gson.fromJson(issuestr, Issue.class);
            issue.setSubmitTime(dt);
            issueSubmitResponse.setCode("0");
            issueSubmitResponse.setData(Long.toString(issue.getIssueId()));
            issueService.SubmitCallMeIssueAsync(issue, old_issue, userId);
        } catch (Exception ex) {
            issueSubmitResponse.setCode("1");
            issueSubmitResponse.setData("-1");
            issueSubmitResponse.setErrMsg("客服联络新流程提交失败：" + ex.getMessage());
            log.error(ex);
        }
        return issueSubmitResponse;
    }

    //將call-center的讀取狀態變更
    @RequestMapping(value = "/api/issues/UpdateAdditionalExplanationReadType", method = RequestMethod.POST)
    public SimpleResponse UpdateAdditionalExplanationReadType(@RequestParam("issueId") String issueId) {
        SimpleResponse response = new SimpleResponse();
        try {
            int result = issueService.UpdateAdditionalExplanationReadType(issueId);
            response.setCode("0");
            response.setData(Integer.toString(result));
        } catch (Exception ex) {
            log.error(ex);
            response.setCode("1");
            response.setErrMsg(ex.getMessage());
        }
        return response;
    }

    //允许客服修改问题描述
    @RequestMapping(value = "/api/issues/UpdateIssueDesc", method = RequestMethod.POST)
    public SimpleResponse UpdateIssueDescription(@RequestBody String issuestr) {
        SimpleResponse response = new SimpleResponse();
        try {
            IssueDetailInfo issue = new IssueDetailInfo();
            Gson gson = new Gson();
            issue = gson.fromJson(issuestr, IssueDetailInfo.class);
            int result = issueService.UpdateIssueDescription(issue);
            response.setCode("0");
            response.setData(Integer.toString(result));
        } catch (Exception ex) {
            log.error(ex);
            response.setCode("1");
            response.setErrMsg(ex.getMessage());
        }
        return response;
    }

    private static class ByteArrayToBase64TypeAdapter implements JsonSerializer<byte[]>, JsonDeserializer<byte[]> {
        public byte[] deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            try {
                return Base64.decodeBase64(json.getAsString());//.decode(json.getAsString());//, Base64..NO_WRAP
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }

        public JsonElement serialize(byte[] src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(Base64.encodeBase64String(src));//.encode(src));//.encodeToString(src));//, Base64.NO_WRAP
        }
    }

    @RequestMapping(value = "/api/issues", method = RequestMethod.GET)
    public IssuesGetResponse GetIssues(@RequestParam("authuserid") String userId,
                                       @RequestParam(value = "product", required = false, defaultValue = "") String productCode,
                                       @RequestParam(value = "status", required = false, defaultValue = "") String status,
                                       @RequestParam(value = "newReply", required = false, defaultValue = "") String newReply,
                                       @RequestParam(value = "myself", required = false, defaultValue = "") String myself,
                                       @RequestParam(value = "from", required = false, defaultValue = "") String from,
                                       @RequestParam("issueType") String issueType,
                                       @RequestParam("page") int page,
                                       @RequestParam("size") int size,
                                       @RequestParam("authuserdept") String department,
                                       @RequestParam(value = "queryId", required = false, defaultValue = "") String queryUserId,
                                       @RequestParam(value = "serviceCode", required = false, defaultValue = "") String serviceCode,
                                       @RequestParam(value = "userCollect", required = false, defaultValue = "false") Boolean userCollect) {
        IssuesGetRequest issuesGetRequest = new IssuesGetRequest();
        issuesGetRequest.setAuthuserid(userId);
        issuesGetRequest.setAuthuserdept(department);
        issuesGetRequest.setProduct(productCode);
        issuesGetRequest.setStatus(status);
        issuesGetRequest.setIssueType(issueType);
        issuesGetRequest.setPage(page);
        issuesGetRequest.setSize(size);
        issuesGetRequest.setToken(queryUserId);
        issuesGetRequest.setNewReply(newReply);
        issuesGetRequest.setMyself(myself);
        issuesGetRequest.setFrom(from);
        issuesGetRequest.setServiceCode(serviceCode);
        issuesGetRequest.setUserCollect(userCollect);
        IssuesGetResponse issuesGetResponse = new IssuesGetResponse();
        try {
            issuesGetResponse.setIssues(issueService.GetIssues(issuesGetRequest));
            issuesGetResponse.setCode("0");
        } catch (Exception ex) {
            issuesGetResponse.setCode("1");
            issuesGetResponse.setErrMsg("获取案件列表异常：" + ex.getMessage());
            log.error(ex);
        }
        return issuesGetResponse;
    }

    @RequestMapping(value = "/api/issue/customer/list", method = RequestMethod.GET)
    public IssuesGetResponse getIssuesForMis(@RequestParam(required = false, defaultValue = "0") int pageIndex,
                                             @RequestParam(required = false, defaultValue = "0") int size,
                                             @RequestParam(required = false, defaultValue = "") String accessToken,
                                             @RequestParam("authuserid") String userId,
                                             @RequestParam("authuserdept") String department,
                                             @RequestParam(required = false, defaultValue = "") String issueStatus,
                                             @RequestParam(required = false, defaultValue = "P") String issueType,
                                             @RequestParam(required = false, defaultValue = "") String startTime,
                                             @RequestParam(required = false, defaultValue = "") String endTime,
                                             @RequestParam(required = false, defaultValue = "") String machineRegion,
                                             @RequestParam(required = false, defaultValue = "") String serviceCode,
                                             @RequestParam(required = false, defaultValue = "") String productCode,
                                             @RequestParam(required = false, defaultValue = "") String erpSystemCode,
                                             @RequestParam(required = false, defaultValue = "") String esSearch) {
        IssuesGetResponse issuesGetResponse = new IssuesGetResponse();
        try {
            log.info("accessToken:" + accessToken);
            log.info("HeaderToken:" + RequestUtil.getHeaderToken());
            issuesGetResponse.setIssues(issueService.getIssuesForMis(pageIndex, size, userId, department, issueStatus, issueType, startTime, endTime, machineRegion, serviceCode, productCode, erpSystemCode, esSearch));
            issuesGetResponse.setCode("0");
        } catch (Exception ex) {
            issuesGetResponse.setCode("1");
            issuesGetResponse.setErrMsg("获取案件列表异常：" + ex.getMessage());
            log.error(ex);
        }
        return issuesGetResponse;
    }

    @RequestMapping(value = "/api/GetIssuesbyDescription", method = RequestMethod.GET)
    public IssuesGetResponse GetIssuesbyDescription(@RequestParam("authuserid") String userId,
                                                    @RequestParam("authuserdept") String department,
                                                    @RequestParam(value = "product", required = false, defaultValue = "") String productCode,
                                                    @RequestParam(value = "status", required = false, defaultValue = "") String status,
                                                    @RequestParam(value = "issueType", required = false, defaultValue = "P") String issueType,
                                                    @RequestParam(value = "page") int page,
                                                    @RequestParam(value = "size") int size,
                                                    @RequestParam(value = "queryId", required = false, defaultValue = "") String queryUserId,
                                                    @RequestParam(value = "serviceCode", required = false, defaultValue = "") String serviceCode,
                                                    @RequestParam(value = "description", required = false, defaultValue = "") String description) {
        IssuesGetRequest issuesGetRequest = new IssuesGetRequest();
        issuesGetRequest.setAuthuserid(userId);
        issuesGetRequest.setAuthuserdept(department);
        issuesGetRequest.setProduct(productCode);
        issuesGetRequest.setStatus(status);
        issuesGetRequest.setIssueType(issueType);
        issuesGetRequest.setPage(page);
        issuesGetRequest.setSize(size);
        issuesGetRequest.setServiceCode(serviceCode);
        issuesGetRequest.setToken(queryUserId);
        if (StringUtils.isNotBlank(description)) { //huly: 修复漏洞/bug StringUtils.isNotBlank(
            SearchByItem[] searchByItem = new SearchByItem[1];
            searchByItem[0] = new SearchByItem();
            searchByItem[0].setColumnsName("IssueDescription");
            searchByItem[0].setSearchType("LIKE");
            searchByItem[0].setDescription(description);
            issuesGetRequest.setSearchByItems(searchByItem);
        }

        IssuesGetResponse issuesGetResponse = new IssuesGetResponse();
        try {
            issuesGetResponse.setIssues(issueService.GetIssuesbyDescription(issuesGetRequest));
            issuesGetResponse.setCode("0");
        } catch (Exception ex) {
            issuesGetResponse.setCode("1");
            issuesGetResponse.setErrMsg("获取案件列表异常：" + ex.getMessage());
            log.error(ex);
        }
        return issuesGetResponse;
    }
/*
    private IssuesGetResponse GetIssuesByDescriptionCore(IssuesGetRequest request){
        IssuesGetResponse issuesGetResponse = new IssuesGetResponse();
        try {
            issuesGetResponse.setIssues(issueService.GetIssuesbyDescription(request.getAuthuserid(), request.getProduct(), request.getStatus(), request.getIssueType(), request.getPage(), request.getSize(), request.getAuthuserdept(), request.getToken(), request.getSearchItem(), request.getDescription(), request.getOrderByItems()));
            issuesGetResponse.setCode("0");
        } catch (Exception ex) {
            issuesGetResponse.setCode("1");
            issuesGetResponse.setErrMsg("获取案件列表异常：" + ex.getMessage());
            log.error(ex);
        }
        return issuesGetResponse;
    }
*/
    //@RequestMapping(value = "/api/GetIssuesbyDescription/new", method = RequestMethod.GET)
    // public IssuesGetResponse GetIssuesbyDescription_new(@RequestParam("authuserid") String userId,
    //                                                 @RequestParam("authuserdept") String department,
    //                                                 @RequestBody IssuesGetRequest issuesGetRequest) {
    //     issuesGetRequest.setAuthuserid(userId);
    //     issuesGetRequest.setAuthuserdept(department);
    //     return GetIssuesByDescriptionCore(issuesGetRequest);
    // }

    /**
     * @param issueId
     * @param userId
     * @param from    来源：web表示web云管家，因web云管家要查反馈人验证中的案件，而云管家中吧反馈人验证中纳入处理中，
     * @return
     */
    @RequestMapping(value = "/api/issues/{issueId}/progress", method = RequestMethod.GET)
    public IssueProgressGetResponse GetIssueProgress(@PathVariable(value = "issueId") String issueId,
                                                     @RequestParam("authuserid") String userId,
                                                     @RequestParam(value = "from", required = false, defaultValue = "") String from
    ) {
        IssueProgressGetResponse issueProgressGetResponse = new IssueProgressGetResponse();
        try {
            issueProgressGetResponse.setIssue(issueService.GetIssueProgress(issueId, userId, from));
            issueProgressGetResponse.setCode("0");
        } catch (Exception ex) {
            issueProgressGetResponse.setCode("1");
            issueProgressGetResponse.setErrMsg("获取案件进度异常：" + ex.getMessage());
            log.error(ex);
        }
        return issueProgressGetResponse;
    }

    @RequestMapping(value = "/api/issues/mis/{issueId}/progress", method = RequestMethod.GET)
    public IssueProgressGetResponse GetIssueProgressForMis(@PathVariable(value = "issueId") String issueId,
                                                           @RequestParam("authuserid") String userId) {
        IssueProgressGetResponse issueProgressGetResponse = new IssueProgressGetResponse();
        try {
            issueProgressGetResponse.setIssue(issueService.GetIssueProgressForMis(issueId, userId));
            issueProgressGetResponse.setCode("0");
        } catch (Exception ex) {
            issueProgressGetResponse.setCode("1");
            issueProgressGetResponse.setErrMsg("获取案件进度异常：" + ex.getMessage());
            log.error(ex);
        }
        return issueProgressGetResponse;
    }

    @RequestMapping(value = "/api/issues/{issueId}/remind", method = RequestMethod.PUT)
    public IssuePutResponse RemindIssue(@PathVariable(value = "issueId") Long issueId,
                                        @RequestParam("authuserid") String userId,
                                        @RequestParam("authuserdept") String deptId) {
        IssuePutResponse issuePutResponse = new IssuePutResponse();
        try {
            if (issueService.RemindIssue(issueId, userId, deptId))
                issuePutResponse.setCode("0");
            else {
                issuePutResponse.setCode("1");
                issuePutResponse.setErrMsg("催单失败");
            }
        } catch (Exception ex) {
            issuePutResponse.setCode("1");
            issuePutResponse.setErrMsg("催单失败：" + ex.getMessage());
            log.error(ex);
        }
        return issuePutResponse;
    }

    @RequestMapping(value = "/api/issues/{issueId}/confirm", method = RequestMethod.PUT)
    public IssuePutResponse ConfirmIssue(@PathVariable(value = "issueId") Long issueId,
                                         @RequestParam("authuserid") String userId,
                                         @RequestParam("desc") String desc,
                                         @RequestParam("authuserdept") String deptId) {
        IssuePutResponse issuePutResponse = new IssuePutResponse();
        try {
            if (issueService.ConfirmIssue(issueId, userId, desc, deptId))
                issuePutResponse.setCode("0");
            else {
                issuePutResponse.setCode("1");
                issuePutResponse.setErrMsg("审核提交案件");
            }
        } catch (Exception ex) {
            issuePutResponse.setCode("1");
            issuePutResponse.setErrMsg("审核提交案件失败：" + ex.getMessage());
            log.error(ex);
        }
        return issuePutResponse;
    }

    @RequestMapping(value = "/api/issues/{issueId}/sendback", method = RequestMethod.PUT)
    public IssuePutResponse SendBackIssue(@PathVariable(value = "issueId") Long issueId,
                                          @RequestParam("authuserid") String userId,
                                          @RequestParam("desc") String desc,
                                          @RequestParam("authuserdept") String deptId) {
        IssuePutResponse issuePutResponse = new IssuePutResponse();
        try {
            if (issueService.SendBackIssue(issueId, userId, desc, deptId))
                issuePutResponse.setCode("0");
            else {
                issuePutResponse.setCode("1");
                issuePutResponse.setErrMsg("退回案件失败");
            }
        } catch (Exception ex) {
            issuePutResponse.setCode("1");
            issuePutResponse.setErrMsg("退回案件失败：" + ex.getMessage());
            log.error(ex);
        }
        return issuePutResponse;
    }

    @RequestMapping(value = "/api/issues/{issueId}/evaluate", method = RequestMethod.PUT)
    public IssuePutResponse EvaluateIssue(@PathVariable(value = "issueId") Long issueId,
                                          @RequestParam("authuserid") String userId,
                                          @RequestParam("desc") String desc,
                                          @RequestParam("authuserdept") String deptId) {
        IssuePutResponse issuePutResponse = new IssuePutResponse();
        try {
            if (issueService.EvaluateIssue(issueId, userId, desc, deptId))
                issuePutResponse.setCode("0");
            else {
                issuePutResponse.setCode("1");
                issuePutResponse.setErrMsg("评价案件成功");
            }
        } catch (Exception ex) {
            issuePutResponse.setCode("1");
            issuePutResponse.setErrMsg("评价案件失败：" + ex.getMessage());
            log.error(ex);
        }
        return issuePutResponse;
    }

    @RequestMapping(value = "/api/issues/{issueId}/followupstatus", method = RequestMethod.GET)
    public ResponseBase GetFollowupIssueStatus(@PathVariable(value = "issueId") Long issueId) {
        ResponseBase responseBase = new ResponseBase();
        try {
            int res = issueService.GetFollowUpIssueStatus(issueId);
            responseBase.setCode("0");
            responseBase.setData(Long.toString(res));
        } catch (Exception ex) {
            responseBase.setCode("1");
            responseBase.setErrMsg("获取跟催案件状态失败：" + ex.getMessage());
            log.error(ex);
        }
        return responseBase;
    }

    @RequestMapping(value = "/api/issues/followup", method = RequestMethod.POST)
    public IssuePutResponse FollowupIssue(@RequestParam("authuserid") String userId,
                                          @RequestParam("desc") String desc,
                                          @RequestBody String issuestr) {
        IssuePutResponse issuePutResponse = new IssuePutResponse();
        try {
            Issue issue = new Issue();
            Gson gson = new Gson();
            issue = gson.fromJson(issuestr, Issue.class);
            int followUpRes = issueService.FollowUpIssue(issue, userId, desc);
            if (followUpRes == 0)
                issuePutResponse.setCode("0");
            else if (followUpRes == 2) {
                issuePutResponse.setCode("2");
                issuePutResponse.setErrMsg("跟催案件频繁");
            }
        } catch (Exception ex) {
            issuePutResponse.setCode("1");
            issuePutResponse.setErrMsg("跟催案件失败：" + ex.getMessage());
            log.error(ex);
        }
        return issuePutResponse;
    }


    @RequestMapping(value = "/api/issues/count", method = RequestMethod.GET)
    public IssueCountGetResponse GetIssueCount(@RequestParam("authuserid") String userId,
                                               @RequestParam("authuserdept") String deptId,
                                               @RequestParam(value = "queryId", required = false, defaultValue = "") String queryUserId,
                                               @RequestParam(value = "product", required = false, defaultValue = "") String productCode,
                                               @RequestParam(value = "serviceRegion", required = false, defaultValue = "") String serviceRegion,
                                               @RequestParam(value = "condition_TW", required = false, defaultValue = "") String condition_TW,
                                               @RequestParam(value = "condition_check_TW", required = false, defaultValue = "") String condition_check_TW,
                                               @RequestParam(value = "agent", required = false, defaultValue = "") String agent,
                                               @RequestParam(value = "newReply", required = false, defaultValue = "") String newReply,
                                               @RequestParam(value = "myself", required = false, defaultValue = "") String myself,
                                               @RequestParam(value = "from", required = false, defaultValue = "") String from,
                                               @RequestParam(value = "serviceCode", required = false, defaultValue = "") String serviceCode
    ) {
        IssueCountGetResponse issueCountGetResponse = new IssueCountGetResponse();
        try {
            issueCountGetResponse.setIssueCount(issueService.GetIssueCountByUserId(userId, productCode, deptId, queryUserId, serviceRegion, condition_TW, condition_check_TW, agent, newReply, myself, from, serviceCode));
            issueCountGetResponse.setCode("0");
        } catch (Exception ex) {
            issueCountGetResponse.setCode("1");
            issueCountGetResponse.setErrMsg("获取案件数量异常：" + ex.getMessage());
            log.error(ex);
        }
        return issueCountGetResponse;
    }

    /**
     * for portal 开会讨论先加一个不需要数据权限的统计数量的接口
     *
     * @param userId
     * @param productCode
     * @param serviceRegion
     * @return
     */
    @RequestMapping(value = "/api/issues/portal/count", method = RequestMethod.GET)
    public IssueCountGetResponse GetPortalIssueCount(@RequestParam(value = "userId", required = true) String userId,
                                                     @RequestParam(value = "product", required = false, defaultValue = "") String productCode,
                                                     @RequestParam(value = "serviceRegion", required = false, defaultValue = "") String serviceRegion) {
        IssueCountGetResponse issueCountGetResponse = new IssueCountGetResponse();
        try {
            issueCountGetResponse.setIssueCount(issueService.GetIssueCountByUserId(userId, productCode, "", "", serviceRegion, "", "", "", "", "", "", ""));
            issueCountGetResponse.setCode("0");
        } catch (Exception ex) {
            issueCountGetResponse.setCode("1");
            issueCountGetResponse.setErrMsg("获取案件数量异常：" + ex.getMessage());
            log.error(ex);
        }
        return issueCountGetResponse;
    }

    @RequestMapping(value = "/api/issues/countbydescription", method = RequestMethod.GET)
    public IssueCountGetResponse GetIssueCountByDescription(@RequestParam("authuserid") String userId,
                                                            @RequestParam("authuserdept") String deptId,
                                                            @RequestParam(value = "description", required = false, defaultValue = "") String description,
                                                            @RequestParam(value = "queryId", required = false, defaultValue = "") String queryUserId,
                                                            @RequestParam(value = "product", required = false, defaultValue = "") String productCode) {
        IssuesGetRequest issuesGetRequest = new IssuesGetRequest();
        issuesGetRequest.setAuthuserid(userId);
        issuesGetRequest.setAuthuserdept(deptId);
        issuesGetRequest.setProduct(productCode);
        issuesGetRequest.setToken(queryUserId);
        if (StringUtils.isNotBlank(description)) { //huly: 修复漏洞/bug StringUtils.isNotBlank(
            SearchByItem[] searchByItem = new SearchByItem[1];
            searchByItem[0] = new SearchByItem();
            searchByItem[0].setColumnsName("IssueDescription");
            searchByItem[0].setSearchType("LIKE");
            searchByItem[0].setDescription(description);
            issuesGetRequest.setSearchByItems(searchByItem);
        }
        IssueCountGetResponse issueCountGetResponse = new IssueCountGetResponse();
        try {
            issueCountGetResponse.setIssueCount(issueService.GetIssueCountByUserIdAndDescription(issuesGetRequest));
            issueCountGetResponse.setCode("0");
        } catch (Exception ex) {
            issueCountGetResponse.setCode("1");
            issueCountGetResponse.setErrMsg("获取案件数量异常：" + ex.getMessage());
            log.error(ex);
        }
        return issueCountGetResponse;
    }

    @RequestMapping(value = "/api/issues/staff", method = RequestMethod.GET)
    public IssuesGetResponse GetIssuesByStaff(@RequestParam("authuserid") String userId,
                                              @RequestParam(value = "status", required = false, defaultValue = "") String status,
                                              @RequestParam("page") int page,
                                              @RequestParam("size") int size,
                                              @RequestParam("authuserdept") String deptId,
                                              @RequestParam(value = "queryId", required = false, defaultValue = "") String queryUserId) {
        IssuesGetResponse issuesGetResponse = new IssuesGetResponse();
        try {
            issuesGetResponse.setIssues(issueService.GetIssuesByStaff(userId, status, page, size, deptId, queryUserId));
            issuesGetResponse.setCode("0");
        } catch (Exception ex) {
            issuesGetResponse.setCode("1");
            issuesGetResponse.setErrMsg("获取案件列表异常：" + ex.getMessage());
            log.error(ex);
        }
        return issuesGetResponse;
    }

    @RequestMapping(value = "/api/sendmailTest", method = RequestMethod.POST)
    public void GetIssuesByStaff(@RequestParam String email) {
        log.info("---------------sendmailTest--------------");
        issueService.SendMailTest(email);
    }

    @RequestMapping(value = "/api/issues/{issueId}/attachs", method = RequestMethod.GET)
    public IssueAttachmentsGetResponse GetIssueAttachments(@PathVariable(value = "issueId") String issueId) {
        IssueAttachmentsGetResponse issueAttachmentsGetResponse = new IssueAttachmentsGetResponse();
        try {
            issueAttachmentsGetResponse.setIssueAttachmentFiles(issueService.GetIssueAttachmentFiles(issueId));
            issueAttachmentsGetResponse.setCode("0");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return issueAttachmentsGetResponse;
    }

    @RequestMapping(value = "/api/issues/attach/{fileId}", method = RequestMethod.GET)
    public void GetIssueAttachFile(@PathVariable(value = "fileId") String fileId,
                                   HttpServletResponse response) {
        try {
            OutputStream out = response.getOutputStream();
            GridFSDBFile gridFSDBFile = issueService.GetIssueAttachFile(fileId);
            if (gridFSDBFile != null) {
                //response.setContentType("text/html; charset=UTF-8");//注意text/html，和application/html
                //response.setHeader("Content-Disposition", "attachment;filename=" + gridFSDBFile.getFilename());
                response.setContentType(gridFSDBFile.getContentType().replace(".", "image/"));
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(gridFSDBFile.getFilename(), "UTF-8"));


                gridFSDBFile.writeTo(out);
                out.flush();
                out.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "/api/issues/attach1/{fileId}", method = RequestMethod.GET)
    public void GetIssueAttachFile1(@PathVariable(value = "fileId") String fileId,
                                    HttpServletResponse response) {
        try {
            OutputStream out = response.getOutputStream();
            GridFSDBFile gridFSDBFile = issueService.GetIssueAttachFile(fileId);
            if (gridFSDBFile != null) {
                //response.setContentType("text/html; charset=UTF-8");//注意text/html，和application/html
                //response.setHeader("Content-Disposition", "attachment;filename=" + gridFSDBFile.getFilename());
                response.setContentType("video/mp4");
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(gridFSDBFile.getFilename(), "UTF-8"));


                gridFSDBFile.writeTo(out);
                out.flush();
                out.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "/api/issues/attach/{fileId}/download", method = RequestMethod.GET)
    public void GetIssueAttachFileDownload(@PathVariable(value = "fileId") String fileId,
                                           HttpServletResponse response) {
        try {
            OutputStream out = response.getOutputStream();
            GridFSDBFile gridFSDBFile = issueService.GetIssueAttachFile(fileId);
            if (gridFSDBFile != null) {
                response.setContentType("application/octet-stream;charset=UTF-8");
                response.addHeader("Pargam", "no-cache");
                response.addHeader("Cache-Control", "no-cache");
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(gridFSDBFile.getFilename(), "UTF-8"));
                gridFSDBFile.writeTo(out);
                out.flush();
                out.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "/api/issues/StaffIssue", method = RequestMethod.GET)
    public GeneralResult<List<IssueDetailInfo>> getAllIssueDetailsFromDB(@RequestParam("issueStatus") String issueStatus,
                                                                         @RequestParam(required = true) int pageIndex,
                                                                         @RequestParam(required = true) int size,
                                                                         @RequestParam("authuserid") String userId,
                                                                         @RequestParam("authuserdept") String deptId,
                                                                         @RequestParam(required = false, defaultValue = "") String queryUserId,
                                                                         @RequestParam(required = false, defaultValue = "p") String issueType,
                                                                         @RequestParam(required = false, defaultValue = "") String startTime,
                                                                         @RequestParam(required = false, defaultValue = "") String endTime,
                                                                         @RequestParam(required = false, defaultValue = "") String machineRegion,
                                                                         @RequestParam(value = "additionalExplanationReadType", required = false, defaultValue = "1") String additionalExplanationReadType,
                                                                         @RequestParam(value = "serviceContact", required = false, defaultValue = "0") String serviceContact,
                                                                         @RequestParam(value = "projectUpdate", required = false, defaultValue = "0") String projectUpdate,
                                                                         @RequestParam(value = "serialNumberUpdate", required = false, defaultValue = "0") String serialNumberUpdate,
                                                                         @RequestParam(required = false, defaultValue = "") String custLevel,
                                                                         @RequestParam(required = false, defaultValue = "") String serviceCode,
                                                                         @RequestParam(required = false, defaultValue = "") String productCode,
                                                                         @RequestParam(required = false, defaultValue = "") String employeeId,
                                                                         @RequestParam(required = false, defaultValue = "") String crmId,
                                                                         @RequestParam(required = false, defaultValue = "") String supportId) {
        List<IssueDetailInfo> serviceResult = issueService.getIssuesInfoListByUserAndStatus(issueStatus, pageIndex, size, userId, deptId
                , issueType, queryUserId, startTime, endTime, machineRegion, additionalExplanationReadType, serviceContact, projectUpdate, serialNumberUpdate, custLevel, serviceCode, productCode, employeeId, crmId, supportId);
        List<IssueDetailInfo> issueDetailInfos = new ArrayList<>();
        if (serviceResult != null) {
            for (IssueDetailInfo temp : serviceResult) {
                temp.setIssuedescription(truncateDescription(temp.getIssuedescription()));
                temp.setCreatdatetime(truncateCreatedTime(temp.getCreatdatetime()));
                long count = issueDetailInfos.stream().filter(o -> o.getCrmId().equals(temp.getCrmId())).count();
                if (count < 1)
                    issueDetailInfos.add(temp);
            }
        }
        return new GeneralResult<>(issueDetailInfos);
    }

    /**
     * 新版易聊
     *
     * @param issueStatus
     * @param userId
     * @param deptId
     * @param queryUserId
     * @param issueType
     * @param startTime
     * @param endTime
     * @param machineRegion
     * @param custLevel
     * @param serviceCode
     * @param productCode
     * @param additionalExplanationReadType
     * @return
     */
    @RequestMapping(value = "/v2/api/issues/StaffIssueCountV2", method = RequestMethod.GET)
    public SimpleResponse StaffIssueCountV2(@RequestParam("issueStatus") String issueStatus,
                                            @RequestParam("authuserid") String userId,
                                            @RequestParam("authuserdept") String deptId,
                                            @RequestParam(required = false, defaultValue = "") String queryUserId,
                                            @RequestParam(required = false, defaultValue = "p") String issueType,
                                            @RequestParam(required = false, defaultValue = "") String startTime,
                                            @RequestParam(required = false, defaultValue = "") String endTime,
                                            @RequestParam(required = false, defaultValue = "") String machineRegion,
                                            @RequestParam(required = false, defaultValue = "") String custLevel,
                                            @RequestParam(required = false, defaultValue = "") String contractState,
                                            @RequestParam(required = false, defaultValue = "") String serviceCode,
                                            @RequestParam(required = false, defaultValue = "") String productCode,
                                            @RequestParam(required = false, defaultValue = "") String employeeId,
                                            @RequestParam(required = false, defaultValue = "") String crmId,
                                            @RequestParam(required = false, defaultValue = "1") String additionalExplanationReadType,
                                            @RequestParam(required = false, defaultValue = "") String issueDescription) {
        SimpleResponse response = new SimpleResponse();
        try {
            System.out.println("authuserid:" + userId);
            System.out.println("authuserdept:" + deptId);
            int result = issueService.StaffIssueCountV2(issueStatus, userId, deptId,
                    issueType, queryUserId, startTime, endTime, machineRegion, custLevel, contractState, serviceCode,
                    productCode, employeeId, crmId, additionalExplanationReadType, issueDescription);
            response.setData(Integer.toString(result));
        } catch (Exception ex) {
            response.setErrMsg(ex.getMessage());
        }
        return response;
    }

    /**
     * 新版易聊
     *
     * @param issueStatus
     * @param pageIndex
     * @param size
     * @param userId
     * @param deptId
     * @param queryUserId
     * @param issueType
     * @param startTime
     * @param endTime
     * @param machineRegion
     * @param custLevel
     * @param serviceCode
     * @param productCode
     * @param additionalExplanationReadType
     * @return
     */
    @RequestMapping(value = "/v2/api/issues/StaffIssue", method = RequestMethod.GET)
    public GeneralResult<List<IssueDetailInfo>> getAllIssueDetails(@RequestParam("issueStatus") String issueStatus,
                                                                   @RequestParam(required = true) int pageIndex,
                                                                   @RequestParam(required = true) int size,
                                                                   @RequestParam("authuserid") String userId,
                                                                   @RequestParam("authuserdept") String deptId,
                                                                   @RequestParam(required = false, defaultValue = "") String queryUserId,
                                                                   @RequestParam(required = false, defaultValue = "p") String issueType,
                                                                   @RequestParam(required = false, defaultValue = "") String startTime,
                                                                   @RequestParam(required = false, defaultValue = "") String endTime,
                                                                   @RequestParam(required = false, defaultValue = "") String machineRegion,
                                                                   @RequestParam(required = false, defaultValue = "") String custLevel,
                                                                   @RequestParam(required = false, defaultValue = "") String contractState,
                                                                   @RequestParam(required = false, defaultValue = "") String serviceCode,
                                                                   @RequestParam(required = false, defaultValue = "") String productCode,
                                                                   @RequestParam(required = false, defaultValue = "") String employeeId,
                                                                   @RequestParam(required = false, defaultValue = "") String crmId,
                                                                   @RequestParam(value = "additionalExplanationReadType", required = false, defaultValue = "1") String additionalExplanationReadType,
                                                                   @RequestParam(required = false, defaultValue = "") String issueDescription
    ) {
        System.out.println("authuserid:" + userId);
        System.out.println("authuserdept:" + deptId);
        List<IssueDetailInfo> serviceResult = issueService.getAllIssueDetails(issueStatus, pageIndex, size, userId, deptId
                , issueType, queryUserId, startTime, endTime, machineRegion, custLevel, contractState, serviceCode, productCode, employeeId, crmId, additionalExplanationReadType, issueDescription);
        List<IssueDetailInfo> issueDetailInfos = new ArrayList<>();
        if (serviceResult != null) {
            for (IssueDetailInfo temp : serviceResult) {
                temp.setIssuedescription(truncateDescription(temp.getIssuedescription()));
                temp.setCreatdatetime(truncateCreatedTime(temp.getCreatdatetime()));
                long count = issueDetailInfos.stream().filter(o -> o.getCrmId() != null && o.getCrmId().equals(temp.getCrmId())).count();
                if (count < 1)
                    issueDetailInfos.add(temp);
            }
        }
        return new GeneralResult<>(issueDetailInfos);
    }

    /**
     * 客服主管查看的案件列表总数量
     *
     * @param issueStatus
     * @param userId
     * @param deptId
     * @param queryUserId
     * @param issueType
     * @param startTime
     * @param endTime
     * @param machineRegion
     * @param custLevel
     * @param serviceCode
     * @param productCode
     * @param additionalExplanationReadType
     * @return
     */
    @RequestMapping(value = "/api/issues/StaffIssueCountForCustomerServiceManager", method = RequestMethod.GET)
    public SimpleResponse getStaffIssueCountForCustomerServiceManager(@RequestParam("issueStatus") String issueStatus,
                                                                      @RequestParam("authuserid") String userId,
                                                                      @RequestParam("authuserdept") String deptId,
                                                                      @RequestParam(required = false, defaultValue = "") String queryUserId,
                                                                      @RequestParam(required = false, defaultValue = "p") String issueType,
                                                                      @RequestParam(required = false, defaultValue = "") String startTime,
                                                                      @RequestParam(required = false, defaultValue = "") String endTime,
                                                                      @RequestParam(required = false, defaultValue = "") String machineRegion,
                                                                      @RequestParam(required = false, defaultValue = "") String custLevel,
                                                                      @RequestParam(required = false, defaultValue = "") String serviceCode,
                                                                      @RequestParam(required = false, defaultValue = "") String productCode,
                                                                      @RequestParam(required = false, defaultValue = "") String employeeId,
                                                                      @RequestParam(required = false, defaultValue = "") String crmId,
                                                                      @RequestParam(required = false, defaultValue = "") String classificationStr,
                                                                      @RequestParam(required = false, defaultValue = "1") String additionalExplanationReadType) {
        SimpleResponse response = new SimpleResponse();
        try {
            int result = issueService.getIssuesInfoCountForCustomerServiceManager(issueStatus, userId, deptId,
                    issueType, queryUserId, startTime, endTime, machineRegion, custLevel, serviceCode,
                    productCode, employeeId, crmId, additionalExplanationReadType, classificationStr);
            response.setData(Integer.toString(result));
        } catch (Exception ex) {
            response.setErrMsg(ex.getMessage());
        }
        return response;
    }

    /**
     * 大陆客服主管与客服查看易聊案件列表区分开，主管的案件列表不限定部门，支持跨授权产品线
     *
     * @param issueStatus
     * @param pageIndex
     * @param size
     * @param userId
     * @param deptId
     * @param queryUserId
     * @param issueType
     * @param startTime
     * @param endTime
     * @param machineRegion
     * @param custLevel
     * @param serviceCode
     * @param productCode
     * @param additionalExplanationReadType
     * @return
     */
    @RequestMapping(value = "/api/issues/StaffIssueForCustomerServiceManager", method = RequestMethod.GET)
    public GeneralResult<List<IssueDetailInfo>> getAllIssueDetailsForCustomerServiceManager(@RequestParam("issueStatus") String issueStatus,
                                                                                            @RequestParam(required = true) int pageIndex,
                                                                                            @RequestParam(required = true) int size,
                                                                                            @RequestParam("authuserid") String userId,
                                                                                            @RequestParam("authuserdept") String deptId,
                                                                                            @RequestParam(required = false, defaultValue = "") String queryUserId,
                                                                                            @RequestParam(required = false, defaultValue = "p") String issueType,
                                                                                            @RequestParam(required = false, defaultValue = "") String startTime,
                                                                                            @RequestParam(required = false, defaultValue = "") String endTime,
                                                                                            @RequestParam(required = false, defaultValue = "") String machineRegion,
                                                                                            @RequestParam(required = false, defaultValue = "") String custLevel,
                                                                                            @RequestParam(required = false, defaultValue = "") String serviceCode,
                                                                                            @RequestParam(required = false, defaultValue = "") String productCode,
                                                                                            @RequestParam(required = false, defaultValue = "") String employeeId,
                                                                                            @RequestParam(required = false, defaultValue = "") String crmId,
                                                                                            @RequestParam(required = false, defaultValue = "") String classificationStr,
                                                                                            @RequestParam(value = "additionalExplanationReadType", required = false, defaultValue = "1") String additionalExplanationReadType) {
        List<IssueDetailInfo> serviceResult = issueService.getIssuesInfoListByUserAndStatusForCustomerServiceManager(issueStatus, pageIndex, size, userId, deptId
                , issueType, queryUserId, startTime, endTime, machineRegion, custLevel, serviceCode, productCode, employeeId, crmId, additionalExplanationReadType, classificationStr);
        List<IssueDetailInfo> issueDetailInfos = new ArrayList<>();
        if (serviceResult != null) {
            for (IssueDetailInfo temp : serviceResult) {
                temp.setIssuedescription(truncateDescription(temp.getIssuedescription()));
                temp.setCreatdatetime(truncateCreatedTime(temp.getCreatdatetime()));
                long count = issueDetailInfos.stream().filter(o -> o.getCrmId() != null && o.getCrmId().equals(temp.getCrmId())).count();
                if (count < 1)
                    issueDetailInfos.add(temp);
            }
        }
        return new GeneralResult<>(issueDetailInfos);
    }

    @RequestMapping(value = "/api/getAllIssueDetails", method = RequestMethod.GET)
    public BaseResponse getAllIssueDetails(@RequestParam("role") String role,
                                           @RequestParam("workNo") String workNo,
                                           @RequestParam(required = false, defaultValue = "") String color,
                                           @RequestParam(required = false, defaultValue = "") String issueStatus,
                                           @RequestParam("pageNum") int pageNum,
                                           @RequestParam("pageSize") int pageSize) {
        try {
            PageInfo<IssueDetailInfo> pageInfo = issueService.getAllIssueDetails(role, workNo, color, issueStatus, pageNum, pageSize);

            if (!CollectionUtils.isEmpty(pageInfo.getList())) {
                for (IssueDetailInfo temp : pageInfo.getList()) {
                    temp.setState(mapState(temp.getState()));
                    temp.setIssuedescription(truncateDescription(temp.getIssuedescription()));
                    temp.setCreatdatetime(truncateCreatedTime(temp.getCreatdatetime()));
                }
            }
            return BaseResponse.ok(pageInfo);

        } catch (Exception ex) {
            log.error(ex.toString());
            return BaseResponse.error(ResponseStatus.QUERY_VERIFY);

        }
    }

    /**
     * 判断某服务中心的某产品线的案件状态是否需要同步到crm。返回的对象是boolean，true表示需要同步，false表示不需要同步
     *
     * @param serviceRegion
     * @param productCode
     * @return
     */
    @RequestMapping(value = "/api/checkIssueIsSyncCrm", method = RequestMethod.GET)
    public BaseResponse checkIssueIsSyncCrm(@RequestParam(required = true) String serviceRegion,
                                            @RequestParam(required = true) String productCode,
                                            @RequestParam(required = true) String issueStatus) {
        try {
            return BaseResponse.ok(issueService.checkIssueIsSyncCrm(serviceRegion, productCode, issueStatus));
        } catch (Exception ex) {
            log.error("查询是否同步CRM设置失败.", ex);
            return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
        }
    }

    @RequestMapping(value = "/api/issues/StaffIssueCount", method = RequestMethod.GET)
    public SimpleResponse getStaffIssueCount(@RequestParam("issueStatus") String issueStatus,
                                             @RequestParam("authuserid") String userId,
                                             @RequestParam("authuserdept") String deptId,
                                             @RequestParam(required = false, defaultValue = "") String queryUserId,
                                             @RequestParam(required = false, defaultValue = "p") String issueType,
                                             @RequestParam(required = false, defaultValue = "") String startTime,
                                             @RequestParam(required = false, defaultValue = "") String endTime,
                                             @RequestParam(required = false, defaultValue = "") String machineRegion,
                                             @RequestParam(required = false, defaultValue = "1") String additionalExplanationReadType,
                                             @RequestParam(required = false, defaultValue = "0") String serviceContact,
                                             @RequestParam(required = false, defaultValue = "0") String projectUpdate,
                                             @RequestParam(required = false, defaultValue = "0") String serialNumberUpdate,
                                             @RequestParam(required = false, defaultValue = "") String custLevel,
                                             @RequestParam(required = false, defaultValue = "") String serviceCode,
                                             @RequestParam(required = false, defaultValue = "") String productCode,
                                             @RequestParam(required = false, defaultValue = "") String employeeId,
                                             @RequestParam(required = false, defaultValue = "") String crmId,
                                             @RequestParam(required = false, defaultValue = "") String supportId) {
        SimpleResponse response = new SimpleResponse();
        try {
            int result = issueService.getIssuesInfoCountByUserAndStatus(issueStatus, userId, deptId
                    , issueType, queryUserId, startTime, endTime, machineRegion, additionalExplanationReadType, serviceContact, projectUpdate, serialNumberUpdate, custLevel, serviceCode, productCode, employeeId, crmId, supportId);
            response.setData(Integer.toString(result));
        } catch (Exception ex) {
            response.setErrMsg(ex.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/api/issues/IssueCountByKewei", method = RequestMethod.GET)
    public SimpleResponse getStaffIssueCountKewei(@RequestParam("issueStatus") String issueStatus,
                                                  @RequestParam("authuserid") String userId,
                                                  @RequestParam("authuserdept") String deptId,
                                                  @RequestParam(required = false, defaultValue = "") String queryUserId,
                                                  @RequestParam(required = false, defaultValue = "p") String issueType,
                                                  @RequestParam(required = false, defaultValue = "") String startTime,
                                                  @RequestParam(required = false, defaultValue = "") String endTime,
                                                  @RequestParam(required = false, defaultValue = "") String machineRegion,
                                                  @RequestParam(required = false, defaultValue = "1") String additionalExplanationReadType) {
        //抓取產品線為科維的所有案件數
        SimpleResponse response = new SimpleResponse();
        try {
            String productCode = "15";
            int result = issueService.getIssuesInfoCountByKewei(issueStatus, issueType, additionalExplanationReadType, productCode);
            response.setData(Integer.toString(result));
        } catch (Exception ex) {
            response.setErrMsg(ex.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/api/issues/webstaffissue", method = RequestMethod.GET)
    public IssueDetailInfosGetResponse getWebIssueDetails(@RequestParam("issueStatus") String issueStatus,
                                                          @RequestParam(required = false, defaultValue = "") String productCode,
                                                          @RequestParam(required = false, defaultValue = "") String codeOrName,
                                                          @RequestParam(required = true) int pageIndex,
                                                          @RequestParam(required = true) int size,
                                                          @RequestParam(required = false, defaultValue = "p") String issueType,
                                                          @RequestParam(required = false, defaultValue = "") String startTime,
                                                          @RequestParam(required = false, defaultValue = "") String endTime) {
        IssueDetailInfosGetResponse issueDetailInfosGetResponse = new IssueDetailInfosGetResponse();
        try {
            List<IssueDetailInfo> serviceResult = issueService.getWebIssuesInfoList(issueStatus, productCode, codeOrName, pageIndex, size
                    , issueType, startTime, endTime);
            if (serviceResult != null) {
                for (IssueDetailInfo temp : serviceResult) {
                    temp.setIssuedescription(truncateDescription(temp.getIssuedescription()));
                    temp.setCreatdatetime(truncateCreatedTime(temp.getCreatdatetime()));
                }
            }
            int count = issueService.getWebIssuesCount(issueStatus, productCode, codeOrName, issueType, startTime, endTime);
            issueDetailInfosGetResponse.setIssueDetailInfos(serviceResult);
            issueDetailInfosGetResponse.setIssueTotalCount(count);
            issueDetailInfosGetResponse.setCode("0");
        } catch (Exception ex) {
            issueDetailInfosGetResponse.setCode("1");
            issueDetailInfosGetResponse.setErrMsg("获取Web案件异常：" + ex.getMessage());
            log.error(ex);
        }
        return issueDetailInfosGetResponse;
    }

    @RequestMapping(value = "/api/issues/webstaffissuecount", method = RequestMethod.GET)
    public ResponseBase getWebIssueCount(@RequestParam("issueStatus") String issueStatus,
                                         @RequestParam(required = false, defaultValue = "") String productCode,
                                         @RequestParam(required = false, defaultValue = "") String codeOrName,
                                         @RequestParam(required = false, defaultValue = "p") String issueType,
                                         @RequestParam(required = false, defaultValue = "") String startTime,
                                         @RequestParam(required = false, defaultValue = "") String endTime) {
        ResponseBase responseBase = new ResponseBase();
        try {
            int res = issueService.getWebIssuesCount(issueStatus, productCode, codeOrName, issueType, startTime, endTime);
            responseBase.setData(Long.toString(res));
            responseBase.setCode("0");
        } catch (Exception ex) {
            responseBase.setCode("1");
            responseBase.setErrMsg("获取Web案件数量异常：" + ex.getMessage());
            log.error(ex);
        }
        return responseBase;
    }

    @RequestMapping(value = "/api/issues/close", method = RequestMethod.POST)
    public SimpleResponse closeIssue(@RequestBody String issueDetail,
                                     @RequestParam("authuserid") String userId,
                                     @RequestParam("authuserdept") String deptId) {
        SimpleResponse response = new SimpleResponse();
        try {
            Gson gson = new Gson();
            IssueDetailInfo issueDetailInfo = gson.fromJson(issueDetail, IssueDetailInfo.class);
            if (issueDetailInfo != null && issueDetailInfo.getIssueCasedetail() != null
                    && issueDetailInfo.getIssueProgresses() != null
                    && issueDetailInfo.getIssueProgresses().size() > 0) {
                boolean result = issueDetailService.CloseIssue(issueDetailInfo.getIssueId(), userId, issueDetailInfo.getProductVersion(),
                        issueDetailInfo.getCrmId(), deptId, issueDetailInfo.getIssueProgresses(), issueDetailInfo.getIssueCasedetail(), issueDetailInfo.getSyncStatus());

                //更新回 influxDB 如果有預警編號的話
                issueService.updateWarningNoticeStatus(issueDetailInfo.getCrmId());
                //更新事件狀態
                issueService.updateEventNoticeStatus(issueDetailInfo.getCrmId());

                response.setCode("200");
                response.setData("true");
            } else {
                response.setErrMsg("数据错误");
                response.setCode("404");
                response.setData("false");
            }

        } catch (Exception ex) {
            response.setCode("404");
            response.setData("false");
            response.setErrMsg("结案失败：" + ex.getMessage());
            log.error(ex);
        }
        return response;
    }

    @RequestMapping(value = "/api/IssueServiceRegionInfosByCustomerServiceCode", method = RequestMethod.GET)
    public SimpleResponse GetCustomerIssueServiceRegionInfos(@RequestParam("customerServiceCode") String customerServiceCode) {
        SimpleResponse response = new SimpleResponse();
        try {
            List<String> result = new ArrayList<String>();
            result = issueService.getCustomerIssueServiceRegionInfo(customerServiceCode);
            response.setCode("0");
            response.setData(String.join(",", result));
        } catch (Exception ex) {
            response.setCode("1");
            response.setErrMsg("獲取客戶案件服務中心失敗" + ex.getMessage());
            log.error(ex);
        }
        return response;
    }

    @RequestMapping(value = "/api/IssueServiceRegionInfos", method = RequestMethod.GET)
    public SimpleResponse GetIssueServiceRegionInfos(@RequestParam("customerServiceCode") String customerServiceCode) {
        SimpleResponse response = new SimpleResponse();
        try {
            List<String> result = new ArrayList<String>();
            result = issueService.getCustomerIssueServiceRegionInfo(customerServiceCode);
            response.setCode("0");
            response.setData(String.join(",", result));
        } catch (Exception ex) {
            response.setCode("1");
            response.setErrMsg("獲取客戶案件服務中心失敗" + ex.getMessage());
            log.error(ex);
        }
        return response;
    }

    @RequestMapping(value = "/api/IssueConnectAreaByStaffId", method = RequestMethod.GET)
    public SimpleResponse GetMachineRegionByStaffId(@RequestParam("staffId") String staffId) {
        SimpleResponse response = new SimpleResponse();
        try {
            List<String> result = new ArrayList<String>();
            result = issueService.getMachineRegionByStaffId(staffId);
            response.setCode("0");
            response.setData(String.join(",", result));
        } catch (Exception ex) {
            response.setCode("1");
            response.setErrMsg("獲取客服案件客戶地區失敗" + ex.getMessage());
            log.error(ex);
        }
        return response;
    }

    //取得試用案件提交限制
    @RequestMapping(value = "/api/issues/trialSubmitIssueLimit", method = RequestMethod.GET)
    public TrialIssueLimitResponse TrialSubmitIssueLimit(@RequestParam("serviceCode") String serviceCode,
                                                         @RequestParam("productCode") String productCode) {
        TrialIssueLimitResponse trialIssueLimitResponse = new TrialIssueLimitResponse();
        try {
            trialIssueLimitResponse = issueService.SelectTrialSubmitedIssueCount(serviceCode, productCode);
        } catch (Exception ex) {
            trialIssueLimitResponse.setCode("1");
            trialIssueLimitResponse.setErrMsg("獲取試用案件提交數失敗" + ex.getMessage());
            log.error(ex);
        }
        return trialIssueLimitResponse;
    }

    @PutMapping(value = "/api/issues/service/update")
    public SimpleResponse updateIssueService(@RequestParam("authuserid") String userId,
                                             @RequestParam("issueId") String issueId,
                                             @RequestParam("serviceId") String serviceId,
                                             @RequestParam("productCode") String productCode) {
        SimpleResponse response = new SimpleResponse();
        try {
            boolean res = issueService.updateIssueService(userId, issueId, serviceId, productCode);
            response.setCode("0");
            response.setData(Boolean.toString(res));
        } catch (Exception ex) {
            log.error(ex);
            response.setCode("1");
            response.setErrMsg(ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/api/issues/usercontact/update")
    public ResponseBase updateUserContact(@RequestParam("issueId") String issueId,
                                          @RequestBody UserContact userContact) {
        ResponseBase responseBase = new ResponseBase();
        try {
            boolean res = userService.updateUserContact(issueId, userContact);
            responseBase.setCode("0");
            responseBase.setData(Boolean.toString(res));
        } catch (Exception ex) {
            responseBase.setCode("1");
            responseBase.setErrMsg(ex.getMessage());
            log.error(ex);
        }
        return responseBase;
    }

    @PutMapping(value = "/api/issues/{issueId}/cancelclose")
    public SimpleResponse cancelCloseIssue(@PathVariable("issueId") String issueId,
                                           @RequestParam("progressId") long progressId) {
        SimpleResponse response = new SimpleResponse();
        try {
            boolean res = issueService.cancelCloseIssue(issueId, progressId);
            response.setCode("0");
            response.setData(Boolean.toString(res));
        } catch (Exception ex) {
            log.error(ex);
            response.setCode("1");
            response.setErrMsg(ex.getMessage());
        }
        return response;
    }

    @PutMapping(value = "/v2/api/issues/{issueId}/cancelclose")
    public SimpleResponse cancelClose(@PathVariable("issueId") long issueId,
                                      @RequestParam(required = false, defaultValue = "") String crmId,
                                      @RequestParam("userId") String userId) {
        SimpleResponse response = new SimpleResponse();
        try {
            boolean res = issueService.cancelClose(issueId, crmId, userId);
            if (res) {
                response.setCode("0");
                response.setData(Boolean.toString(res));
            }

        } catch (Exception ex) {
            log.error(ex);
            response.setCode("1");
            response.setErrMsg(ex.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/api/phoneIssues", method = RequestMethod.POST)
    public IssueSubmitResponse SubmitPhoneIssue(@RequestBody String issuestr) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        IssueSubmitResponse issueSubmitResponse = new IssueSubmitResponse();
        try {
            Issue issue = new Issue();
            Gson gson = new Gson();
            issue = gson.fromJson(issuestr, Issue.class);
            //校验文件扩展名是否符合规范，不可上傳這些副檔名.js、.jar、.bat、.cpl、.scr、.com、.pif、.vbs、.exe、.cmd、.reg、.lnk、.hta
            if(DgwFileUtils.checkFileName(issue.getIssueAttachments())){
                issueSubmitResponse.setCode("1");
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("问题提交失败：附件不符合要求");
                return issueSubmitResponse;
            }
            if (issue.getSubmitWay().equals("CTI_Phone")) { //判別是否由CTI智能客服立案
                issue.setSyncStatus("N");//未同步
                issue.setCcUser("");//抄送
                String issueStaus = "";
                if (org.apache.commons.lang.StringUtils.isNotEmpty(issue.getIssueStatus())) {
                    if (issue.getIssueStatus().equals("Y")) {
                        issueStaus = issue.getIssueStatus();
                    }
                }
                // 20220812 明助說會請大人物傳email，所以當有email時直接以此找userId與userId(若找不到就不管)，若無email或不符合email格式就藉由電話查找email
                // 找出可能的使用者email與userId
                issueService.CompleteUserContact(issue);
                //完善反映者資訊
                UserContact uc = issue.getUserContact();
                uc.setQq("");
                //20200709 客戶聯繫資訊，傳什麼就存什麼，不做其他查詢，僅在EST案件同步回寫雲管家時若有Email時才找UserId
                if (uc.getName() == null || uc.getName().isEmpty()) {
                    uc.setName("待確認");//無任何客戶名稱資訊，所以存待確認
                }
                if (uc.getEmail() == null || uc.getEmail().isEmpty()) {
                    uc.setEmail("<EMAIL>");//無mail資訊，所以存Unknown@待確認.com，因同步至CRM會檢查email格式所以要使用Unknown@待確認.com
                }
                if ((uc.getPhone01() == null || uc.getPhone01().isEmpty()) && (uc.getExtension() == null || uc.getExtension().isEmpty()) && (uc.getPhone02() == null || uc.getPhone02().isEmpty())) {
                    uc.setPhone01("待確認");//無任何聯繫電話資訊就存待確認
                } else if ((uc.getPhone01() != null && !uc.getPhone01().isEmpty())) {
                    uc.setPhone01(uc.getPhone01());//存電話
                    if (uc.getExtension() != null && !uc.getExtension().isEmpty()) {
                        uc.setPhone02(uc.getExtension());//存分機
                    } else {
                        uc.setPhone02("");//存分機
                    }
                } else if ((uc.getPhone01() == null || uc.getPhone01().isEmpty()) && (uc.getPhone02() != null && !uc.getPhone02().isEmpty())) {
                    uc.setPhone01(uc.getPhone02());//存手機
                    uc.setPhone02("");
                } else if ((uc.getPhone01() == null || uc.getPhone01().isEmpty()) && (uc.getPhone02() == null || uc.getPhone02().isEmpty()) && (uc.getExtension() != null && !uc.getExtension().isEmpty())) {
                    uc.setPhone02(uc.getExtension());//存分機
                }
                /*
                                    String mail="";
                                    //判別所傳聯繫電話(電話(phone01)、分機(extension)、手機(phone02))是否都為空
                                    //因紀錄客戶聯繫資訊依現行僅記錄phone01(電話)、phone02(分機)，故後續要將聯繫資料存入phone01、phone02
                                    if((uc.getPhone01() ==null || uc.getPhone01().isEmpty()) && (uc.getExtension() ==null || uc.getExtension().isEmpty()) && (uc.getPhone02() ==null || uc.getPhone02().isEmpty()))
                                    {
                                        //客戶不提供電話時（告知直接聯絡公司總機或窗口）：若沒有聯絡人電話，CRM立案時需在反應者增加註記（待確認）並聯絡電話預設為客戶基本資料的公司電話。
                                        uc.setName(uc.getName()+ "(代確認)");
                                        //由客代找出客戶公司的電話
                                        String phone = issueService.findDefaultPhonebyServiceCode(issue.getServiceCode());
                                        uc.setPhone01(phone);
                                    }  else {
                                        boolean toBeConfirmed=false;
                                        //客戶有提供電話時：依照聯絡人電話，CRM立案時需預設反應者以往記錄的電子郵件
                                        //由客代、客戶聯繫方式找出mail找UserId
                                        mail=issueService.findMailbyPhone(issue.getServiceCode(),uc.getPhone01(),uc.getExtension(),uc.getPhone02());
                                        if(mail == ""){
                                            //表示由聯繫電話找不到mail
                                            toBeConfirmed=true;
                                            //uc.setName(uc.getName()+ "(代確認)");
                                        }
                                        //因在issue中只會紀錄phone01的電話，所以當phone01為空時將phone02的值放置phone01中，若phone01中的值不為空，則要與extension組合存入phone01中
                                        if((uc.getPhone01() !=null && !uc.getPhone01().isEmpty()) && (uc.getExtension() !=null  && !uc.getExtension().isEmpty())){
                                            uc.setPhone01(uc.getPhone01());//存電話
                                            uc.setPhone02(uc.getExtension());//存分機
                                        } else if((uc.getPhone01() ==null || uc.getPhone01().isEmpty()) && (uc.getExtension() !=null && !uc.getExtension().isEmpty())){
                                            //表示僅有分機，則找出客戶公司電話
                                            String phone = issueService.findDefaultPhonebyServiceCode(issue.getServiceCode());
                                            if(phone == null || phone.isEmpty()) {
                                                toBeConfirmed=true;
                                                //uc.setName(uc.getName()+ "(代確認)");
                                                if(uc.getPhone02()!=null && !uc.getPhone02().isEmpty()){
                                                    uc.setPhone01(uc.getPhone02());//存手機
                                                    uc.setPhone02("");
                                                }
                                                else {
                                                    uc.setPhone01("#" + uc.getExtension());//找不到客戶公司電話，所以只存分機
                                                    uc.setPhone02("");
                                                }
                                            }
                                            else {
                                                uc.setPhone01(phone);//存電話
                                                uc.setPhone02(uc.getExtension());//存分機
                                            }
                                        }
                                        else if((uc.getPhone01() == null || uc.getPhone01().isEmpty()) && !uc.getPhone02().isEmpty()){
                                            uc.setPhone01(uc.getPhone02());//存手機
                                            uc.setPhone02("");
                                        }
                                        if(toBeConfirmed==true){
                                            uc.setName(uc.getName()+ "(代確認)");
                                        }
                                    }
                                    uc.setEmail(mail);
                                    //由客代、mail找出mars_user中的userId
                                    String newUserId= mail!="" ? issueService.findUserIdbyMail(mail,issue.getServiceCode()) :"";
                                */
                /*String newUserId ="";
                issue.setUserId(newUserId);
                uc.setUserId(newUserId);*/
                issue.setUserContact(uc);

                //紀錄立案
                int res = issueService.SubmitIssue(issue);
                if (res == 0) {
                    issueSubmitResponse.setCode("0");
                    Long issueId = issue.getIssueId();
                    issueSubmitResponse.setData(Long.toString(issueId));
                    System.out.print("Before do SubmitIssueAsync" + issueId);
                    issueService.SubmitIssueAsync(issue, issueStaus, true);
                    System.out.print("Before do SubmitIssueAsync" + issueId);
                } else {
                    issueSubmitResponse.setCode(Integer.toString(res));
                    issueSubmitResponse.setData("-1");
                    issueSubmitResponse.setErrMsg("電話立案提交失败");
                }
            } else {
                issueSubmitResponse.setCode("1");
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg("電話立案提交失败");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            issueSubmitResponse.setCode("1");
            issueSubmitResponse.setData("-1");
            issueSubmitResponse.setErrMsg("電話立案提交失败：" + ex.getMessage());
            log.error(ex);
        }
        return issueSubmitResponse;
    }

    public static String mapState(String state) {
        if (state == null) {
            return "无状态";
        }
        switch (state) {
            case "C":
                return "等待客服处理";
            case "T":
                return "待审核";
            case "N":
                return "处理中";
            case "Y":
                return "已结案";
            case "R":
                return "已处理";
            default:
                return "位置状态" + state;
        }
    }

    private String truncateDescription(String desc) {
        int length = 1000;
        if (desc == null) return null;
        if (desc.length() > length) {
            return desc.substring(0, length) + "....";
        } else {
            return desc;
        }
    }

    private String truncateCreatedTime(String time) {
        if (time == null) {
            return null;
        } else {
            if (time.lastIndexOf(".") != -1) {
                return time.substring(0, time.lastIndexOf("."));
            } else {
                return time;
            }
        }
    }

    @ApiOperation(value = "获取案件列表（針對某一来源、某一任務、案件状态做查詢）")
    @GetMapping(value = "/getIssueSourceMapList")
    public BaseResponse getIssueSourceMapList(@RequestParam(required = false) String issueStatus,
                                              @RequestParam(required = false, defaultValue = "warning") String sourceType,
                                              @RequestParam(required = false) String sourceId,
                                              @RequestParam(required = false) String userId) {
        try {
            return BaseResponse.ok(issueService.getIssueSourceMapList(issueStatus, sourceType, sourceId, userId));
        } catch (Exception ex) {
            log.error(ex.toString());
            return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
        }
    }

    @ApiOperation(value = "获取案件汇总量（处理中，已结案）,EMC开头的案件")
    @GetMapping(value = "/api/issue/sum")
    public BaseResponse getIssueSum(@RequestParam("authuserid") String userId,
                                    @RequestParam("authuserdept") String department,
                                    @RequestParam(required = false, defaultValue = "") String serviceCode) {
        try {
            return BaseResponse.ok(issueService.getIssueSum(userId, department, serviceCode));
        } catch (Exception ex) {
            log.error(ex.toString());
            return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
        }
    }

    @ApiOperation(value = "解决自动化测试更新同步状态Z=>X")
    @GetMapping(value = "/updateSyncStatus4Test")
    public BaseResponse updateSyncStatus4Test(@RequestParam String issueId) {
        try {
            return BaseResponse.ok(issueService.updateSyncStatus4Test(issueId));
        } catch (Exception ex) {
            log.error(ex.toString());
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //批次檢查同步callcenter失敗的案件重新同步
    @ApiOperation(value = "CallCenter未同步案件")
    @RequestMapping(value = "/api/issues/sync/callcenter/tw", method = RequestMethod.GET)
    public void syncCallCenterIssues() {
        try {
            issueService.SyncCallCenterIssues();
        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
    }

    @GetMapping(value = "/v2/api/issues/full/content/search")
    public List<Issue> issueFullContentSearch(
            @RequestParam("msgContent") String msgContent,
            @RequestParam("userId") String userId,
            @RequestParam("serviceCode") String serviceCode,
            @RequestParam("productCode") String productCode,
            @RequestParam("localId") String localId) {
        try {
            return issueService.issueFullContentSearch(msgContent, userId, serviceCode, productCode, localId);
        } catch (Exception ex) {
            log.error(ex);
            return null;
            //return  BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR);
        }


    }

    @ApiOperation(value = "客服于客服易聊新增留言")
    @RequestMapping(value = "/api/issues/service/contact/{issueId}", method = RequestMethod.POST)
    public BaseResponse addServiceContact(@PathVariable("issueId") long issueId,
                                          @RequestParam("userId") String userId,
                                          @RequestBody IssueProgress issueProgress) {
        issueProgress.setIssueId(issueId);
        issueProgress.setProcessor(userId);
        return issueService.addServiceContact(issueProgress);
    }

    @ApiOperation(value = "查看案件的所有留言")
    @RequestMapping(value = "/api/issues/{issueId}/service/contacts", method = RequestMethod.GET)
    public BaseResponse getServiceContacts(@PathVariable("issueId") long issueId) {
        return issueService.getServiceContacts(issueId);
    }

    @ApiOperation(value = "查看案件的所有程式更新")
    @RequestMapping(value = "/api/issues/{issueId}/self/update/system/files", method = RequestMethod.GET)
    public BaseResponse getSelfUpdateSystemFiles(@PathVariable("issueId") long issueId,
                                                 @RequestParam(value = "invalidStatus", required = false, defaultValue = "") String invalidStatus) {
        return issueService.getSelfUpdateSystemFiles(issueId, invalidStatus);
    }

    @ApiOperation(value = "查看案件的某个程式更新的操作记录，区分客服/客户查看的操作记录会不一样")
    @RequestMapping(value = "/api/issues/{isusfId}/self/update/system/file/logs", method = RequestMethod.GET)
    public BaseResponse getSelfUpdateSystemFileLogs(@PathVariable("isusfId") long isusfId,
                                                    @RequestParam(value = "isClient", required = false, defaultValue = "") String isClient) {
        return issueService.getSelfUpdateSystemFileLogs(isusfId, isClient);
    }

    @ApiOperation(value = "程式更新/授权安装操作记录log")
    @RequestMapping(value = "/api/issues/{issueId}/self/update/system/log", method = RequestMethod.POST)
    public BaseResponse insertSelfUpdateSystemFile(@PathVariable("issueId") long issueId,
                                                   @RequestBody IssueSelfUpdateSystemLog issueSelfUpdateSystemLog) {
        return issueService.insertSelfUpdateSystemLog(issueId, issueSelfUpdateSystemLog);
    }

    @ApiOperation(value = "SFT审核")
    @RequestMapping(value = "/api/issues/{crmId}/sft/check", method = RequestMethod.POST)
    public BaseResponse sftCheck(@PathVariable("crmId") String crmId,
                                 @RequestParam(value = "approve", required = false, defaultValue = "1") int approve,
                                 @RequestParam(value = "maderWorkNo", required = false) String maderWorkNo,
                                 @RequestParam(value = "description", required = false) String description) {
        return issueService.sftCheck(crmId, approve, maderWorkNo, description);
    }

    @ApiOperation(value = "程式更新/授权安装等操作，会记录案件单身")
    @RequestMapping(value = "/api/issues/{issueId}/self/update/system/file/log", method = RequestMethod.POST)
    public BaseResponse insertIssueSelfUpdateSystemLog(@PathVariable("issueId") long issueId,
                                                       @RequestParam(value = "isusfId", required = false, defaultValue = "0") long isusfId,
                                                       @RequestParam("userId") String userId,
                                                       @RequestBody IssueSelfUpdateSystemLogReq issueSelfUpdateSystemLogReq) {
        return issueService.insertIssueSelfUpdateSystemLog(issueId, isusfId, userId, issueSelfUpdateSystemLogReq);
    }

    @ApiOperation(value = "留言查看后，标记已读")
    @RequestMapping(value = "/api/issues/{issueId}/service/contact/read", method = RequestMethod.PUT)
    public BaseResponse updateServiceContactRead(@PathVariable("issueId") long issueId,
                                                 @RequestParam("userId") String userId) {
        return issueService.updateServiceContactRead(issueId, userId);
    }

    @ApiOperation(value = "序号安装/程式更新，标记已读")
    @RequestMapping(value = "/api/issues/{issueId}/self/update/system/file/read", method = RequestMethod.PUT)
    public BaseResponse updateSystemFileRead(@PathVariable("issueId") long issueId,
                                             @RequestParam("userId") String userId) {
        return issueService.updateSystemFileRead(issueId, userId);
    }

    @ApiOperation(value = "客户定时器：台湾四大产品线，取约定日之客服留言并写入相关资料表")
    @RequestMapping(value = "/api/issues/agreedate/reply", method = RequestMethod.POST)
    public void replyToContact() {
        try {
            issueService.replyToContact();
        } catch (Exception ex) {
            log.error("replyToContact fail:{}", ex);
        }
    }

    @ApiOperation(value = "客服定时器：台湾四大产品线，取约定日当天和明天的案件，并推播给客服与支援人员")
    @RequestMapping(value = "/api/issues/agreedate/boardcast", method = RequestMethod.POST)
    public void boardcastIssueToService() {
        try {
            issueService.boardcastIssueToService();
        } catch (Exception ex) {
            log.error("boardcastIssueToService fail:{}", ex);
        }
    }

    /**
     * 判断是否是代理商
     *
     * @param productCode
     * @return
     */
    @GetMapping(value = "/api/customer/agent/check")
    public BaseResponse checkAgent(@RequestParam("serviceCode") String serviceCode,
                                   @RequestParam(value = "productCode", required = false, defaultValue = "") String productCode) {
        int result = 0;
        try {
            result = issueService.checkAgent(serviceCode, productCode);
        } catch (Exception ex) {
            log.error(ex);
        }
        return BaseResponse.ok(result > 0 ? true : false);

    }

    /**
     * 返回代理商的核对案件量 已使用案件量 以及使用率
     *
     * @param productCode
     * @return
     */
    @GetMapping(value = "/api/customer/agent/count")
    public BaseResponse selectAgentIssueSumarry(@RequestParam("serviceCode") String serviceCode,
                                                @RequestParam("productCode") String productCode) {
        return BaseResponse.ok(issueService.selectAgentIssueSumarry(serviceCode, productCode));
    }

    @ApiOperation(value = "查看案件的客戶資訊")
    @RequestMapping(value = "/api/issues/{issueId}/customer/info", method = RequestMethod.GET)
    public BaseResponse getIssueCustomerInfo(@PathVariable("issueId") long issueId) {
        return issueService.getIssueCustomerInfo(issueId);
    }

    /**
     * 各产品未结案之案件统计数 (含总未结案数量)-圆环图
     * 未结案的状态: 11,12,2,22,23,24,25,26,27,29,3,30,33,34,4,8,C,D,I,N,O,Q,R ,T  -> 不包含(Y,7,10,P)
     **/
    @RequestMapping(value = "/api/issues/unresolved", method = RequestMethod.GET)
    public BaseResponse selectUnresolvedIssueList(@RequestParam("authuserid") String userId,
                                                  @RequestParam(value = "product", required = false, defaultValue = "") String productCode,
                                                  @RequestParam("authuserdept") String department,
                                                  @RequestParam(value = "accessToken", required = false, defaultValue = "") String queryUserId,
                                                  @RequestParam(value = "customerServiceCode", required = true) String customerServiceCode) {
        return BaseResponse.ok(issueService.selectUnresolvedIssueList(userId, productCode, department, queryUserId, customerServiceCode));
    }

    /**
     * 案件統計API--折線圖用
     **/
    @RequestMapping(value = "/api/issues/statistic", method = RequestMethod.GET)
    public BaseResponse selectIssueStatisticList(@RequestParam("authuserid") String userId,
                                                 @RequestParam(value = "product", required = false, defaultValue = "") String productCode,
                                                 @RequestParam("authuserdept") String department,
                                                 @RequestParam(value = "startDate", required = true) String startDate,
                                                 @RequestParam(value = "endDate", required = true) String endDate,
                                                 @RequestParam(value = "customerServiceCode", required = true) String customerServiceCode,
                                                 @RequestParam(value = "accessToken", required = false, defaultValue = "") String queryUserId) {
        return BaseResponse.ok(issueService.selectIssueStatisticList(userId, productCode, department, startDate, endDate, customerServiceCode, queryUserId));
    }

    /**
     * 各產品案件統計API--直方圖
     **/
    @RequestMapping(value = "/api/issues/productCode", method = RequestMethod.GET)
    public BaseResponse selectIssueProductCodeStatisticList(@RequestParam("authuserid") String userId,
                                                            @RequestParam(value = "product", required = false, defaultValue = "") String productCode,
                                                            @RequestParam("authuserdept") String department,
                                                            @RequestParam(value = "startDate", required = true) String startDate,
                                                            @RequestParam(value = "endDate", required = true) String endDate,
                                                            @RequestParam(value = "customerServiceCode", required = true) String customerServiceCode,
                                                            @RequestParam(value = "accessToken", required = false, defaultValue = "") String queryUserId) {
        return BaseResponse.ok(issueService.selectIssueProductCodeStatisticList(userId, productCode, department, startDate, endDate, customerServiceCode, queryUserId));
    }
    @ApiOperation(value = "客服标记ChatFile回答赞不赞")
    @RequestMapping(value = "/api/issues/{issueId}/chatfilehelp/{chatfileHelp}", method = RequestMethod.PUT)
    public BaseResponse updateIssueKbShareChatFileHelp(@PathVariable("issueId") long issueId,
                                                 @PathVariable("chatfileHelp") String chatfileHelp) {
        if(StringUtils.isEmpty(chatfileHelp) || !("T".equals(chatfileHelp) || "F".equals(chatfileHelp))){
            return  BaseResponse.error(ResponseStatus.WRONG_PARAMETER);
        }
        return issueService.updateIssueKbShareChatFileHelp(issueId, chatfileHelp);
    }
    @ApiOperation(value = "變更提交人")
    @RequestMapping(value = "/api/issues/changesubmiterhistory", method = RequestMethod.PUT)
    public BaseResponse updateIssueSubmiterHistory(@RequestBody IssueChangeSubmiterHistory issueChangeSubmiterHistory) {
        return issueService.updateUserId(issueChangeSubmiterHistory);
    }

    @ApiOperation(value = "案件編號查詢提案人變更歷程")
    @RequestMapping(value = "/api/issues/select/changesubmiterhistory", method = RequestMethod.GET)
    public BaseResponse selectIssueChangeSubmiterHistory(@RequestParam(value = "issueId", required = false) Long issueId) {
        return issueService.selectIssueChangeSubmiterHistory(issueId);
    }

    @ApiOperation(value = "查詢提案人列表")
    @RequestMapping(value = "/api/issues/select/submit", method = RequestMethod.GET)
    public BaseResponse selectIssueByServiceCode(@RequestParam(value = "serviceCode", required = false) String serviceCode) {
        return issueService.selectIssueByServiceCode(serviceCode);
    }
}
