package com.digiwin.escloud.aiocmdb.asset.utils;

import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSimple;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

public class AssetNoUtil {
    private final static AssetNoUtil anoUtil = new AssetNoUtil();

    private AssetNoUtil() {
    }

    public static AssetNoUtil getInstance() {
        return anoUtil;
    }

    public String getAssetNo(AssetCategoryCodingRuleSimple[] ruleSimples) {
        String prefixCode = ruleSimples[0].getMainCode();
        String calFn = Arrays.stream(ruleSimples)
                .map(rule -> {
                    if (rule.getRuleNumber() == AssetNoProduceType.SERIAL_NUMBER) {
                        return rule.getRuleNumber().execute(ruleSimples);
                    }
                    return rule.getRuleNumber().execute(rule);
                })
                .collect(Collectors.joining("-"));
        return prefixCode + "-" + calFn;
    }

    /**
     * 根據提供的編碼規則和生產數量生成資產編號列表。
     *
     * @param ruleSimples 一個{@code AssetCategoryCodingRuleSimple}對象的數組表示生成資產編號的編碼規則。
     * @param produceCount 要生成的資產編號數量。
     * @return 生成的資產號列表為 {@code List <string>}。
     */
    public List<String> getMultiAssetNo(AssetCategoryCodingRuleSimple[] ruleSimples, long produceCount) {
        List<String> assetNoList = new ArrayList<>();

        AssetCategoryCodingRuleSimple[] calRuleSamples = this.deepClone(ruleSimples);
        LongStream.range(0, produceCount).forEach(i -> {
            String assetNo = getAssetNo(calRuleSamples);
            assetNoList.add(assetNo);
            Arrays.stream(calRuleSamples)
                    .forEach(rule -> rule.setCurrentFlowNumber(assetNo));
        });

        return assetNoList;
    }

    public long setAssetNoRuleInfo(
            List<AssetCategoryCodingRuleSimple> codingRuleSimples, String mainCode, String curFlowNumber,
            Function<List<AssetCategoryCodingRuleSimple>, Long> calAlreadyUsedCountFunc
    ) {
        codingRuleSimples.forEach(row -> {
            row.setMainCode(mainCode);
            row.setCurrentFlowNumber(curFlowNumber);
        });
        return calAlreadyUsedCountFunc.apply(codingRuleSimples);
    }

    public AssetCategoryCodingRuleSimple[] deepClone(AssetCategoryCodingRuleSimple[] original) {
        if (original == null) {
            return null;
        }

        AssetCategoryCodingRuleSimple[] copy = new AssetCategoryCodingRuleSimple[original.length];
        for (int i = 0; i < original.length; i++) {
            if (original[i] != null) {
                copy[i] = new AssetCategoryCodingRuleSimple();
                // 複製所有屬性
                copy[i].setMainCode(original[i].getMainCode());
                copy[i].setCurrentFlowNumber(original[i].getCurrentFlowNumber());
                copy[i].setRuleNumber(original[i].getRuleNumber());
                copy[i].setRuleSettingValue(original[i].getRuleSettingValue());
                // 根據實際屬性添加更多的 setter
            }
        }
        return copy;
    }

//
//    public static void main(String[] args) {
//        String currentFlowNumber = "HW202507196-00001";
//
//        AssetCategoryCodingRuleSimple accrs1 = new AssetCategoryCodingRuleSimple();
//        accrs1.setMainCode("HW");
//        accrs1.setCurrentFlowNumber(currentFlowNumber);
//        accrs1.setRuleNumber(AssetNoProduceType.DATE);
////        accrs1.setRuleSettingValue("yyyyMMdd");
//        accrs1.setRuleSettingValue("YYYYMMDD");
//
////        AssetCategoryCodingRuleSimple accrs2 = new AssetCategoryCodingRuleSimple();
////        accrs2.setMainCode("HW");
////        accrs2.setCurrentFlowNumber(currentFlowNumber);
////        accrs2.setRuleNumber(AssetNoProduceType.TEXT);
////        accrs2.setRuleSettingValue("::Asam::");
//
//        AssetCategoryCodingRuleSimple accrs3 = new AssetCategoryCodingRuleSimple();
//        accrs3.setMainCode("HW");
//        accrs3.setCurrentFlowNumber(currentFlowNumber);
//        accrs3.setRuleNumber(AssetNoProduceType.SERIAL_NUMBER);
//        accrs3.setRuleSettingValue("5");
//
////        AssetCategoryCodingRuleSimple[] mainRules = new AssetCategoryCodingRuleSimple[]{accrs1, accrs2, accrs3};
//        AssetCategoryCodingRuleSimple[] mainRules = new AssetCategoryCodingRuleSimple[]{accrs1, accrs3};
//
//        String kk = AssetNoUtil.getInstance().getAssetNo(mainRules);
//        System.out.println(kk);
//    }
}
