package com.digiwin.escloud.aiocmdb.assetmaintenancev2.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ReverseRelatedAsset extends AssetRelatedMap{

    @ApiModelProperty("被关联模型Code")
    private String beAssociatedModelCode;

    @ApiModelProperty("被关联模型 sinkName")
    private String beAssociatedSinkName;

    @ApiModelProperty("被关联资产ID")
    private long beAssociatedAssetId;

    @ApiModelProperty("当前资产模型Code")
    private String currentAssetModelCode;

    @ApiModelProperty("当前资产 sinkName")
    private String currentAssetSinkName;

    @ApiModelProperty("当前资产ID")
    private long currentAssetId;
}
