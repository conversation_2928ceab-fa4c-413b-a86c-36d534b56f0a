package com.digiwin.escloud.aiocmdb.assetchange.service;

import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationSaveRequest;
import com.digiwin.escloud.common.model.ResponseBase;

/**
 * <p>
 * 资产变更申请单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
public interface IAssetChangeApplicationService {

    /**
     * 新增/保存资产变更申请单
     * 在一个事务中保存主表及所有子表信息
     *
     * @param request 保存请求
     * @return 响应结果
     */
    ResponseBase saveAssetChangeApplication(AssetChangeApplicationSaveRequest request);

    /**
     * 删除资产变更申请单
     *
     * @param id 申请单id
     * @return 响应结果
     */
    ResponseBase deleteAssetChangeApplication(String id);
}
