@startuml
title 网安稽查报告生成完整时序图 (基于实际代码分析)

participant "NetworkSecurityExamServiceImpl" as NetworkService
participant "AiopsExamService" as ExamService  
participant "AiopsExamRecordMapper" as ExamMapper
participant "NetworkSecurityExamMapper" as NetworkMapper
participant "DbReportService" as DbService
participant "DbDataFactory" as DbFactory
participant "BigDataUtil" as BigData
participant "WarningServiceImpl" as WarningService
participant "RestUtil" as RestUtil
participant "NetworkSecurityExamEsReport" as EsReport
participant "MySQL" as MySQL
participant "ElasticSearch" as ES
participant "StarRocks" as StarRocks
participant "后台服务(AI服务)" as AIService

== 1. 创建报告记录 ==

NetworkService -> ExamService: insertAiopsExamRecordsReportRecord(reportRecord)
ExamService -> MySQL: INSERT INTO aiops_exam_records_report_record
MySQL --> ExamService: 返回插入的记录ID
ExamService --> NetworkService: BaseResponse<AiopsExamRecordsReportRecord>

== 2. 异步报告生成流程 ==

NetworkService -> NetworkService: CompletableFuture.runAsync(() -> saveNetworkSecurityExamReport2Es(data))
activate NetworkService

note right of NetworkService: 获取请求上下文信息
NetworkService -> NetworkService: RequestUtil.getHeaderAppCode()
NetworkService -> NetworkService: record.getEid()

== 3. 查询组织类数据 (新流程中将删除) ==

NetworkService -> NetworkService: getProjectType(NETWORK_SECURITY_EXAM_PORJECT_ORGANIZATIONAL, null, null, true, null, eid)

NetworkService -> NetworkMapper: selectProjectType(categoryCode, categoryName, parentCode, filterNullModel, null)
NetworkMapper -> MySQL: SELECT * FROM network_security_examination_project_type WHERE category_code = 'Organizational'
MySQL --> NetworkMapper: List<NetworkSecurityExaminationProjectType>
NetworkMapper --> NetworkService: List<NetworkSecurityExaminationProjectType>

NetworkService -> NetworkService: getModelCodeListFromProjectTypes(projectTypes)
note right: 提取模型代码列表

NetworkService -> NetworkService: queryModelCount(modelCodeList, eid)
NetworkService -> BigData: srQuery(buildUnionSql(...))
note right: 构建UNION SQL查询各模型数据计数\nSELECT 'modelCode1' as modelCode, COUNT(1) cnt FROM servicecloud.ModelCode1 WHERE isDeleted = 0 and eid = ?
BigData -> StarRocks: 执行UNION查询
StarRocks --> BigData: 返回计数结果
BigData --> NetworkService: List<Map<String, Object>>

NetworkService -> NetworkService: queryModelDetail(modelCodeList, whereClause)
NetworkService -> BigData: srQuery(buildUnionSql(...))
note right: 查询组织类详细数据\nSELECT * FROM servicecloud.ModelCode WHERE modelPk = modelPkValue
BigData -> StarRocks: 执行详细数据查询
StarRocks --> BigData: 返回详细数据
BigData --> NetworkService: List<Map<String, Object>> organizationalDataList

NetworkService -> RestUtil: getModelDetail(modelCode)
RestUtil --> NetworkService: BaseResponse<ModelDetail>

== 4. 处理模型数据 ==

NetworkService -> NetworkService: doModelData(organizationalData, modelDetail, record)

loop 处理每个ModelFieldGroup
    NetworkService -> NetworkService: 创建OrganizationalModelMap
    
    loop 处理每个ModelFieldMapping
        alt 如果是FieldSet类型
            loop 处理每个subfield
                NetworkService -> NetworkService: 创建subDataMap
                note right: 设置name, fieldSetName, fieldSetCode, code, value等\nvalue = organizationalData.get(subfield.getFieldCode())
            end
        else 如果是单个Field
            NetworkService -> NetworkService: 创建dataMap
            note right: 设置name, code, value, sort等\nvalue = organizationalData.get(field.getFieldCode())
        end
    end
    
    alt 如果是DataContent分组
        NetworkService -> NetworkService: 按fieldSetName分组处理
        NetworkService -> NetworkService: createDataContent()
        NetworkService -> NetworkService: mergeAdjacentElements()
    else 其他分组
        NetworkService -> NetworkService: 直接添加到organizationalModelMapList
    end
end

NetworkService -> NetworkService: record.setOrganizationalData(model)

== 5. 查询体检记录信息 ==

NetworkService -> ExamMapper: selectAiopsExamRecordReport(record.getAerId())
ExamMapper -> MySQL: SELECT * FROM aiops_exam_record WHERE id = ?
MySQL --> ExamMapper: AiopsExamRecord
ExamMapper --> NetworkService: AiopsExamRecord

== 6. 查询预警数据 ==

NetworkService -> WarningService: getDailyWarningInfo(appCode, eid, startDate, endDate)
activate WarningService

WarningService -> WarningService: 构建预警查询SQL
note right: StringBuilder sbSql = new StringBuilder()\nSELECT date_format(wd.warningtime, '%Y-%m-%d') AS warningDate,\nwd.warningLevel AS level, COUNT(*) AS count\nFROM {dbName}warning w\nINNER JOIN {dbName}warningdetail wd ON w.key = wd.warningrowkey\nWHERE w.eid = '{eid}' AND wd.warningtime BETWEEN '{startDate}' AND '{endDate}'\nGROUP BY warningDate, level ORDER BY warningDate

WarningService -> BigData: srQuery(warningSQL)
BigData -> StarRocks: 执行预警统计查询
StarRocks --> BigData: 返回预警统计数据
BigData --> WarningService: List<Map<String, Object>>

WarningService --> NetworkService: List<Map<String, Object>> warningData
deactivate WarningService

NetworkService -> NetworkService: record.setWarningData(warningData)
NetworkService -> NetworkService: record.setGenerationTime(LocalDateTime.now())

== 7. 数据库报告服务处理 (新流程中的数据库报告方案) ==

note over NetworkService, AIService: 根据活动图，新流程将使用数据库报告服务查询指标检测值和参考值

alt 如果record.getType() == "Database"
    NetworkService -> DbService: getDbReportData(String.valueOf(record.getId()), dbType, productCode)
    activate DbService

    DbService -> DbService: getCurrentType(dbType, productCode)
    note right: 确定报告类型\nif (StringUtils.isBlank(productCode)) return dbType\nelse return productCode + "_" + dbType

    DbService -> DbFactory: getDbReportData(id, dbTypeCode)
    activate DbFactory

    DbFactory -> DbFactory: getDbReportClass(dbTypeCode)
    note right: 获取对应的报告类型Class

    DbFactory -> ES: esService.findById(id, clazz)
    note right: 从ES查询数据库报告数据
    ES --> DbFactory: DefaultOracleDbReport
    DbFactory --> DbService: Object dbReportData
    deactivate DbFactory

    alt 如果是DefaultOracleDbReport类型
        note over DbService: 处理检测值和参考值数据

        DbService -> DbService: processActualValue.processValue(avDefaultValue, targetKey)
        note right: 处理检测值内容\n按规则拼接检测值 如 c: 10% d: 20%\n需重写ProcessActualValue接口的行为

        DbService -> BigData: srQuery(sql)
        note right: 查询StarRocks获取指标检测值\n使用bigDataUtil查询实时数据
        BigData -> StarRocks: 执行指标检测值查询
        StarRocks --> BigData: 返回检测值数据
        BigData --> DbService: List<Map<String, Object>>

        DbService -> MySQL: 查询参考值数据
        note right: 从report_reference_value表查询参考值
        MySQL --> DbService: ReportReferenceValue

        DbService -> DbService: 整理参考值、检测值数据
        note right: 按照一定规则拼接检测值\n比如 c: 10% d: 20% 需要重写 ProcessActualValue 接口的行为
    end

    DbService --> NetworkService: DefaultOracleDbReport
    deactivate DbService

    NetworkService -> NetworkService: report.getAssessmentConclusion()
    NetworkService -> NetworkService: record.setExamReportContent(resSuggestion)
end

== 8. AI服务生成报告结论 (新流程中将添加) ==

note over NetworkService, AIService: 根据活动图，新流程将调用AI服务生成报告结论

NetworkService -> NetworkService: 组合报告数据和分数数据、预警数据

NetworkService -> AIService: 发起报告结论生成请求
note right: 调用aioai项目的AI服务\n发起生成报告结论API
activate AIService

AIService -> AIService: 发起生成报告结论API
note right: 调用IndepthAI服务生成结论

AIService -> AIService: 阻塞等待报告结论
note right: 等待第三方IndepthAI服务响应\n类似ChatGptService.EvaluationConclusion的实现\ncountDownLatch.await(5, TimeUnit.MINUTES)

AIService --> NetworkService: 返回报告结论
deactivate AIService

== 9. 存储报告到ES ==

NetworkService -> EsReport: generateReport(record)
activate EsReport

EsReport -> EsReport: BeanUtils.copyProperties(reportRecord, report)
EsReport -> EsReport: report.setId(reportRecord.getId() + "")
EsReport -> EsReport: report.setReportType("Complete")

EsReport -> ES: save(report, false)
note right: 存储报告详情到ES\n新流程中不再存储组织类数据\n现在报告指标分数数据实时查询，因检测值的不同查询逻辑需存储ES
ES --> EsReport: AiopsExamRecordsEsReportRecord

EsReport --> NetworkService: String reportId
deactivate EsReport

== 10. 更新报告状态 ==

NetworkService -> ExamMapper: updateReportStatus(record.getId(), ReportStatus.UNDER_EVA.getIndex(), LocalDateTime.now())
ExamMapper -> MySQL: UPDATE aiops_exam_records_report_record SET status = ?, generation_time = ? WHERE id = ?
note right: 更新报告记录状态为评估中
MySQL --> ExamMapper: 更新成功
ExamMapper --> NetworkService: 更新完成

deactivate NetworkService

== 补充：体检记录分数查询详细流程 ==

note over NetworkService, MySQL: getReportScore方法的详细实现 (后台服务-一键体检服务)

NetworkService -> ExamService: getReportScore(record.getAerId(), scale)
activate ExamService

ExamService -> ExamMapper: selectAiopsExamRecordReport(aerId)
ExamMapper -> MySQL: SELECT * FROM aiops_exam_record WHERE id = ?
MySQL --> ExamMapper: AiopsExamRecord
ExamMapper --> ExamService: AiopsExamRecord

ExamService -> ExamService: aiopsExamService.getAeById(aer.getAeId())
ExamService -> MySQL: SELECT * FROM aiops_exam WHERE id = ?
MySQL --> ExamService: AiopsExam

ExamService -> ExamMapper: selectIndexTypeByAeid(aer.getAeId())
ExamMapper -> MySQL: SELECT * FROM aiops_exam_index_type WHERE ae_id = ?
MySQL --> ExamMapper: List<AiopsExamIndexType>
ExamMapper --> ExamService: List<AiopsExamIndexType>

ExamService -> ExamMapper: selectAiopsExamItem(aerId)
ExamMapper -> MySQL: SELECT aiops_item FROM aiops_exam_item_map WHERE aer_id = ?
MySQL --> ExamMapper: List<AiopsExamItemMap>
ExamMapper --> ExamService: List<AiopsExamItemMap>

ExamService -> ExamService: 过滤启用的体检项目映射
note right: enabledMaps = exam.getAeimList().stream()\n.filter(e -> aiopsItemList.contains(e.getAiopsItem()))\n.collect(Collectors.toList())

ExamService -> ExamService: 计算各指标类型分数
note right: 遍历indexTypes，计算每个指标类型的分数\n根据体检项目的权重和分数计算总分

ExamService -> ExamService: 构建AiopsExamRecordsReportRecord
note right: 设置examScore, indexTypeList, examTitle, examEnv等

ExamService --> NetworkService: BaseResponse<AiopsExamRecordsReportRecord>
deactivate ExamService

@enduml
