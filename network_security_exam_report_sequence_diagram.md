# 网安稽查报告生成时序图 (数据库报告方案)

```mermaid
sequenceDiagram
    participant NS as 后台服务(网安稽查服务)
    participant AE as 后台服务(一键体检服务)
    participant DB as 后台服务(数据库报告服务)
    participant BD as 大数据平台
    participant MySQL as Mysql
    participant ES as ES
    participant AI as 后台服务(AI服务)

    Note over NS: 异步任务开始
    NS->>NS: saveNetworkSecurityExamReport2Es(record)
    
    Note right of NS: 1. 查询体检记录分数数据
    NS->>+AE: getReportScore(aerId, scale)
    AE->>+MySQL: 查询体检记录数据
    MySQL-->>-AE: 返回分数数据
    AE-->>-NS: BaseResponse<AiopsExamRecordsReportRecord>
    
    Note right of NS: 2. 查询指标检测值数据
    NS->>+DB: getDbReportData(id, dbType, productCode)
    DB->>+BD: 查询指标检测值数据(StarRocks)
    BD-->>-DB: 返回指标检测值数据
    
    DB->>+MySQL: 查询指标参考值数据
    MySQL-->>-DB: 返回指标参考值数据
    
    Note over DB: 整理参考值、检测值数据<br/>按规则拼接检测值
    DB->>DB: processActualValue.processValue()
    
    DB-->>-NS: 返回指标检测值数据
    
    Note right of NS: 3. 查询预警数据
    NS->>+BD: warningService.getDailyWarningInfo()
    BD-->>-NS: 返回预警数据
    
    Note over NS: 组合报告数据和分数数据、预警数据
    
    Note right of NS: 4. 生成报告结论
    NS->>+AI: chatGptService.EvaluationConclusion()
    Note over AI: 发起生成报告结论API<br/>调用IndepthAI服务
    Note over AI: 阻塞等待报告结论<br/>countDownLatch.await(5分钟)
    AI-->>-NS: 返回报告结论
    
    Note right of NS: 5. 存储报告详情
    NS->>+ES: networkSecurityExamEsReport.generateReport()
    Note over ES: 存储报告详情<br/>(不再存储组织类数据)
    ES-->>-NS: 存储完成
    
    Note right of NS: 6. 更新报告记录
    NS->>+MySQL: updateReportStatus()
    MySQL-->>-NS: 更新完成
```

## 关键流程说明

### 1. 异步任务启动
- 使用 `CompletableFuture.runAsync()` 启动异步任务
- 调用 `saveNetworkSecurityExamReport2Es(record)` 方法

### 2. 查询体检记录分数数据
- 调用 `getReportScore(aerId, scale)` 方法
- 从MySQL查询体检记录、指标类型、体检项目等数据
- 返回包含分数数据的 `BaseResponse<AiopsExamRecordsReportRecord>`

### 3. 查询指标检测值和参考值数据
- 调用数据库报告服务的 `getDbReportData()` 方法
- 使用 `bigDataUtil` 查询StarRocks获取指标检测值
- 从MySQL查询参考值数据
- 使用 `ProcessActualValue` 接口处理检测值内容

### 4. 查询预警数据
- 调用 `warningService.getDailyWarningInfo()` 方法
- 从大数据平台查询预警统计数据

### 5. AI服务生成报告结论
- 调用 `chatGptService.EvaluationConclusion()` 方法
- 异步调用AI服务API
- 使用 `CountDownLatch` 阻塞等待结论生成完成（最多5分钟）

### 6. 存储报告到ES
- 调用 `networkSecurityExamEsReport.generateReport()` 方法
- 将报告数据存储到ElasticSearch
- 注意：新流程中不再存储组织类数据

### 7. 更新报告状态
- 调用 `updateReportStatus()` 更新MySQL中的报告记录状态
- 标记报告生成完成
