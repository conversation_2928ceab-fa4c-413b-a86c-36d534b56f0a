package com.digiwin.escloud.aiobasic.edr.service.edr.impl.mail;

import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.aiobasic.edr.annotation.ReportType;
import com.digiwin.escloud.aiobasic.edr.model.base.ReportRecord;
import com.digiwin.escloud.aiobasic.edr.model.edr.CustomerOrgMap;
import com.digiwin.escloud.aiobasic.edr.service.base.IAioReportMailItem;
import com.digiwin.escloud.aiobasic.freemarker.FreemarkerService;
import com.digiwin.escloud.aiobasic.util.BigDataUtil;
import com.digiwin.escloud.aiobasic.util.FreemarkerTplData;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.EncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.digiwin.escloud.aiobasic.util.MessageUtils.ZH_CN_STANDARD;
import static com.digiwin.escloud.aiobasic.util.MessageUtils.ZH_TW_STANDARD;

@Slf4j
@ReportType(15)
@Service
@RefreshScope
public class VersionUpdateAssessmentReport implements IAioReportMailItem {
    @Value("${service.area}")
    String serviceArea;
    @Value("${aiobasic.address}")
    String aiobasicAddress;
    @Value("${mis.root}")
    String misRoot;

    @Autowired
    private FreemarkerService tplService;
    @Autowired
    private BigDataUtil bigDataUtil;

    @Override
    public String getReportMailTitle(ReportRecord rm, List<CustomerOrgMap> ofOrgs) {
        boolean isCnLanguage = "CN".equals(rm.getLanguage());
        boolean isCnServiceArea = "CN".equals(serviceArea);
        String siteName = isCnServiceArea ? "智管家" : "Ai智管家";
        String companyName = isCnServiceArea ? "鼎捷" : "鼎新";
        String title = isCnLanguage ? "【%s数智】%s升版評估報告_%s_%s(系统发送请勿回复)" : "【%s數智】%s升版評估報告_%s_%s(系統發送請勿回覆)";

        return String.format(
                title,
                companyName,
                siteName,
                DateUtil.getSomeDateFormatString(DateUtil.getLocalToday(), DateUtil.DATE_FORMATTER),
                rm.getCustomerName());
    }

    @Override
    public String getReportMailContent(ReportRecord rm, List<CustomerOrgMap> ofOrgs) {
        try {
            String language = rm.getLanguage();
            boolean isCnLanguage = "CN".equals(language);
            String description = StringUtils.isEmpty(rm.getDescription()) ? "" : rm.getDescription();
            String hrefAddress = getHrefAddress(aiobasicAddress, rm);
            String misAddress = getMisAddress(misRoot);
            String downloadAddress = "CN".equals(serviceArea) ? "https://es-ygj.digiwincloud.com.cn/productDownload" : "https://es-ygj.digiwincloud.com/productDownload";

            Map<String, Object> map = convertToMap(rm);
            map.put("serviceArea", serviceArea); // 環境
            map.put("language", language); // 語言
            map.put("aiobasicAddress", aiobasicAddress);
            map.put("description", description);
            map.put("hrefAddress", hrefAddress);
            map.put("misRoot", misRoot);
            map.put("misAddress", misAddress);
            map.put("downloadAddress", downloadAddress);
            map.put("loginUrl", getLoginUrl(rm));
            map.put("registerUrl", getRegisterUrl(rm));

            FreemarkerTplData tplData = new FreemarkerTplData(isCnLanguage ? ZH_CN_STANDARD : ZH_TW_STANDARD, map);

            return tplService.getByTemplateName("VersionUpdateAssessmentReportMailContent.ftl", tplData);
        } catch (Exception ex) {
            log.error("Get VersionUpdateAssessmentReportMail Error:", ex);
            return "";
        }
    }

    @Override
    public MailSourceType getMailSourceType(ReportRecord rm, List<CustomerOrgMap> ofOrgs) {
        return MailSourceType.VersionUpdateAssessmentReport;
    }

    @Override
    public String getMisAddress(String misRoot) {
        return "CN".equals(serviceArea)
                ? "https://aieom.digiwincloud.com.cn/#/report/product-maintenance-report/version-evaluation-report"
                : "https://aieom.digiwincloud.com/#/report/product-maintenance-report/version-evaluation-report";
    }

    // 尚未登入
    private String getLoginUrl(ReportRecord rm) throws Exception {
        return getUrl(baseParams(rm), "login");
    }

    // 尚未註冊
    private String getRegisterUrl(ReportRecord rm) throws Exception {
        Map<String, Object> params = baseParams(rm);

        params.put("serviceCode", rm.getServiceCode());
        params.put("servicePassword", getCustomerTaxId(String.valueOf(rm.getEid()))); // 客服密碼

        return getUrl(params, "register-account");
    }

    private Map<String, Object> baseParams(ReportRecord rm) {
        Map<String, Object> params = new HashMap<>();
        List<String> mailList = (rm.getSendLog().getReceiverList()).stream().map(e -> e.getReceiverMail()).collect(Collectors.toList());
        String reportUrl = misRoot + "#/report/product-maintenance-report/version-evaluation-report/details/" + rm.getReportId() + "?serviceCode=" + rm.getServiceCode();

        params.put("mailList", mailList);
        params.put("reportUrl", reportUrl);

        return params;
    }

    // 查詢統編
    private String getCustomerTaxId(String eid) {
        String srQuery = String.format("SELECT COALESCE(NULLIF(aci.customerTaxId, ''), NULLIF(at.taxCode, ''), '') AS customerTaxId " +
                "FROM servicecloud.ACP_Customer_Info aci " +
                "FULL OUTER JOIN servicecloud.AiopsTenant at ON aci.eid = at.eid " +
                "WHERE (aci.eid = '%s' OR at.eid = '%s') AND COALESCE(NULLIF(aci.customerTaxId, ''), NULLIF(at.taxCode, ''), '') <> '' ", eid, eid);

        List<Map<String, Object>> data = bigDataUtil.srQuery(srQuery);

        return data.isEmpty() ? "" : ObjectUtils.toString(data.get(0).get("customerTaxId"), "");
    }

    private String getUrl(Map<String, Object> params, String urltype) throws Exception {
        String urlEncode = URLEncoder.encode(Base64.getEncoder().encodeToString(EncryptionUtil.encrypt(JSON.toJSONString(params).getBytes(), EncryptionUtil.key.getBytes())), "UTF-8");
        return misRoot + "#/" + urltype + "?emParam=" + urlEncode;
    }

}
