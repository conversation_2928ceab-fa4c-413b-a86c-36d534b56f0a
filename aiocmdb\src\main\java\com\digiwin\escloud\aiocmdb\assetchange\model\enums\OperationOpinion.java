package com.digiwin.escloud.aiocmdb.assetchange.model.enums;

/**
 * 操作意见
 * 通过、不通过、需调整
 */
public enum OperationOpinion {
    /** 通过 */
    APPROVED("通过"),
    /** 不通过 */
    REJECTED("不通过"),
    /** 需调整 */
    NEEDS_ADJUSTMENT("需调整");

    private final String label;

    OperationOpinion(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public boolean isEqual(String name) {
        return this.name().equals(name);
    }
    
    /**
     * 根据字符串获取对应的枚举值
     * @param value 枚举名称或标签
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static OperationOpinion fromString(String value) {
        if (value == null) {
            return null;
        }
        
        // 先尝试按枚举名称匹配
        for (OperationOpinion opinion : OperationOpinion.values()) {
            if (opinion.name().equals(value)) {
                return opinion;
            }
        }
        
        // 再尝试按标签匹配
        for (OperationOpinion opinion : OperationOpinion.values()) {
            if (opinion.getLabel().equals(value)) {
                return opinion;
            }
        }
        
        return null;
    }

    
    /**
     * 判断传入的字符串是否与指定的枚举值相等
     * @param value 要比较的字符串（可以是枚举名称或标签）
     * @param opinion 指定的枚举值
     * @return 如果匹配则返回true，否则返回false
     */
    public static boolean isEqual(String value, OperationOpinion opinion) {
        if (value == null || opinion == null) {
            return false;
        }
        return opinion.name().equals(value) || opinion.getLabel().equals(value);
    }

}