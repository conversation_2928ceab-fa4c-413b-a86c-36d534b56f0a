<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.digiwin.escloud.aiobasic.sync.dao.SyncMapper">
    <resultMap id="esUserMap" type="com.digiwin.escloud.aiobasic.sync.model.user.EsUser">
        <result property="id" column="id"/>
        <result property="userName" column="userName"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="openId" column="openId"/>
        <result property="customerServiceCode" column="customerServiceCode"/>
        <result property="defaultProductCode" column="defaultProductCode"/>
        <association property="esUserPersonalInfo" columnPrefix="up_" resultMap="esUserPersonalInfoMap" />
        <collection property="esUserHasServiceCodes" columnPrefix="us_"  resultMap="esUserHasServiceCodesMap" />
        <collection property="esCustomerServices" columnPrefix="cs_"  resultMap="esCustomerServicesMap" />
    </resultMap>

    <resultMap id="esUserPersonalInfoMap" type="com.digiwin.escloud.aiobasic.sync.model.user.EsUserPersonalInfo">
        <result property="userId" column="userId"/>
        <result property="language" column="language"/>
        <result property="serviceRegion" column="serviceRegion"/>
        <result property="timeZone" column="timeZone"/>
    </resultMap>

    <resultMap id="esUserHasServiceCodesMap" type="com.digiwin.escloud.aiobasic.sync.model.user.EsUserHasServiceCode">
        <result property="userId" column="userId"/>
        <result property="customerServiceCode" column="customerServiceCode"/>
    </resultMap>

    <resultMap id="esCustomerServicesMap" type="com.digiwin.escloud.aiobasic.sync.model.user.EsCustomerService">
        <result property="customerServiceCode" column="customerServiceCode"/>
        <result property="productCode" column="productCode"/>
    </resultMap>

    <resultMap id="esUserProductsMap" type="com.digiwin.escloud.aiobasic.sync.model.user.EsUserProduct">
        <result property="userId" column="userId"/>
        <result property="productCode" column="productCode"/>
        <result property="isDefault" column="isDefault"/>
    </resultMap>

    <resultMap id="esCustomerMap" type="com.digiwin.escloud.aiobasic.sync.model.tenant.EsCustomer"/>

    <resultMap id="esCustomerContractMap" type="com.digiwin.escloud.aiobasic.sync.model.tenant.EsCustomerContract"/>

    <select id="getEsUsers" resultMap="esUserMap">
        SELECT a.id,userName,a.email,a.phone,a.customerServiceCode,c.openId,f.productCode as defaultProductCode, language as up_language,
        serviceRegion as up_serviceRegion,
        timeZone as up_timeZone,b.userId as up_userId,d.userId as us_userId,d.customerServiceCode as us_customerServiceCode,
        e.productCode as cs_productCode,e.customerServiceCode as cs_customerServiceCode
        FROM `${escloudDBName}`.mars_user a
        inner join `${escloudDBName}`.mars_userpersonalinfo b on a.id=b.userId
        left join `${escloudDBName}`.mars_wechataccount c on a.id=c.userid
        left join `${escloudDBName}`.mars_user_has_servicecode d on a.id=d.userId
        left join `${escloudDBName}`.mars_customerservice e on d.customerServiceCode=e.customerServiceCode
        left join `${escloudDBName}`.mars_userdefaultproductinfo f on a.id=f.userId and f.isDefault='1'
        WHERE 1=1
        <if test="userId!=null and userId!=''">
            and a.id= #{userId}
        </if>
    </select>

    <select id="getEsCustomers" resultMap="esCustomerMap">
        SELECT mc.customerCode,mc.customerServiceCode,mc.customerName,
        case WHEN mc.CustomerFullNameCH is NULL or mc.CustomerFullNameCH = '' THEN sr.GG004 ELSE mc.CustomerFullNameCH END customerFullNameCH,mc.CustomerFullNameEN customerFullNameEN,mc.taxNo,mc.eid
        FROM `${escloudDBName}`.mars_customer mc
        LEFT JOIN `${escloudDBName}`.serae sr ON mc.CustomerServiceCode = sr.AE001
        WHERE 1=1 and IFNULL(customerServiceCode,'')<![CDATA[<>]]>''
        <if test="serviceCode!=null and serviceCode!=''">
            and mc.customerServiceCode= #{serviceCode}
        </if>
    </select>

    <update id="updateEsCustomer">
        update `${escloudDBName}`.mars_customer
        set eid=#{tenantSid}
        WHERE 1=1 and customerServiceCode= #{serviceCode}
    </update>

    <update id="updateSupplierCustomerEid">
        update `${escloudDBName}`.supplier_customer_map
        set eid=#{tenantSid}
        WHERE 1=1 and eid= #{eid}
    </update>

    <select id="getEsCustomerContracts" resultMap="esCustomerContractMap">
        SELECT customerServiceCode, productCode, productCategory, productVersion,
               hasOwnerService, hasTextService, hasOwnerIssueService,
               serviceStaffCode, serviceStaff, contractState, contractStartDate, contractExprityDate,
               industryCode, areaCode,
               c.userid
        FROM `${escloudDBName}`.mars_customerservice a
        LEFT join `${escloudDBName}`.mars_customerservicesatff b on a.ServiceStaffCode=b.workno
        LEFT join `${escloudDBName}`.mars_staffaccount c on b.itcode=c.itcode
        WHERE 1=1
        <if test="serviceCode!=null and serviceCode!=''">
            and customerServiceCode= #{serviceCode}
        </if>
    </select>

    <select id="getAioAllTenantIds" resultType="java.lang.String">
        SELECT id
        FROM tenant
    </select>

    <insert id="batchInsertTenant" parameterType="com.digiwin.escloud.aiobasic.sync.model.tenant.Tenant">
        insert into tenant(sid, id, name,customer_id, taxCode, status)
        VALUES
        <foreach collection="list" item="item"  separator=",">
            (#{item.sid},#{item.id},#{item.name}, #{item.customer_id}, #{item.taxCode}, #{item.status})
        </foreach>
    </insert>

    <insert id="insertTenant" parameterType="com.digiwin.escloud.aiobasic.sync.model.tenant.Tenant">
        insert into tenant(sid, id, name, customerFullNameCH, customerFullNameEN, customer_id, taxCode, status)
        VALUES(#{sid},#{id},#{name},#{customerFullNameCH},#{customerFullNameEN},  #{customer_id}, #{taxCode}, #{status})
        ON DUPLICATE KEY UPDATE name = #{name}, customerFullNameCH = #{customerFullNameCH}, customerFullNameEN = #{customerFullNameEN}, customer_id = #{customer_id},taxCode = #{taxCode}
    </insert>

    <insert id="insertSupplierTenantMap" parameterType="com.digiwin.escloud.aiobasic.sync.model.supplier.SupplierTenantMap">
        insert into supplier_tenant_map (id, sid, eid, serviceCode)
        values(#{id}, #{sid}, #{eid}, #{serviceCode})
        ON DUPLICATE KEY UPDATE sid=#{sid}
    </insert>

    <select id="getUserExist" resultType="java.lang.Integer">
        select 1
        from user
        where sid=#{userSid}
        limit 1
    </select>

    <select id="getUserTenantMapExist" resultType="java.lang.Integer">
        select 1
        from user_tenant_map
        where userSid=#{userSid} and eid=#{eid}
        limit 1
    </select>

    <insert id="insertUser" keyProperty="sid" keyColumn="sid" parameterType="com.digiwin.escloud.aiouser.model.user.User">
        insert into user(sid, id, name, email, telephone,openId,defaultEid,defaultEidSid,defaultSid,defaultSidEid,status
        <if test="wechat!=null and wechat!=''">
            ,wechat
        </if>
        )
        values(#{sid}, #{id}, #{name}, #{email}, #{telephone}, #{openId}, #{defaultEid}, #{defaultEidSid},
        #{defaultSid},#{defaultSidEid},#{status}
        <if test="wechat!=null and wechat!=''">
            ,#{wechat}
        </if>
        )
    </insert>

    <update id="updateUser" parameterType="com.digiwin.escloud.aiouser.model.user.User">
        update user
        set name=#{name}, email=#{email}, telephone=#{telephone}, openId=#{openId}, status=#{status}
        <if test="wechat!=null and wechat!=''">
            ,wechat=#{wechat}
        </if>
        where sid=#{sid}
    </update>

    <insert id="insertUserTenantMap" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiobasic.sync.model.user.UserTenantMap">
        insert into user_tenant_map(id, userSid, eid, sid,authorizedProductCodes,defaultProductCode, enterprise)
        values(#{id}, #{userSid}, #{eid}, #{sid}, #{authorizedProductCodes},#{defaultProductCode},#{enterprise})
        ON DUPLICATE KEY UPDATE authorizedProductCodes=#{authorizedProductCodes}, defaultProductCode=#{defaultProductCode}
    </insert>

    <insert id="insertUserPersonalInfo" keyProperty="sid" keyColumn="sid" parameterType="com.digiwin.escloud.aiobasic.sync.model.user.UserPersonalInfo">
        insert into user_personalinfo(sid, language, serviceRegion, timeZone)
        values(#{sid}, #{language}, #{serviceRegion}, #{timeZone})
        ON DUPLICATE KEY UPDATE language=#{language},serviceRegion=#{serviceRegion},timeZone=#{timeZone}
    </insert>

    <insert id="insertTpUser" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiobasic.sync.model.user.UserThirdParty">
        insert into user_thirdparty(id, tpEid, tpProductCode, tpAccountSet, tpAccountSetType,tpUserId,tpUserType,tpVipUserId,tpVipUserName,
        vipUserId,userId, eid,sid,tpUserEmail,tpUserTelephone)
        values(#{id}, #{tpEid}, #{tpProductCode}, #{tpAccountSet}, #{tpAccountSetType}, #{tpUserId}, #{tpUserType},#{tpVipUserId},#{tpVipUserName},
        #{vipUserId},#{userId},#{eid}, #{sid},#{tpUserEmail},#{tpUserTelephone})
        ON DUPLICATE KEY UPDATE tpVipUserId=#{tpVipUserId},tpVipUserName=#{tpVipUserName}
    </insert>

    <insert id="insertTenantContract" parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantContract">
        insert into tenant_contract(id,eid,sid,productCode,productShortName,productVersion,serviceType,hasOwnerService,hasTextService,hasOwnerIssueService,
        serviceStaffId,serviceStaffName,contractState,contractStartDate,contractExpiryDate,industryId,areaId,access,description,canContact)
        values (#{id},#{eid},#{sid},#{productCode},#{productShortName},#{productVersion},#{serviceType},#{hasOwnerService},#{hasTextService},
        #{hasOwnerIssueService},#{serviceStaffId},#{serviceStaffName},#{contractState},
        #{contractStartDate},#{contractExpiryDate},#{industryId},#{areaId},#{access},#{description},#{canContact})
        ON DUPLICATE KEY UPDATE productShortName=#{productShortName},productVersion=#{productVersion},serviceType=#{serviceType},
        hasOwnerService=#{hasOwnerService},hasTextService=#{hasTextService},hasOwnerIssueService=#{hasOwnerIssueService},serviceStaffId=#{serviceStaffId},
        serviceStaffName=#{serviceStaffName},contractState=#{contractState},contractStartDate=#{contractStartDate},
        contractExpiryDate=#{contractExpiryDate},industryId=#{industryId},areaId=#{areaId},access=#{access},description=#{description},
        canContact=#{canContact}
    </insert>

    <select id="selectTenantListByMap" resultType="com.digiwin.escloud.aiobasic.sync.model.tenant.Tenant">
        SELECT t.sid, t.id
        FROM tenant t
        INNER JOIN supplier_tenant_map stm ON t.sid = stm.eid
        WHERE 1 = 1
        <if test="sid != null and sid > 0">
            AND stm.sid = #{sid}
        </if>
        <if test="serviceCode != null and serviceCode != ''">
            AND stm.serviceCode = #{serviceCode}
        </if>
        <if test="serviceCodeList != null">
            <foreach collection="serviceCodeList" item="item" open=" AND stm.serviceCode IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectTenantContractListByMap" resultType="com.digiwin.escloud.aiobasic.sync.model.tenant.TenantModeContract">
        select spmcss.id AS spmcssId,spmcss.productCode,spmcss.moduleCode,spmcss.moduleId,spmcss.enabled,
        spmcss.sid,tc.eid, tc.contractStartDate, tc.contractExpiryDate,
        stm.serviceCode,
        tmc.id AS tmcId,tmc.startDate, tmc.endDate, tmc.status
        from supplier_product_module_contract_sync_setting spmcss
        LEFT JOIN tenant_contract tc ON tc.sid = spmcss.sid  and spmcss.productCode= tc.productCode
        and TIMESTAMPDIFF(DAY, NOW(), tc.contractExpiryDate) >= 0
        LEFT JOIN supplier_tenant_map stm ON tc.eid = stm.eid
        LEFT JOIN tenant_module_contract tmc on tc.eid=tmc.eid and tmc.moduleId  = spmcss.moduleId
        where 1= 1 AND spmcss.enabled =1
        <if test="sid != null and sid > 0">
            AND spmcss.sid = #{sid}
        </if>
        <if test="serviceCode != null and serviceCode != ''">
            AND stm.serviceCode = #{serviceCode}
        </if>
        <if test="serviceCodeList != null">
            <foreach collection="serviceCodeList" item="item" open=" AND stm.serviceCode IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY spmcss.moduleCode, tc.contractExpiryDate desc
    </select>

    <update id="updateTenantModuleContractsBySync">
        <foreach collection="tenantModuleContracts" item="item" index="index" separator=";">
            update tenant_module_contract
            <set>
                status=#{item.status}, startDate=#{item.startDate}, endDate=#{item.endDate}
                <if test="item.staffId != null and item.staffId > 0">
                    , staffId=#{item.staffId}
                </if>
            </set>
            where id=#{item.tmcId}
        </foreach>
    </update>

    <insert id="insertTenantModuleContractsBySync" keyProperty="id" keyColumn="id">
        INSERT INTO tenant_module_contract(id, sid, eid, moduleId, status, orderMethod, startDate, endDate,
        serverCount, workStationCount, iotCount, staffId, openId,
        userName, noticeEmail)
        <foreach collection="inserttenantModuleContracts" open="VALUES" item="item" separator=",">
            (#{item.id}, #{item.sid}, #{item.eid}, #{item.moduleId}, #{item.status},
            #{item.orderMethod},
            #{item.startDate}, #{item.endDate},
            #{item.serverCount}, #{item.workStationCount}, #{item.iotCount}, #{item.staffId}, #{item.openId},
            #{item.userName}, #{item.noticeEmail})
        </foreach>
        ON DUPLICATE KEY UPDATE status = VALUES(status),startDate = VALUES(startDate),endDate =   VALUES(endDate)
    </insert>

    <select id="selectSamcIdByMap" resultType="java.lang.Long">
        select samc.id
        from supplier_aiops_module_class samc
        WHERE 1 = 1
        <if test="sid != null and sid > 0">
            AND samc.sid = #{sid}
        </if>
        <if test="samId != null and samId != ''">
            AND samc.samId = #{samId}
        </if>
        <if test="classCode != null and classCode != ''">
            AND samc.classCode = #{classCode}
        </if>
    </select>

    <select id="selectTmcExistByMap"  resultType="java.lang.Integer">
        select count(*) from tenant_module_contract where moduleId = #{moduleId}
        and sid =#{sid}  and eid =#{eid}
    </select>

    <insert id="insertTenantModuleContractsDetailBySync" keyProperty="id" keyColumn="id">
        INSERT INTO tenant_module_contract_detail(id, tmcId, samcId, availableCount, usedCount)
        <foreach collection="inserttenantModuleContractsDetail" item="item" open="VALUES(" separator="), (" close=")">
            #{item.id}, #{item.tmcId}, #{item.samcId}, #{item.availableCount}, #{item.usedCount}
        </foreach>
        ON DUPLICATE KEY UPDATE availableCount = (CASE WHEN IFNULL(usedCount, 0) > VALUES(availableCount) THEN availableCount
        ELSE VALUES(availableCount) END)
    </insert>

    <select id="selectSupplierEmployeeByWorkNo" resultType="java.util.Map">
        SELECT IFNULL(userSid, 0) AS sid, `name` AS userName
        FROM supplier_employee
        WHERE workNo = #{workNo}
    </select>

    <select id="getSyncServcieCodeByRecentTime" resultType="java.lang.String">
        select mcs.CustomerServiceCode
        from `${escloudDBName}`.mars_customerservice mcs
        where mcs.__version__ > DATE_SUB(#{endTime},INTERVAL #{nearlyHours} HOUR)
        group by mcs.CustomerServiceCode
        order by __version__
    </select>

    <select id="queryServiceCodeByContractTime" resultType="java.lang.String">
        SELECT DISTINCT CustomerServiceCode
        FROM `${escloudDBName}`.mars_customerservice cs
        LEFT JOIN supplier_tenant_map stm ON cs.CustomerServiceCode= stm.serviceCode
        LEFT JOIN tenant t ON t.sid = stm.eid
        WHERE cs.ProductCode IN ('37','08','100','06','164','165','163','15','147') AND cs.ContractExprityDate >= NOW() AND t.id IS NULL
        <if test="serviceCodeList != null and serviceCodeList.size()>0">
            <foreach collection="serviceCodeList" item="item" index="index" separator="," open="AND cs.CustomerServiceCode in(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 目前新增的 穩態 的supplier_aiops_module.id 為 "60001" -->
    <select id="getAppAutoUpdate" resultType="com.digiwin.escloud.aiobasic.sync.model.tenant.TenantModeContract">
        select stm.serviceCode, tmc.*
        from tenant_module_contract tmc
        inner join supplier_tenant_map stm on tmc.eid = stm.eid
        where moduleId = 60001
    </select>

    <!-- 目前新增的 穩態 的supplier_aiops_module.id 為 "60001" -->
    <select id="getAppAutoUpdateDetail" resultType="com.digiwin.escloud.aiobasic.sync.model.tenant.TenantModeContract">
        select * from tenant_module_contract_detail where tmcId in (
            select id from tenant_module_contract where moduleId = 60001
        );
    </select>

    <!-- 目前新增的 穩態 的supplier_aiops_module.id 為 "60001" -->
    <select id="getAppAutoUpdateRecoverAuthIds" resultType="java.lang.Long">
        select tmc.id as tmcId
        from tenant_module_contract tmc where moduleId = 60001 and TIMESTAMPDIFF(DAY, startDate, endDate) >= 0
    </select>

    <!-- 目前新增的 穩態 的supplier_aiops_module.id 為 "60001" -->
    <select id="getExistEidAndServiceCodeMapping" resultType="com.digiwin.escloud.aiobasic.sync.model.tenant.EidAndProductOwner">
        select
            ai.eid,
            ai.aiopsItemType,
            ai.aiopsItem,
            /*ai.aiopsItemId,*/
            /*ai.aiopsAuthStatus,*/

            t.name as custName,
            stm.serviceCode,
            /*aa.aiid,*/
            po.*
        from
            aiops_instance ai
                inner join supplier_tenant_map stm on stm.eid = ai.eid
                inner join (
                /*從收集項回串*/
                select adcd.aiid from
                    aiops_device_collect_detail adcd
                        inner join (
                        select id from aiops_collect_config where collectCode in ('YiFeiAppAutoUpdate', 'E10AppAutoUpdate', 'WF_ATN_AutoUpdate', 'PLM_ATN_AutoUpdate', 'PLMAppAutoUpdate')
                    ) acc on adcd.accid = acc.id
            ) aa on aa.aiid = ai.id
                inner join (
                /*
                易飛：李加伟 01296
                E10: 陈朝阳 09289
                WF:  陳琬渝 02713
                PLM:徐红昱 15931
                */
                select
                    case when workNo = 01296 then '08'/*易飛*/
                         when workNo = 09289 then '37' /*E10*/
                         when workNo = 02713 then '02' /*Workflow ERP*/
                         when workNo = 15931 then 'PLM' /*PLM*/
                        end productCode,
                    userSid, workNo, email, name as staffName
                from supplier_employee
                where workno in (01296, 09289, 02713, 15931)
            ) po on po.productCode = ai.aiopsItem
                left join tenant t on t.customer_id = stm.serviceCode
    </select>

    <select id="select147TenantContractByDatetime" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantContractEx">
        SELECT tc.*,mcs.CustomerServiceCode serviceCode
        FROM `${escloudDBName}`.mars_customerservice mcs
        left join tenant t on t.id = mcs.CustomerServiceCode
        left join tenant_contract tc on t.sid = tc.eid and tc.productCode = '147'
        left join tenant_module_contract tmc on tmc.eid = tc.eid and tmc.moduleId = 1
        left join user u on u.sid = tmc.staffId
        WHERE mcs.ProductCode = '147' AND (
            (mcs.ContractExprityDate != tc.contractExpiryDate and mcs.__version__ > tc.updateTime)
                                               OR (mcs.ServiceStaffContact is not null and mcs.ServiceStaffContact !='' and mcs.ServiceStaffContact != u.id)
        OR (datediff(tc.contractExpiryDate,now())>=0 and tmc.status not in  (1,3))
            )
        <if test="serviceCodeList != null and serviceCodeList.size() > 0">
            <foreach collection="serviceCodeList" item="item" index="index" separator="," open="AND mcs.CustomerServiceCode in (" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="select147TenantContractByInstalled" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantContractEx">
        SELECT tc.*,mcs.CustomerServiceCode serviceCode
        FROM `${escloudDBName}`.mars_customerservice mcs
                 left join tenant t on t.id = mcs.CustomerServiceCode
                 left join tenant_contract tc on t.sid = tc.eid and tc.productCode = '147'
        WHERE mcs.ProductCode = '147' and t.installed = false
    </select>

    <select id="selectAll147TenantContract" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantContractEx">
        SELECT tc.*,mcs.CustomerServiceCode serviceCode
        FROM `${escloudDBName}`.mars_customerservice mcs
        left join tenant t on t.id = mcs.CustomerServiceCode
        left join tenant_contract tc on t.sid = tc.eid and tc.productCode = '147'
        WHERE mcs.ProductCode = '147' and mcs.__version__ >= #{updateTime}
        <if test="serviceCodeList != null and serviceCodeList.size() > 0">
          <foreach collection="serviceCodeList" item="item" open=" and mcs.CustomerServiceCode not in (" close=")" separator=",">
              #{item}
          </foreach>
        </if>
    </select>

    <update id="updateTenantInstalled">
            UPDATE tenant
            SET installed = '0'
            WHERE sid IN
        <foreach collection="tenantSids" item="sid" open="(" separator="," close=")">
             #{sid}
        </foreach>
    </update>

</mapper>