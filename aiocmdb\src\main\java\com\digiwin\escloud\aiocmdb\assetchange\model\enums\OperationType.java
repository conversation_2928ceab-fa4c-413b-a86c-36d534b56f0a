package com.digiwin.escloud.aiocmdb.assetchange.model.enums;

/**
 * 操作类型
 * 新建、提交审批、申请、执行、提交验收、验收
 */
public enum OperationType {
    /** 新建 */
    CREATE("新建"),
    /** 提交审批 */
    SUBMIT_APPROVAL("提交审批"),
    /** 申请 */
    APPLY("申请"),
    /** 执行 */
    EXECUTE("执行"),
    /** 提交验收 */
    SUBMIT_ACCEPTANCE("提交验收"),
    /** 验收 */
    ACCEPTANCE("验收");

    private final String label;

    OperationType(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public boolean isEqual(String name) {
        return this.name().equals(name);
    }
    
    /**
     * 根据字符串获取对应的枚举值
     * @param value 枚举名称或标签
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static OperationType fromString(String value) {
        if (value == null) {
            return null;
        }
        
        // 先尝试按枚举名称匹配
        for (OperationType type : OperationType.values()) {
            if (type.name().equals(value)) {
                return type;
            }
        }
        
        // 再尝试按标签匹配
        for (OperationType type : OperationType.values()) {
            if (type.getLabel().equals(value)) {
                return type;
            }
        }
        
        return null;
    }

    
    /**
     * 判断传入的字符串是否与指定的枚举值相等
     * @param value 要比较的字符串（可以是枚举名称或标签）
     * @param type 指定的枚举值
     * @return 如果匹配则返回true，否则返回false
     */
    public static boolean isEqual(String value, OperationType type) {
        if (value == null || type == null) {
            return false;
        }
        return type.name().equals(value) || type.getLabel().equals(value);
    }

}