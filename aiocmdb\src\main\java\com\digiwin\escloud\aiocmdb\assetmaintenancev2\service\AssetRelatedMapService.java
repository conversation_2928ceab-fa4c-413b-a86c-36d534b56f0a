package com.digiwin.escloud.aiocmdb.assetmaintenancev2.service;

import cn.hutool.core.lang.Pair;
import com.digiwin.escloud.aiocmdb.asset.model.AssetRelatedCategoryClassification;
import com.digiwin.escloud.aiocmdb.asset.model.AssetRelatedCategoryClassificationTree;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetAttribute;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetRelatedMap;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.ReverseRelatedAsset;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface AssetRelatedMapService {

    Integer deleteRelatedAsset(String eid, long assetRelatedId);
    Map<String, Object> addRelatedAsset(List<AssetRelatedMap> assetRelatedMaps);
    Map<String, Object> addReverseRelatedAssets(List<ReverseRelatedAsset> reverseRelatedAssets);
    Pair<Map<String, Object>, List<LinkedHashMap<String, Object>>> saveAssetAttribute(String modelCode, AssetAttribute assetAttribute);
    Optional<String> getNewAssetCode(String modelCode, String eid, AssetAttribute assetAttribute);
    List<AssetRelatedCategoryClassificationTree> queryRelatedAssets(String eid, String primaryAssetId);
    List<AssetRelatedCategoryClassificationTree> queryReverseRelatedAssets(String eid, String associatedAssetId);
    
    // 新增的批量查询和数量查询方法
    List<Map<String, Object>> queryReverseRelatedAssetsByIds(String eid, List<String> associatedAssetIds);
    List< Map<String, Object>>  queryAssetRelationCount(String eid, List<String> assetIds, String queryType);
    

    void removeAssetCodeCache(String eid, String modeCode, String sinkName);
    void removeRangeAssetCodeCache(String modeCode, String sinkName);
    void removeAllAssetCodeCache();
    List<String> watchInventoryAssetCodAssetCodes(String eid, String modelCode, String sinkName);
}
