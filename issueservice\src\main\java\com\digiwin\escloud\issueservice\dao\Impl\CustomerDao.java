package com.digiwin.escloud.issueservice.dao.Impl;

import com.digiwin.escloud.issueservice.dao.ICustomerDao;
import com.digiwin.escloud.issueservice.model.CustomerServiceInfo;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2018-01-03.
 */
@Service
public class CustomerDao implements ICustomerDao {
    @Autowired
    private SqlSession sqlSession;

    @Override
    public String GetCustomerIssueConfirmer(String serviceCode, String productCode){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("productCode", productCode);
        return sqlSession.selectOne("escloud.customermapper.GetCustomerIssueConfirmer", map);
    }


    @Override
    public boolean SelectCustomerIssueConfirmSetting(String serviceCode, String productCode){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("productCode", productCode);
        int res =  sqlSession.selectOne("escloud.customermapper.SelectCustomerIssueConfirmSetting", map);
        if(res == 1)
            return true;
        else
            return false;
    }


    @Override
    public String SelectCustomerContractState(String serviceCode, String productCode){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("productCode", productCode);
        String res =  sqlSession.selectOne("escloud.customermapper.SelectCustomerContractState", map);
        if(res == null)
            return  "";
        else
            return res;
    }

    @Override
    public CustomerServiceInfo SelectCustomerServiceInfo(String serviceCode, String productCode){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("productCode", productCode);
        return sqlSession.selectOne("escloud.customermapper.SelectCustomerServiceInfo", map);

    }
}
