package com.digiwin.escloud.issueservice.services;

import com.digiwin.escloud.issueservice.model.*;
import com.digiwin.escloud.issueservice.t.model.common.BaseResponse;
import com.digiwin.escloud.issueservice.t.model.common.PageInfo;
import com.mongodb.gridfs.GridFSDBFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018-01-02.
 */
public interface IIssueService {
    /**
     * 案件提交
     * @param issue
     * @return
     */
    int SubmitIssue(Issue issue);
    boolean checkConfirm(Issue issue);
    void SubmitIssueAsync(Issue issue, String issueStatus, boolean isContainFileName);
    void SubmitIssueAsyncNew(Issue issue, String issueStatus, boolean isContainFileName);

    void UpdateSyncStatusByIssueId(long issueId, String syncStatus);
    /**
     * 使用券
     * @param couponNo
     * @return
     */
    ResponseBase UseCouponAsync(String couponNo, String issueId, String userId);
    String getCrmIdByIssueId(long issueId);
    /**
     * 获取案件列表
     * @param issuesGetRequest      需求项目
     * @return
     */
    List<Issue> GetIssues(IssuesGetRequest issuesGetRequest);
    List<Issue> getIssuesForMis(int pageIndex, int size, String userId, String department, String issueStatus, String issueType, String startTime, String endTime, String machineRegion, String serviceCode, String productCode,String erpSystemCode, String esSearch);

    /**
     * 获取案件列表
     * @param issuesGetRequest      需求项目
     * @return
     */
    List<Issue> GetIssuesbyDescription(IssuesGetRequest issuesGetRequest);

    /**
     * 获取案件进度
     * @param issueId 案件Id
     * @return
     */
    Issue GetIssueProgress(String issueId,String userId,String from);
    Issue GetIssueProgressForMis(String issueId,String userId);

    /**
     * 催单
     * @param issueId
     * @param userId
     * @return
     */
    boolean RemindIssue(long issueId, String userId, String deptId);

    /**
     * 审核
     * @param issueId
     * @param userId
     * @param desc
     * @return
     */
    boolean ConfirmIssue(long issueId, String userId, String desc, String deptId);

    /**
     * 退回
     * @param issueId
     * @param userId
     * @param desc
     * @return
     */
    boolean SendBackIssue(long issueId, String userId, String desc, String deptId);

    /**
     * 评价案件
     * @param issueId
     * @param userId
     * @param desc
     * @param deptId
     * @return
     */
    boolean EvaluateIssue(long issueId, String userId, String desc, String deptId);

    int GetFollowUpIssueStatus(Long issueId);

    /**
     * 跟催案件
     * @param issue
     * @param userId
     * @param desc
     * @return
     */
    int FollowUpIssue(Issue issue, String userId, String desc);

    /**
     * 获取用户的所有案件数量
     * @param userId 用户ID
     * @return
     */
    IssueCount GetIssueCountByUserId(String userId,  String productCode, String department, String queryUserId, String serviceRegion, String condition_TW, String condition_check_TW,String agent,String newReply,String myself,String from,String serviceCode);

    /**
     * 获取用户的所有案件数量
     * @param  issuesGetRequest      需求项目
     * @return
     */
    IssueCount GetIssueCountByUserIdAndDescription(IssuesGetRequest issuesGetRequest);


    /**
     *
     * @param userId
     * @param status
     * @param page
     * @param count
     * @return
     */
    List<Issue> GetIssuesByStaff(String userId, String status, int page, int count, String deptId, String queryUserId);

    void SendMailTest(String email);


    /**
     * 查看图片
     * @param fileId
     * @return
     */
    GridFSDBFile GetIssueAttachFile(String fileId);

    /**
     * 获取案件附件文件列表
     * @param issueId
     * @return
     */
    List<IssueAttachmentFile> GetIssueAttachmentFiles(String issueId);

    /**
     * 获取案件补充说明附件文件列表
     * @param issueId
     * @return
     */
    List<IssueAttachmentFile> GetIssueAdditionalExplanationAttachmentFiles(String issueId, String dt);

    /**
     * 获取案件附件
     * @param issueId
     * @return
     */
    List<IssueAttachment> GetIssueAttachments(String issueId);
    /**
     * 單獨获取與客服聯繫附件
     * @param issueId
     * @return
     */
    List<IssueAttachment> GetIssueCallCenterAttachments(String issueId,String progressId);
    /*
    * 分页获取客服案件集合
    * */
    List<IssueDetailInfo> getIssuesInfoListByUserAndStatus(String issueStatus,int pageIndex, int size,String userId,String deptId
            ,String issueType,String queryUserId,String startTime,String endTime, String machineRegion, String additionalExplanationReadType, String serviceContact, String projectUpdate, String serialNumberUpdate, String custLevel, String serviceCode, String productCode, String employeeId, String crmId,String supportId);
    List<IssueDetailInfo> getAllIssueDetails(String issueStatus, int pageIndex, int size, String userId, String deptId
            , String issueType, String queryUserId, String startTime, String endTime, String machineRegion, String custLevel, String contractState, String serviceCode, String productCode, String employeeId, String crmId, String additionalExplanationReadType, String issueDescription);

    /*
    * 获取客服案件总数
    * */
    int getIssuesInfoCountByUserAndStatus(String issueStatus,String userId,String deptId
            ,String issueType,String queryUserId,String startTime,String endTime, String machineRegion,String additionalExplanationReadType,String serviceContact,String projectUpdate,String serialNumberUpdate,String custLevel, String serviceCode, String productCode, String employeeId, String crmId,String supportId);
    /*
     * 客服主管查看的案件列表总数量
     * */
    int StaffIssueCountV2(String issueStatus,String userId,String deptId
            ,String issueType,String queryUserId,String startTime,String endTime, String machineRegion, String custLevel, String contractState, String serviceCode, String productCode, String employeeId, String crmId, String additionalExplanationReadType, String issueDescription);
    /*
     * 客服主管查看的案件列表总数量
     * */
    int getIssuesInfoCountForCustomerServiceManager(String issueStatus,String userId,String deptId
            ,String issueType,String queryUserId,String startTime,String endTime, String machineRegion, String custLevel, String serviceCode, String productCode, String employeeId, String crmId, String additionalExplanationReadType,String classificationStr);
    List<IssueDetailInfo> getIssuesInfoListByUserAndStatusForCustomerServiceManager(String issueStatus, int pageIndex, int size, String userId, String deptId
            , String issueType, String queryUserId, String startTime, String endTime, String machineRegion, String custLevel, String serviceCode, String productCode, String employeeId, String crmId, String additionalExplanationReadType, String classificationStr);

    /* 获取產品線為科維產品之案件总数
    * */
    int getIssuesInfoCountByKewei(String issueStatus,String issueType,String additionalExplanationReadType,String productCode);
    /**
     * 案件补充说明邮件
     * @param issue
     * @return
     */
    void SubmitIssueAdditionalExplanationAsync(Issue issue);

    void SubmitCallMeIssueAsync(Issue issue,Issue old_issue,String userid);

    int SendAdditionalExplanation(Issue issue, String dt);

    int SendAdditionalExplanationToUser(Issue issue, String dt);

    List<IssueDetailInfo> getWebIssuesInfoList(String issueStatus,String productCode,String codeOrName,int pageIndex, int size
            ,String issueType,String startTime,String endTime);

    int getWebIssuesCount(String issueStatus,String productCode,String codeOrName,String issueType,String startTime,String endTime);
    /**
     * 獲取客戶案件服務中心
     * */
    List<String> getCustomerIssueServiceRegionInfo( String customerServiceCode);
    List<String> getMachineRegionByStaffId( String staffId);
    /**
     * 转换时区
     *
     * @param srcTime
     * @param userTimezone
     * @return
     */
    String transferTimeZone(String srcTime, int userTimezone);

    /*將call-centeer內容標示為已讀
     * */
    int UpdateAdditionalExplanationReadType(String issueId);

    /*修改问题描述* */
    int UpdateIssueDescription(IssueDetailInfo issue);

    /*储存子单头表issue_summary* */
    int saveIssueSummary(String issueId,String type,String processTime);
    int saveIssueSummaryForAgressClose(String issueId,String processTime);
    /*查询子单头表issue_summary */
    IssueSummary queryIssueSummary(String issueId);

    List<Map<String, String>> getWebIssueClassification(String serviceRegion, String customerServiceCode, String productCode);

    PageInfo<IssueDetailInfo> getAllIssueDetails(String role, String workNo, String color, String issueStatus, int pageNum, int pageSize);

    TrialIssueLimitResponse SelectTrialSubmitedIssueCount(String serviceCode, String productCode);

    boolean CheckCustomerContract(String serviceCode, String productCode);

    boolean CheckCustomerContractExist(String serviceCode, String productCode);

    void updateWarningNoticeStatus(Issue issue);
    void updateWarningNoticeStatus(String crmId);
    void updateEventNoticeStatus(Issue issue);
    void updateEventNoticeStatus(String crmId);
    void updateEventNoticeStatus(IssueStatusSyncData issue);
    void batchInsertWarningId(Issue issue);

    List<ServiceProductCount> getServiceProductTarget(String serviceRegion, String staffId,String refreshDate);

    boolean updateIssueService(String userId, String issueId, String serviceId,String productCode);

    boolean cancelCloseIssue(String issueId, long progressId);
    boolean cancelClose(long issueId, String crmId, String userId);

    boolean checkIssueIsSyncCrm(String serviceRegion, String productCode, String issueStatus);

    String findUserIdbyMail(String mail,String serviceCode);

    String findDefaultPhonebyServiceCode(String serviceCode);

    String findMailbyPhone(String serviceCode, String phone,String extension , String mobilePhone);
    void CompleteUserContact(Issue issue);
    IssueSourceMapResult getIssueSourceMapList(String issueStatus, String sourceType, String sourceId, String userId);
    IssueSum getIssueSum(String userId, String department, String serviceCode);
    Object updateSyncStatus4Test(String issueId);

    /*修改ProgressAndFile* */
    void updateIssueProgressAndFile(Issue issue);

    Object updateCaseDetailSyncStatus(long issueId);
    /**
     * 获取案件列表
     * @param serviceCode      客服代號
     * @param page      頁碼
     * @param size      大小
     * @param searchKey      搜尋字
     * @param serviceRegion
     * @return
     */
    List<Issue> GetIssuesbyServiceCodeAndSearchKey(String serviceCode, int page, int size, String searchKey, String serviceRegion);

    /**
     * 获取用户的所有案件数量
     * @param serviceCode      客服代號
     * @param searchKey      搜尋字
     * @param serviceRegion
     * @return
     */
    IssueCount GetIssueCountByServiceCodeAndSearchKey(String serviceCode,String searchKey, String serviceRegion);

    void SyncCallCenterIssues();

    List<Issue> issueFullContentSearch(String msgContent,String userId, String serviceCode, String productCode, String localId);

    long saveShipment(String serviceCode, String productCode, String shipmentNum, String orderName, MultipartFile multipartFile);

    long saveShipment_SFT(String serviceCode, String productCode, String shipmentNum, String orderName, String serial);

    UserPersonalInfo getUserInfoForShipment (String serviceCode, String orderName);

    BaseResponse addServiceContact(IssueProgress issueProgress);

    BaseResponse getServiceContacts(long issueId);

    BaseResponse getSelfUpdateSystemFiles(long issueId, String invalidStatus);

    BaseResponse getSelfUpdateSystemFileLogs(long isusfId, String isClient);

    BaseResponse insertSelfUpdateSystemLog(long issueId, IssueSelfUpdateSystemLog issueSelfUpdateSystemLog);

    BaseResponse sftCheck(String crmId, int approve, String maderWorkNo, String description);

    BaseResponse insertIssueSelfUpdateSystemLog(long issueId, long isusfId, String userId, IssueSelfUpdateSystemLogReq issueSelfUpdateSystemLogReq);

    BaseResponse updateServiceContactRead(long issueId, String userId);

    BaseResponse updateSystemFileRead(long issueId, String userId);

    void replyToContact();

    void boardcastIssueToService();

    int checkAgent(String serviceCode, String productCode);

    AgentIssueSum selectAgentIssueSumarry(String serviceCode, String productCode);

    BaseResponse getIssueCustomerInfo(long issueId);

    com.digiwin.escloud.common.model.ResponseBase checkEdrEventIssue(List<Map<String,String>> list);

    BaseResponse getEventIssues(int pageNum, int pageSize, String eventId);

    String changeSFTProductCode(String serviceCode);

    List<IssueUnresolvedCount> selectUnresolvedIssueList(String userId, String productCode, String department, String queryUserId, String customerServiceCode);

    List<IssueCountStatistic> selectIssueStatisticList(String userId, String productCode, String department, String startDate, String endDate, String customerServiceCode, String queryUserId);

    List<IssueProductCodeCount> selectIssueProductCodeStatisticList(String userId, String productCode, String department, String startDate, String endDate, String customerServiceCode, String queryUserId);
    BaseResponse updateIssueKbShareChatFileHelp(long issueId, String chatFileHelp);

    BaseResponse updateUserId(IssueChangeSubmiterHistory issueChangeSubmiterHistory);

    BaseResponse selectIssueChangeSubmiterHistory(Long issueId);
    BaseResponse selectIssueByServiceCode(String serviceCode);

    /**
     * 同步大陆案件状态到 sr的预警warning表
     * @param issue
     */
    void updateWarningNoticeStatus_bigData(Issue issue);
}
