package com.digiwin.escloud.aiocmdb.assetmaintenancev2.service;

import cn.hutool.core.lang.Pair;
import com.digiwin.escloud.aiocmdb.asset.dao.AssetMaintenanceV2Mapper;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSimple;
import com.digiwin.escloud.aiocmdb.asset.utils.AssetCodeRuleException;
import com.digiwin.escloud.aiocmdb.asset.utils.AssetNoUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;

import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;

import static com.digiwin.escloud.aiocmdb.asset.utils.AssetNoProduceType.SERIAL_NUMBER;

public abstract class AssetRelatedMapBasic {

    private final static String PREFIX_ASSET_CODE_CACHE_KEY = "asset:code:cache:%s:%s";
    private final static String PREFIX_ASSET_CODE_MAX_CACHE_KEY = PREFIX_ASSET_CODE_CACHE_KEY + ":max";

    private final static String PREFIX_ASSET_CODE_RULE_KEY = "asset:code:rule:%s:%s:%s";

    private enum AssetCategoryCodeRuleType {
        BASIC, ASSET, NONE;
    }

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private AssetMaintenanceV2Mapper assetMaintenanceV2Mapper;

    private String getAssetCodeGroupRedisKey(String eid, String ruleKey) {
        return String.format(PREFIX_ASSET_CODE_CACHE_KEY, ruleKey, eid);
    }
    private String getAssetCodeMaxGroupRedisKey(String eid, String ruleKey) {
        return String.format(PREFIX_ASSET_CODE_MAX_CACHE_KEY, ruleKey, eid);
    }

    private String getAssetCodeRuleRedisKey(String eid, String modelCode, String sinkName) {
        return String.format(PREFIX_ASSET_CODE_RULE_KEY, modelCode, sinkName, eid);
    }

    /**
     * 刪除緩存中, 相關資產代碼
     *
     */
    private void removeAllAssetCodeCacheCore(String assetCodeGroupRangeKeys) {
        stringRedisTemplate.execute((RedisCallback<Object>) connection -> {
            Cursor<byte[]> cursor = null;
            try {
//                String assetCodeGroupRangeKeys;
//                if (isRemoveAll) {
////                    assetCodeGroupRangeKeys = "asset:code:cache:*";
//                    assetCodeGroupRangeKeys = "asset:code:*"; // 清掉所有跟資產有關的緩存
//                } else {
//                    String ruleKey = this.getRuleKey(modeCode, sinkName).orElse("");
//                    if (ruleKey.isEmpty()) {
//                        return null;
//                    }
//                    assetCodeGroupRangeKeys = String.format(PREFIX_ASSET_CODE_CACHE_KEY, ruleKey, "*");
//                }

                ScanOptions options = ScanOptions.scanOptions()
                        .match(assetCodeGroupRangeKeys)
                        .count(100) // 每次掃描建議數量
                        .build();

                cursor = connection.scan(options);
                Set<String> keysToDelete = new HashSet<>();

                while (cursor.hasNext()) {
                    String key = new String(cursor.next());
                    keysToDelete.add(key);

                    // 每收集到 50 個鍵就執行一次刪除，避免記憶體過度使用
                    if (keysToDelete.size() >= 50) {
                        stringRedisTemplate.delete(keysToDelete);
                        keysToDelete.clear();
                    }
                }

                // 刪除剩餘的鍵
                if (!keysToDelete.isEmpty()) {
                    stringRedisTemplate.delete(keysToDelete);
                }

            } catch (Exception e) {
              throw new RuntimeException(e);
            } finally {
                if (cursor != null) {
                    try {
                        cursor.close();
                    } catch (Exception e2) {
                        throw new RuntimeException(e2);
                    }
                }
            }
            return null;
        });
    }

    public void removeAllAssetCodeCache() {
        String assetCodeGroupRangeKeys = "asset:code:*"; // 清掉所有跟資產有關的緩存
        this.removeAllAssetCodeCacheCore(assetCodeGroupRangeKeys);
    }


    public void removeRangeAssetCodeCache(String modelCode, String sinkName) {
        String redisKey = String.format(PREFIX_ASSET_CODE_RULE_KEY, modelCode, sinkName, "*");
        this.removeAllAssetCodeCacheCore(redisKey);
    }

    /**
     * 刪除緩存中, 相關資產代碼
     *
     */
    public void removeAssetCodeCache(String eid, String modeCode, String sinkName) {
        String ruleKey = this.getRuleKey(eid, modeCode, sinkName).orElse("");
        if (ruleKey.isEmpty()) {
            return;
        }
        // 移除 eid + ruleKey 所產生的資產編號
        String assetCodeGroupRangeKey = getAssetCodeGroupRedisKey(eid, ruleKey);
        stringRedisTemplate.delete(assetCodeGroupRangeKey);

        // 移除 eid, modeCode, sinkName, 所對應的 ruleKey
        String ruleRedisKey = this.getAssetCodeRuleRedisKey(eid, modeCode, sinkName);
        stringRedisTemplate.delete(ruleRedisKey);
    }

    public List<String> watchInventoryAssetCodAssetCodes(String eid, String modelCode, String sinkName) {
        String ruleKey = this.getRuleKey(modelCode, sinkName).orElse("");
        String assetCodeMaxGroupKey = this.getAssetCodeMaxGroupRedisKey(eid, ruleKey);
//        Long size = Optional.ofNullable(stringRedisTemplate.opsForList().size(assetCodeMaxGroupKey)).orElse(0L);
        return stringRedisTemplate.opsForList().range(assetCodeMaxGroupKey, 0, -1);
    }

    private AssetCategoryCodeRuleType buildInventoryAssetCode(
            String eid, String ruleKey,
            Supplier<List<AssetCategoryCodingRuleSimple>> assetCodeRuleFunc,
            Function<List<AssetCategoryCodingRuleSimple>, Pair<String, Long>> curFlowNumFunc
    ) {
        //避開拼發, 同時間產生編號, 如果己經有在建立, 則就是等
        String lockKey = "lock:" + getAssetCodeGroupRedisKey(eid, ruleKey);
        Boolean lockAcquired = stringRedisTemplate.opsForValue()
                .setIfAbsent(lockKey, "1", Duration.ofSeconds(3 * 60));

        if (Boolean.FALSE.equals(lockAcquired)) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("等待鎖時被中斷", e);
            }
            return AssetCategoryCodeRuleType.NONE;
        }

        try {
            return this.startBuildInventoryAssetCode(eid, assetCodeRuleFunc, curFlowNumFunc);
        } catch(AssetCodeRuleException acre) {
            throw acre;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        } finally {
            stringRedisTemplate.delete(lockKey); // 釋放鎖
        }
    }

    private AssetCategoryCodeRuleType startBuildInventoryAssetCode(
            String eid,
            Supplier<List<AssetCategoryCodingRuleSimple>> assetCodeRuleFunc,
            Function<List<AssetCategoryCodingRuleSimple>, Pair<String, Long>> curFlowNumFunc
    ) {
        // 取出編碼規則, 跟編碼大類別
        List<AssetCategoryCodingRuleSimple> codingRuleSimples = assetCodeRuleFunc.get();
        if (codingRuleSimples.isEmpty()) {
//            throw new RuntimeException("Not Found Asset Category Coding Rule Setting, modelCode:" + modelCode);
//            return Optional.empty(); // 有些資產是不用 assetCode, 所以如果查不到編碼規則, 就表示不需要
            return AssetCategoryCodeRuleType.BASIC;
        }
        String mainCode = codingRuleSimples.get(0).getMainCode(); //編碼大類別
        if (mainCode == null || mainCode.isEmpty()) {
            long classificationId = codingRuleSimples.get(0).getClassificationId();
            throw new RuntimeException("AssetCode prefix coding is null or empty, classificationId:" + classificationId);
        }

        //資產編碼規則 key
        String ruleKey = codingRuleSimples.get(0).getRuleKey();

        if (this.hasInventoryAssetCodAssetCodes(eid, ruleKey)) {
            return AssetCategoryCodeRuleType.ASSET;
        }

        // 取得最大的資產編號
        if (curFlowNumFunc == null) {
            throw  new IllegalArgumentException("curFlowNumFunc must not be null");
        }

        //檢查緩存是否有最大編號, 如果沒有,再從DB取出
        String curFlowNum = this.getCacheMaxAssetCode(eid, ruleKey);
        Function<List<AssetCategoryCodingRuleSimple>, Long> calAlreadyUsedCountFunc;
        if (curFlowNum.isEmpty()) {
            Pair<String, Long> p = curFlowNumFunc.apply(codingRuleSimples);
            curFlowNum = p.getKey();
            calAlreadyUsedCountFunc = ruleSimples -> p.getValue(); //己使用的數量
        } else {
            calAlreadyUsedCountFunc = ruleSimples -> {
                Pair<Integer, Long> curFlowNumberPair = SERIAL_NUMBER.getCurFlowNumber(codingRuleSimples);
                return curFlowNumberPair.getValue(); //己使用的數量
            };
        }

        // 產生編號預備動作
        // 取得己使用的數量
        long alreadyUsedCount = AssetNoUtil.getInstance().setAssetNoRuleInfo(
                codingRuleSimples, mainCode, curFlowNum, calAlreadyUsedCountFunc
        );

        // 如果有設定流水號, 則依流水號動態計算, 若無則固定產生 1000 組的資產編號
        long produceCount = 1000;
        if (!codingRuleSimples.isEmpty()) {
            int[] snEncodingCounts = codingRuleSimples.stream()
                    .filter(row -> row.getRuleNumber() == SERIAL_NUMBER)
                    .map(AssetCategoryCodingRuleSimple::getRuleSettingValue)
                    .mapToInt(Integer::parseInt)
                    .toArray();
            if (snEncodingCounts.length > 0) {
                int snEncodingCount = snEncodingCounts[0];
                switch (snEncodingCount) {
                    case 1:
                        produceCount = 9 - alreadyUsedCount; // 扣除己使用的
                        break;
                    case 2:
                        produceCount = 99 - alreadyUsedCount; // 扣除己使用的
                        break;
                    case 3:
                        produceCount = 999 - alreadyUsedCount; // 扣除己使用的
                        break;
                    default:
                        // 通用計算方式
                        long maxNum = (long) Math.pow(10, snEncodingCount) - 1;
                        long remaining =  maxNum - alreadyUsedCount; // 扣除己使用的
                        if (remaining <= produceCount) {
                            produceCount = remaining;
                        }
                        break;
                }
            }
        }
        
        if (produceCount <= 0) {
            String errMsg = "assetCode SERIAL_NUMBER Rule Err: remaining count is 0, snEncodingCount has exceeded the maximum limit";
            throw new AssetCodeRuleException(SERIAL_NUMBER, errMsg, SERIAL_NUMBER.getErrCode());
        }
        
        List<String> multiAssetNoList = AssetNoUtil.getInstance().getMultiAssetNo(
                codingRuleSimples.toArray(new AssetCategoryCodingRuleSimple[0]), produceCount
        );

        // save to redis
        this.cacheAssetCodes(eid, ruleKey, multiAssetNoList);

        return AssetCategoryCodeRuleType.ASSET;
    }

    private void cacheMaxAssetCode(String eid, String ruleKey, String assetCode) {
        String assetCodeMaxGroupKey = this.getAssetCodeMaxGroupRedisKey(eid, ruleKey);
        Long size = Optional.ofNullable(stringRedisTemplate.opsForList().size(assetCodeMaxGroupKey)).orElse(0L);
        if (size > 0) {
            stringRedisTemplate.opsForList().leftPop(assetCodeMaxGroupKey);
        }
        stringRedisTemplate.opsForList().rightPush(assetCodeMaxGroupKey, assetCode);
        stringRedisTemplate.expire(assetCodeMaxGroupKey, Duration.ofDays(14));
    }

    private String getCacheMaxAssetCode(String eid, String ruleKey) {
        String assetCodeMaxGroupKey = this.getAssetCodeMaxGroupRedisKey(eid, ruleKey);
        return Optional.ofNullable(stringRedisTemplate.opsForList().leftPop(assetCodeMaxGroupKey)).orElse("");
    }
    
    private void cacheAssetCodes(String eid, String ruleKey, List<String> multiAssetNoList) {
        String assetCodeGroupKey = this.getAssetCodeGroupRedisKey(eid, ruleKey);
        stringRedisTemplate.opsForList().rightPushAll(assetCodeGroupKey, multiAssetNoList);
        stringRedisTemplate.expire(assetCodeGroupKey, Duration.ofDays(14));
    }

    private boolean hasInventoryAssetCodAssetCodes(String eid, String ruleKey) {
        String assetCodeGroupKey = this.getAssetCodeGroupRedisKey(eid, ruleKey);

        // Lua 腳本確保原子性
        String script = "return redis.call('LLEN', KEYS[1]) > 0 and 1 or 0";

        Object result = stringRedisTemplate.execute(
                RedisScript.of(script, Long.class),
                Collections.singletonList(assetCodeGroupKey)
        );

        if (result instanceof Long) {
            return (Long) result > 0;
        } else if (result instanceof Number) {
            return ((Number) result).longValue() > 0;
        }
        return false;
    }

    protected Optional<String> getCacheAssetCode(
            String eid, String ruleKey,
//            String modelCode, String sinkName,
            Supplier<List<AssetCategoryCodingRuleSimple>> assetCodeRuleFunc,
            Function<List<AssetCategoryCodingRuleSimple>, Pair<String, Long>> curFlowNumFunc,
            int retryCount
    ) {

        String assetCodeGroupKey = this.getAssetCodeGroupRedisKey(eid, ruleKey);

        //step1. 從庫存取 assetCode
        String assetCode = stringRedisTemplate.opsForList().leftPop(assetCodeGroupKey);
        assetCode = Optional.ofNullable(assetCode).orElse("");
        if (!assetCode.isEmpty()) {
            this.cacheMaxAssetCode(eid, ruleKey, assetCode); //緩存當前最大資產編號
            return Optional.of(assetCode);
        }

        //step2. 檢查庫存, 如果沒有庫存, 則重建 assetCode 的庫存
        AssetCategoryCodeRuleType assetCategoryCodeRuleType = this.buildInventoryAssetCode(
                eid, ruleKey, assetCodeRuleFunc, curFlowNumFunc
        );
        if (AssetCategoryCodeRuleType.BASIC.equals(assetCategoryCodeRuleType)) {
            //檢查是不是categoryType = BASIC , 如果是, 則是不用產生任何的資產編號
            return Optional.empty();
        }

        if (retryCount <= 5) {
            retryCount++;
            return this.getCacheAssetCode(eid, ruleKey, assetCodeRuleFunc, curFlowNumFunc, retryCount);
        } else {
            String errMsg = String.format("get AssetCode error, eid:%s, ruleKey:%s", eid, ruleKey);
            throw new RuntimeException(errMsg);
        }
    }


    // 取得編碼規則
    protected List<AssetCategoryCodingRuleSimple> getAssetCategoryCodingRuleSimple(String modelCode, String sinkName) {
        // 取出編碼規則
        Map<String, Object> params = new HashMap<>();
        params.put("modelCode", modelCode);
        params.put("sinkName", sinkName);
        return assetMaintenanceV2Mapper.selectAssetCodingRuleSetting(params);
    }

    protected Optional<String> getRuleKey(String eid, String modelCode, String sinkName) {
        String redisKey = this.getAssetCodeRuleRedisKey(eid, modelCode, sinkName);
        String ruleKey = Optional.ofNullable(stringRedisTemplate.opsForValue().get(redisKey)).orElse("");
        if (ruleKey.isEmpty()) {
            List<AssetCategoryCodingRuleSimple> data = this.getAssetCategoryCodingRuleSimple(modelCode, sinkName);
            if (data.isEmpty()) {
                // 若查不到任何的編碼規則, 則表示, 該資產不用產生資產編號.
                // 但要防止批量操作時, 一直往 db 讀取, 故用 redis 來當緩衝
                stringRedisTemplate.opsForValue().set(redisKey, ruleKey, Duration.ofSeconds(300));
                return Optional.empty();
            }
            ruleKey = data.get(0).getRuleKey();
            stringRedisTemplate.opsForValue().set(redisKey, ruleKey, Duration.ofDays(14));
        }
        return Optional.of(ruleKey);
    }
    
    private Optional<String> getRuleKey(String modelCode, String sinkName) {
        return Optional.ofNullable(stringRedisTemplate.execute((RedisCallback<String>) connection -> {
            Cursor<byte[]> cursor = null;
            try {
                String pattern = String.format(PREFIX_ASSET_CODE_RULE_KEY, modelCode, sinkName, "*");
                ScanOptions options = ScanOptions.scanOptions()
                        .match(pattern)
                        .count(100)
                        .build();

                cursor = connection.scan(options);
                if (cursor.hasNext()) {
                    byte[] key = cursor.next();
                    return stringRedisTemplate.opsForValue().get(new String(key));
                }
                return null;
            } finally {
                if (cursor != null) {
                    try {
                        cursor.close();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }));
    }
}
