package com.digiwin.escloud.issueservice.services;

import com.digiwin.escloud.issueservice.utils.MessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2020/3/30-15:22
 */
@Slf4j
@Service
public class CommonMailService {

    @Autowired
    private MessageUtils messageUtils;

    private HashMap<String, String> mailTemplateContent = new HashMap<>();

    /**
     * 获取邮件模板中的内容
     * 
     * @param fileName
     * @param lang
     * @return
     */
    public String readMailContent(String fileName, String lang) {
        lang = messageUtils.getLanguage(lang);
        String content = "%s";
        StringBuffer sbStr = new StringBuffer();
        try {
            String templeteKey = fileName + "_" + lang;
            content = mailTemplateContent.get(templeteKey);
            if (content != null && !content.isEmpty()) {
                return content;
            } else {
                content = "%s";
            }
            Resource resource = new PathMatchingResourcePatternResolver()
                .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + lang + "/" + fileName);
            InputStream inputStream = resource.getInputStream();
            InputStreamReader read = new InputStreamReader(inputStream, "UTF-8");
            BufferedReader ins = new BufferedReader(read);
            String dataLine = "";
            while (null != (dataLine = ins.readLine())) {
                sbStr.append(dataLine);
            }
            ins.close();
            content = sbStr.toString();
            mailTemplateContent.put(templeteKey, content);
        } catch (Exception ex) {
            log.error("获取邮件内容失败:---" + ex.getMessage());
        }
        return content;
    }
}
