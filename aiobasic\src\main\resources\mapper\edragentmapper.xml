<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.digiwin.escloud.aiobasic.edrv2.dao.EdrAgentMapper">
    <select id="getAgent" parameterType="com.digiwin.escloud.aiobasic.edrv2.model.EdrAgentParam"
            resultType="com.digiwin.escloud.aiobasic.report.model.EdrAgentField">
        SELECT DISTINCT
        sa.id, sa.eid, ss.accountId, sa.siteId, sa.agentId, sa.agentUUId, sa.siteName, sa.endpointName, sa.lastReportedIP,
        sa.groupTitle, sa.deviceType, sa.modelName, sa.os, sa.architecture, sa.osName, sa.osRevision,
        sa.lastRebootDate, sa.operationalState, sa.healthState, sa.managmentConnectivity, sa.subscribedOn,
        sa.lastActive, sa.agentClosedTime, sa.agentPreActiveTime, sa.scanStatus, sa.lastSuccessfulScanDate,
        sa.isPendingUninstall, sa.isUninstalled, sa.networkStatus, sa.createdAt, sa.updatedAt, sa.finalSuccessfulScanDate,
        CASE
        WHEN sa.deviceType LIKE '%server%' THEN 'SRV'
        ELSE 'PC'
        END AS agentType,

        sst.status AS planStatus,
        sst.crons,
        lastTime.lastExecutionTime,
        sa.isWarningEnable

        FROM sentinelone_agents sa
        LEFT JOIN sentinelone_sites AS ss ON ss.id = sa.siteId
        LEFT JOIN sentinelone_scanplan_agents AS ssa ON ssa.saId = sa.id AND ssa.isRemove = 0
        LEFT JOIN sentinelone_scanplan_tasks AS sst ON sst.id = ssa.sstId
        LEFT JOIN (
            SELECT ssta.sstId, MAX(ssta.createdAt) AS LastExecutionTime
            FROM sentinelone_scanplan_tasks_activity ssta
            GROUP BY ssta.sstId
        ) AS lastTime ON lastTime.sstId = sst.id

        WHERE 1=1
        AND sa.isUninstalled != 1
        AND ss.state = 'active'
        <if test="siteId != null and siteId != ''">
            AND siteId = #{siteId}
        </if>
        <if test="siteIds != null and !siteIds.isEmpty">
            AND siteId IN (
                <foreach collection="siteIds" item="id" separator=",">
                    #{id}
                </foreach>
            )
        </if>
        <if test="agentIds != null and !agentIds.isEmpty">
            AND agentId IN (
            <foreach collection="agentIds" item="agentId" separator=",">
                #{agentId}
            </foreach>
            )
        </if>
        <if test="eidList != null and !eidList.isEmpty">
            AND sa.eid IN (
                <foreach collection="eidList" item="eid" separator=",">
                    #{eid}
                </foreach>
            )
        </if>
        <if test="devices != null and !devices.isEmpty">
            AND (
            <foreach collection="devices" item="endpointName" separator="OR">
                LOWER(endpointName) LIKE LOWER(CONCAT('%', #{endpointName}, '%'))
            </foreach>
            )
        </if>
        <if test="ips != null and !ips.isEmpty">
            AND (
            <foreach collection="ips" item="lastReportedIP" separator="OR">
                lastReportedIP LIKE CONCAT('%', #{lastReportedIP}, '%')
            </foreach>
            )
        </if>
        <trim prefix="AND" prefixOverrides="AND | OR">
            <if test="states != null and !states.isEmpty">
                (
                    <if test="states.contains('PendingUninstall')">
                        isPendingUninstall = 1 OR
                    </if>
                    operationalState IN (
                        <foreach collection="states" item="operationalState" separator=",">
                            #{operationalState}
                        </foreach>
                    )
                )
            </if>
        </trim>
        <if test="healthState != null and !healthState.isEmpty">
            AND healthState IN (
            <foreach collection="healthState" item="state" separator=",">
                #{state}
            </foreach>
            )
        </if>
        <if test="managmentConnectivity != null and !managmentConnectivity.isEmpty">
            AND managmentConnectivity IN (
            <foreach collection="managmentConnectivity" item="state" separator=",">
                #{state}
            </foreach>
            )
        </if>
        <if test="networkStatus != null and !networkStatus.isEmpty and networkStatus[0] != ''">
            AND networkStatus IN
            <foreach collection="networkStatus" item="status" separator="," open="(" close=")">
                <choose>
                    <when test="status == 'Connected'">
                        #{status}, 'Connecting'
                    </when>
                    <when test="status == 'Disconnected'">
                        #{status}, 'Disconnecting'
                    </when>
                    <otherwise>
                        #{status}
                    </otherwise>
                </choose>
            </foreach>
        </if>
        <if test="scanStatus != null and !scanStatus.isEmpty() and scanStatus[0] != null">
            <foreach collection="scanStatus" item="status" separator="," open="AND scanStatus IN (" close=")">
                #{status}
            </foreach>
        </if>
        <if test="planStatus != null and !planStatus.isEmpty()">
            AND (
            <foreach collection="planStatus" item="status" separator=" OR ">
                <choose>
                    <when test="status == -1">
                        (sst.status IS NULL OR sst.status NOT IN (0, 1))
                    </when>
                    <otherwise>
                        sst.status = #{status}
                    </otherwise>
                </choose>
            </foreach>
            )
        </if>

        <if test="operatingSystems != null and !operatingSystems.isEmpty">
            AND osName IN (
                <foreach collection="operatingSystems" item="osName" separator=",">
                    #{osName}
                </foreach>
            )
        </if>
        <if test="lastSeenStart != null and lastSeenEnd != null">
            AND lastActive &gt;=#{lastSeenStart} AND lastActive &lt;=#{lastSeenEnd}
        </if>
        <if test="isPendingUninstall != null">
            AND isPendingUninstall = #{isPendingUninstall}
        </if>

        ORDER BY lastActive desc
        <if test="itemsPerPageStart != null and itemsPerPage != null">
            LIMIT #{itemsPerPageStart}, #{itemsPerPage}
        </if>
    </select>

    <select id="getOSTotalAmount" resultType="java.util.Map">
        SELECT osName as 'title', COUNT(osName) as 'count'
        FROM sentinelone_agents
        WHERE 1=1
        <if test="eid != null and eid != ''">
            AND eid = #{eid}
        </if>
        <if test="timeFrom != null and timeTo != null">
            AND lastActive &gt;=#{timeFrom} AND lastActive &lt;=#{timeTo}
        </if>
        GROUP BY osName
    </select>

    <insert id="upsertAgent">
        INSERT INTO sentinelone_agents
        (id, eid, siteId, agentId, agentUUId, siteName, endpointName, lastReportedIP, groupTitle, deviceType, modelName,
        os, architecture, osName, osRevision, lastRebootDate, operationalState, healthState, managmentConnectivity,
        subscribedOn, lastActive, scanStatus, lastSuccessfulScanDate, isPendingUninstall, isUninstalled, networkStatus,
         finalSuccessfulScanDate, pendingUninstallTime)
        VALUES
        (#{id}, #{eid}, #{siteId}, #{agentId}, #{agentUUId}, #{siteName}, #{endpointName}, #{lastReportedIP},
        #{groupTitle}, #{deviceType}, #{modelName}, #{os}, #{architecture}, #{osName}, #{osRevision}, #{lastRebootDate},
        #{operationalState}, #{healthState}, #{managmentConnectivity}, #{subscribedOn}, #{lastActive}, #{scanStatus},
         #{lastSuccessfulScanDate}, #{isPendingUninstall}, #{isUninstalled}, #{networkStatus}, #{finalSuccessfulScanDate},
         #{pendingUninstallTime})
        ON DUPLICATE KEY UPDATE
        eid = IF(VALUES(eid) IS NULL, eid, VALUES(eid)),
        siteName = IF(VALUES(siteName) IS NULL, siteName, VALUES(siteName)),
        endpointName = IF(VALUES(endpointName) IS NULL, endpointName, VALUES(endpointName)),
        lastReportedIP = IF(VALUES(lastReportedIP) IS NULL, lastReportedIP, VALUES(lastReportedIP)),
        groupTitle = IF(VALUES(groupTitle) IS NULL, groupTitle, VALUES(groupTitle)),
        deviceType = IF(VALUES(deviceType) IS NULL, deviceType, VALUES(deviceType)),
        modelName = IF(VALUES(modelName) IS NULL, modelName, VALUES(modelName)),
        os = IF(VALUES(os) IS NULL, os, VALUES(os)),
        architecture = IF(VALUES(architecture) IS NULL, architecture, VALUES(architecture)),
        osName = IF(VALUES(osName) IS NULL, osName, VALUES(osName)),
        osRevision = IF(VALUES(osRevision) IS NULL, osRevision, VALUES(osRevision)),
        lastRebootDate = IF(VALUES(lastRebootDate) IS NULL, lastRebootDate, VALUES(lastRebootDate)),
        operationalState = IF(VALUES(operationalState) IS NULL, operationalState, VALUES(operationalState)),
        healthState = IF(VALUES(healthState) IS NULL, healthState, VALUES(healthState)),
        managmentConnectivity = IF(VALUES(managmentConnectivity) IS NULL, managmentConnectivity, VALUES(managmentConnectivity)),
        subscribedOn = IF(VALUES(subscribedOn) IS NULL, subscribedOn, VALUES(subscribedOn)),
        lastActive = IF(VALUES(lastActive) IS NULL, lastActive, VALUES(lastActive)),
        scanStatus = IF(VALUES(scanStatus) IS NULL, scanStatus, VALUES(scanStatus)),
        lastSuccessfulScanDate = IF(VALUES(lastSuccessfulScanDate) IS NULL, lastSuccessfulScanDate, VALUES(lastSuccessfulScanDate)),
        isPendingUninstall = IF(VALUES(isPendingUninstall) IS NULL, isPendingUninstall, VALUES(isPendingUninstall)),
        isUninstalled = IF(VALUES(isUninstalled) IS NULL, isUninstalled, VALUES(isUninstalled)),
        networkStatus = IF(VALUES(networkStatus) IS NULL, networkStatus, VALUES(networkStatus)),
        finalSuccessfulScanDate = IF(VALUES(finalSuccessfulScanDate) IS NULL, finalSuccessfulScanDate, VALUES(finalSuccessfulScanDate)),
        pendingUninstallTime = IF(VALUES(pendingUninstallTime) IS NULL, pendingUninstallTime, VALUES(pendingUninstallTime))
    </insert>

    <select id="getAgentSiteIdByEid" resultType="java.util.Map">
        SELECT sa.siteId, ss.accountId
        FROM sentinelone_agents sa
        LEFT JOIN sentinelone_sites ss ON ss.id = sa.siteId
        WHERE sa.eid=#{eid}
        GROUP BY sa.siteId
    </select>

    <update id="updateAgentStatus">
        UPDATE sentinelone_agents
        SET operationalState=#{operationalState}, agentClosedTime=#{agentClosedTime}
        WHERE agentId IN (
            <foreach collection="agentIds" item="agentId" separator=",">
                #{agentId}
            </foreach>
            )
    </update>

    <delete id="removeAgent">
        DELETE FROM sentinelone_agents WHERE siteId=#{siteId}
        <if test="agentId != null and agentId != ''">
            AND agentId=#{agentId}
        </if>
        <if test="agentIdList != null">
            AND agentId in (
                <foreach collection="agentIdList" item="id" separator=",">
                    #{id}
                </foreach>
            )
        </if>
    </delete>

    <select id="getAgentByAgentId" resultType="com.digiwin.escloud.aiobasic.report.model.EdrAgentField">
        SELECT id, eid, siteId, agentId, agentUUId, siteName, endpointName, lastReportedIP, groupTitle,
            deviceType, modelName, os, architecture, osName, osRevision, lastRebootDate, operationalState,
            healthState, managmentConnectivity, subscribedOn, lastActive, agentClosedTime, agentPreActiveTime,
            createdAt, updatedAt, isPendingUninstall, isUninstalled
        FROM sentinelone_agents
        WHERE siteId=#{siteId}
        AND agentId IN (
        <foreach collection="agentIdList" item="agentId" separator=",">
            #{agentId}
        </foreach>
        )
    </select>

    <select id="getOsNameList" resultType="java.lang.String">
        SELECT DISTINCT osName FROM sentinelone_agents
        WHERE 1=1
        <if test="eid!=null">
            AND eid = #{eid}
        </if>
    </select>

    <update id="moveAgent">
        UPDATE sentinelone_agents
        SET siteName=#{siteName}, groupTitle=#{groupTitle}
        WHERE agentId=#{agentId}
    </update>

    <select id="getHealthTotalAmount" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN healthState = 'Infected' THEN 1 ELSE 0 END) AS infectedCount,
            SUM(CASE WHEN healthState = 'Healthy' THEN 1 ELSE 0 END) AS healthyCount
        FROM sentinelone_agents
        WHERE eid = #{eid}
        <if test="siteIds != null and !siteIds.isEmpty">
            AND siteId IN (
                <foreach collection="siteIds" item="siteId" separator=",">
                    #{siteId}
                </foreach>
            )
        </if>
    </select>

    <update id = "updateAgentNetworkStatus">
        <foreach collection="IDS" item="ID" separator=";">
            UPDATE
                sentinelone_agents
            SET
                networkStatus = #{status}
            WHERE
                siteId = #{ID.siteId} AND agentId = #{ID.agentId}
        </foreach>
    </update>

    <select id="getAgentById" parameterType="java.lang.String"
            resultType="com.digiwin.escloud.aiobasic.report.model.EdrAgentField">
        SELECT id, eid, siteId, agentId
        FROM sentinelone_agents
        WHERE id IN (
            <foreach collection="idList" item="id" separator=",">
                #{id}
            </foreach>
            )
    </select>

    <update id="updateAgentScanStatus">
        UPDATE sentinelone_agents
        SET scanStatus = #{scanStatus}, lastSuccessfulScanDate = NOW()
        WHERE siteId IN
        <foreach collection="siteIds" item="siteId" separator="," open="(" close=")">
            #{siteId}
        </foreach>
        AND agentId IN
        <foreach collection="agentIds" item="agentId" separator="," open="(" close=")">
            #{agentId}
        </foreach>
    </update>

    <update id="updateIsUninstalled">
        UPDATE sentinelone_agents
        SET isUninstalled = 1
        WHERE siteId = #{siteId}
        AND agentId = #{agentId}
    </update>

    <update id="updateIsPendingUninstall">
        UPDATE sentinelone_agents
        SET isPendingUninstall = 0
        WHERE siteId = #{siteId}
        AND agentId = #{agentId}
    </update>

    <update id="updateAgentIsWarning" parameterType="com.digiwin.escloud.aiobasic.edrv2.model.EdrAgentIsWarningParam">
        UPDATE sentinelone_agents
        SET isWarningEnable = #{isWarning}
        WHERE siteId = #{siteId}
        <foreach collection="agentIds" item="agentId" open="AND agentId IN (" separator="," close=")">
            #{agentId}
        </foreach>
    </update>

    <select id="selectAgentIsWarning" parameterType="java.lang.String"
            resultType="com.digiwin.escloud.aiobasic.edrv2.model.EdrAgentIsWarning">
        SELECT siteId, agentId, isWarningEnable isWarning, pendingUninstallTime
        FROM sentinelone_agents
        WHERE 1=1
        <if test="siteId != null and siteId != ''">
            AND siteId = #{siteId}
        </if>
        <if test="isWarning != null">
            AND isWarningEnable = #{isWarning}
        </if>
    </select>
</mapper>