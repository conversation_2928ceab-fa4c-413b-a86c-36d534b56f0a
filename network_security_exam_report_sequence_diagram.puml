@startuml
title 网安稽查报告生成时序图 (数据库报告方案)

participant "后台服务(网安稽查服务)" as NetworkSecurityService
participant "后台服务(一键体检服务)" as AiopsExamService  
participant "Mysql" as MySQL
participant "后台服务(数据库报告服务)" as DbReportService
participant "大数据平台" as BigDataPlatform
participant "ES" as ElasticSearch
participant "后台服务(AI服务)" as AioAIService

== 异步报告生成流程 ==

NetworkSecurityService -> NetworkSecurityService: saveNetworkSecurityExamReport2Es(record)
note right: 异步任务开始

NetworkSecurityService -> AiopsExamService: getReportScore(aerId, scale)
note right: 发起查询体检记录分数请求
AiopsExamService -> MySQL: 查询体检记录数据
MySQL --> AiopsExamService: 返回分数数据
AiopsExamService --> NetworkSecurityService: 返回分数数据

NetworkSecurityService -> DbReportService: getDbReportData(id, dbType, productCode)
note right: 发起查询指标检测值数据
DbReportService -> BigDataPlatform: 查询指标检测值数据
note right: 使用bigDataUtil查询StarRocks
BigDataPlatform --> DbReportService: 返回指标检测值数据

DbReportService -> MySQL: 查询指标参考值数据
MySQL --> DbReportService: 返回指标参考值数据

DbReportService -> DbReportService: processActualValue.processValue()
note right: 整理参考值、检测值数据\n按规则拼接检测值

DbReportService --> NetworkSecurityService: 返回指标检测值数据

NetworkSecurityService -> BigDataPlatform: warningService.getDailyWarningInfo()
note right: 发起查询预警数据
BigDataPlatform --> NetworkSecurityService: 返回预警数据

NetworkSecurityService -> NetworkSecurityService: 组合报告数据和分数数据、预警数据

NetworkSecurityService -> AioAIService: chatGptService.EvaluationConclusion()
note right: 发起报告结论生成请求
AioAIService -> AioAIService: 发起生成报告结论API
note right: 调用IndepthAI服务
AioAIService -> AioAIService: 阻塞等待报告结论
note right: countDownLatch.await(5, TimeUnit.MINUTES)
AioAIService --> NetworkSecurityService: 返回报告结论

NetworkSecurityService -> ElasticSearch: networkSecurityExamEsReport.generateReport()
note right: 存储报告详情\n(不再存储组织类数据)
ElasticSearch --> NetworkSecurityService: 存储完成

NetworkSecurityService -> MySQL: aiopsExamRecordMapper.updateReportStatus()
note right: 更新报告记录状态
MySQL --> NetworkSecurityService: 更新完成

@enduml
