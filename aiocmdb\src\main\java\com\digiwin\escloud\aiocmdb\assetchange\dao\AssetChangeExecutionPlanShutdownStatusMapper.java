package com.digiwin.escloud.aiocmdb.assetchange.dao;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeExecutionPlanShutdownStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 执行计划停机状况 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Mapper
public interface AssetChangeExecutionPlanShutdownStatusMapper {

    /**
     * 插入执行计划停机状况
     *
     * @param shutdownStatus 执行计划停机状况
     * @return 影响行数
     */
    int insert(AssetChangeExecutionPlanShutdownStatus shutdownStatus);

    /**
     * 批量插入执行计划停机状况
     *
     * @param shutdownStatusList 执行计划停机状况列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<AssetChangeExecutionPlanShutdownStatus> shutdownStatusList);

    /**
     * 根据计划ID删除执行计划停机状况
     *
     * @param planId 计划ID
     * @return 影响行数
     */
    int deleteByPlanId(@Param("planId") Long planId);

    /**
     * 根据计划ID列表批量删除执行计划停机状况
     *
     * @param planIdList 计划ID列表
     * @return 影响行数
     */
    int deleteByPlanIdList(@Param("planIdList") List<Long> planIdList);

    /**
     * 根据计划ID查询执行计划停机状况
     *
     * @param planId 计划ID
     * @return 执行计划停机状况
     */
    AssetChangeExecutionPlanShutdownStatus selectByPlanId(@Param("planId") Long planId);
}
