package com.digiwin.escloud.aiocmdb.assetchange;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationAssetList;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationExecutionPlan;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeExecutionPlanShutdownStatus;
import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationSaveRequest;
import com.digiwin.escloud.aiocmdb.assetchange.service.IAssetChangeApplicationService;
import com.digiwin.escloud.common.model.ResponseBase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 资产变更申请单测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class AssetChangeApplicationTest {

    @Autowired
    private IAssetChangeApplicationService assetChangeApplicationService;

    @Test
    public void testSaveAssetChangeApplication() {
        // 创建测试数据
        AssetChangeApplicationSaveRequest request = createTestRequest();
        
        // 执行保存操作
        ResponseBase response = assetChangeApplicationService.saveAssetChangeApplication(request);
        
        // 验证结果
        System.out.println("保存结果: " + response);
        assert response.checkIsSuccess() : "保存操作应该成功";
    }

    private AssetChangeApplicationSaveRequest createTestRequest() {
        AssetChangeApplicationSaveRequest request = new AssetChangeApplicationSaveRequest();

        // 创建主表数据
        AssetChangeApplication application = new AssetChangeApplication();
        application.setEid(123456L);
        application.setApplicationDate(LocalDateTime.now());
        application.setApplicantUserId("test_user_001");
        application.setApplicantName("测试用户");
        application.setApplicantUnit("测试部门");
        application.setApplicationCategory("系统升级");
        application.setApplicationStatus("待审批");
        application.setChangeBackgroundReason("系统版本过旧，需要升级到最新版本");
        application.setChangeContentDescription("升级操作系统和相关软件");
        application.setChangePriority("高");
        application.setRiskAssessment("风险较低，有完整的回滚方案");
        application.setRollbackPlan("如升级失败，立即回滚到原版本");

        request.setApplication(application);

        // 创建资产清单
        List<AssetChangeApplicationAssetList> assetList = new ArrayList<>();
        AssetChangeApplicationAssetList asset1 = new AssetChangeApplicationAssetList();
        asset1.setAssetCategoryId(1L);
        asset1.setModelCode("SERVER001");
        asset1.setAssetId("SRV-TEST-001");
        assetList.add(asset1);

        AssetChangeApplicationAssetList asset2 = new AssetChangeApplicationAssetList();
        asset2.setAssetCategoryId(2L);
        asset2.setModelCode("DATABASE001");
        asset2.setAssetId("DB-TEST-001");
        assetList.add(asset2);

        request.setAssetList(assetList);

        // 创建执行计划及停机状况
        List<AssetChangeApplicationSaveRequest.ExecutionPlanWithShutdownStatus> executionPlanList = new ArrayList<>();
        
        AssetChangeApplicationSaveRequest.ExecutionPlanWithShutdownStatus planWithStatus = 
                new AssetChangeApplicationSaveRequest.ExecutionPlanWithShutdownStatus();

        // 执行计划
        AssetChangeApplicationExecutionPlan executionPlan = new AssetChangeApplicationExecutionPlan();
        executionPlan.setPlanStartDate(LocalDate.now().plusDays(2));
        executionPlan.setPlanEndDate(LocalDate.now().plusDays(3));
        executionPlan.setPlanStatus("计划中");
        planWithStatus.setExecutionPlan(executionPlan);

        // 停机状况
        AssetChangeExecutionPlanShutdownStatus shutdownStatus = new AssetChangeExecutionPlanShutdownStatus();
        shutdownStatus.setIsShutdownRequired(true);
        shutdownStatus.setEstimatedShutdownStartTime(LocalDateTime.now().plusDays(2).withHour(2).withMinute(0));
        shutdownStatus.setEstimatedShutdownEndTime(LocalDateTime.now().plusDays(2).withHour(6).withMinute(0));
        shutdownStatus.setShutdownDuration(4.0);
        shutdownStatus.setShutdownExplanation("系统升级需要停机维护，预计4小时完成");
        planWithStatus.setShutdownStatus(shutdownStatus);

        executionPlanList.add(planWithStatus);
        request.setExecutionPlanList(executionPlanList);

        return request;
    }
}
