package com.digiwin.escloud.aiocmdb.assetmaintenancev2.service;

import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetRelatedMap;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;

/**
 * 资产关联映射服务测试类
 * 用于验证反向关联功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class AssetRelatedMapServiceTest {

    /**
     * 测试反向关联数据结构
     */
    @Test
    public void testReverseRelatedAssetStructure() {
        // 创建测试数据
        AssetRelatedMap assetRelatedMap = new AssetRelatedMap();
        assetRelatedMap.setEid("1001");
        assetRelatedMap.setPrimaryModelCode("SERVER");
        assetRelatedMap.setPrimarySinkName("ServerAssets");
        assetRelatedMap.setPrimaryAssetId(12345L);
        assetRelatedMap.setAssociatedModelCode("NETWORK");
        assetRelatedMap.setAssociatedSinkName("NetworkAssets");
        assetRelatedMap.setAssociatedAssetId(67890L);

        // 创建反向关联数据
        List<AssetRelatedMap.ReverseRelatedAsset> reverseAssets = new ArrayList<>();
        
        AssetRelatedMap.ReverseRelatedAsset reverseAsset1 = new AssetRelatedMap.ReverseRelatedAsset();
        reverseAsset1.setBeAssociatedModelCode("DATABASE");
        reverseAsset1.setBeAssociatedSinkName("DatabaseAssets");
        reverseAsset1.setBeAssociatedAssetId(11111L);
        reverseAsset1.setCurrentAssetModelCode("APPLICATION");
        reverseAsset1.setCurrentAssetSinkName("ApplicationAssets");
        reverseAsset1.setCurrentAssetId(22222L);
        
        AssetRelatedMap.ReverseRelatedAsset reverseAsset2 = new AssetRelatedMap.ReverseRelatedAsset();
        reverseAsset2.setBeAssociatedModelCode("STORAGE");
        reverseAsset2.setBeAssociatedSinkName("StorageAssets");
        reverseAsset2.setBeAssociatedAssetId(33333L);
        reverseAsset2.setCurrentAssetModelCode("BACKUP");
        reverseAsset2.setCurrentAssetSinkName("BackupAssets");
        reverseAsset2.setCurrentAssetId(44444L);
        
        reverseAssets.add(reverseAsset1);
        reverseAssets.add(reverseAsset2);
        
        assetRelatedMap.setReverseRelatedAssets(reverseAssets);

        // 验证数据结构
        assert assetRelatedMap.getReverseRelatedAssets() != null;
        assert assetRelatedMap.getReverseRelatedAssets().size() == 2;
        
        AssetRelatedMap.ReverseRelatedAsset firstReverse = assetRelatedMap.getReverseRelatedAssets().get(0);
        assert "DATABASE".equals(firstReverse.getBeAssociatedModelCode());
        assert "APPLICATION".equals(firstReverse.getCurrentAssetModelCode());
        assert firstReverse.getBeAssociatedAssetId() == 11111L;
        assert firstReverse.getCurrentAssetId() == 22222L;
        
        System.out.println("反向关联数据结构测试通过");
    }

    /**
     * 测试数据转换逻辑
     * 验证反向关联数据如何转换为正向关联数据
     */
    @Test
    public void testReverseToForwardConversion() {
        // 模拟反向关联转换逻辑
        AssetRelatedMap.ReverseRelatedAsset reverseAsset = new AssetRelatedMap.ReverseRelatedAsset();
        reverseAsset.setBeAssociatedModelCode("DATABASE");
        reverseAsset.setBeAssociatedSinkName("DatabaseAssets");
        reverseAsset.setBeAssociatedAssetId(11111L);
        reverseAsset.setCurrentAssetModelCode("APPLICATION");
        reverseAsset.setCurrentAssetSinkName("ApplicationAssets");
        reverseAsset.setCurrentAssetId(22222L);

        // 转换为正向关联
        AssetRelatedMap forwardMap = new AssetRelatedMap();
        forwardMap.setEid("1001");
        
        // be开头的字段作为primary字段
        forwardMap.setPrimaryModelCode(reverseAsset.getBeAssociatedModelCode());
        forwardMap.setPrimarySinkName(reverseAsset.getBeAssociatedSinkName());
        forwardMap.setPrimaryAssetId(reverseAsset.getBeAssociatedAssetId());
        
        // current开头的字段作为associated字段
        forwardMap.setAssociatedModelCode(reverseAsset.getCurrentAssetModelCode());
        forwardMap.setAssociatedSinkName(reverseAsset.getCurrentAssetSinkName());
        forwardMap.setAssociatedAssetId(reverseAsset.getCurrentAssetId());

        // 验证转换结果
        assert "DATABASE".equals(forwardMap.getPrimaryModelCode());
        assert "DatabaseAssets".equals(forwardMap.getPrimarySinkName());
        assert forwardMap.getPrimaryAssetId() == 11111L;
        assert "APPLICATION".equals(forwardMap.getAssociatedModelCode());
        assert "ApplicationAssets".equals(forwardMap.getAssociatedSinkName());
        assert forwardMap.getAssociatedAssetId() == 22222L;
        
        System.out.println("反向关联转换逻辑测试通过");
    }
}
