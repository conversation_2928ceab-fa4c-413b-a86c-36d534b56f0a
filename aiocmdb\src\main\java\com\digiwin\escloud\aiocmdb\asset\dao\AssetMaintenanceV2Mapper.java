package com.digiwin.escloud.aiocmdb.asset.dao;

import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSimple;
import com.digiwin.escloud.aiocmdb.asset.model.AssetRelatedCategoryClassification;
import com.digiwin.escloud.aiocmdb.asset.model.AssetRelatedCategoryClassificationTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface AssetMaintenanceV2Mapper {

    List<AssetRelatedCategoryClassification> selectAssetCategory(Map<String, Object> params);

    List<AssetCategoryCodingRuleSimple> selectAssetCodingRuleSetting(Map<String, Object> params);

    String selectAssetPrefixCoding(@Param("classificationId") long classificationId);

    List<AssetRelatedCategoryClassificationTree> selectAssetCategoryClassification();

}
