package com.digiwin.escloud.aiouser.service.iamapp.impl.user;

import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.aiouser.config.UserNotifyConfig;
import com.digiwin.escloud.aiouser.dao.ITenantDao;
import com.digiwin.escloud.aiouser.dao.ITenantNotifyDao;
import com.digiwin.escloud.aiouser.dao.IUserDao;
import com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyContact;
import com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyModuleSetting;
import com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifySettingResult;
import com.digiwin.escloud.aiouser.model.user.CountingUser;
import com.digiwin.escloud.aiouser.model.user.User;
import com.digiwin.escloud.aiouser.model.user.UsersIntoTenantParam;
import com.digiwin.escloud.aiouser.service.impl.UserBaseService;
import com.digiwin.escloud.aiouser.util.CommonUtils;
import com.digiwin.escloud.common.constant.AioPlatformEnum;
import com.digiwin.escloud.common.feign.UserV2FeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.integration.api.cac.req.CountReq;
import com.digiwin.escloud.integration.api.iam.req.tenant.TenantVO;
import com.digiwin.escloud.integration.common.CacRes;
import com.digiwin.escloud.integration.service.CacService;
import com.digiwin.escloud.integration.service.IamService;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @Date: 2024-10-15 17:03
 * @Description
 */
@Slf4j
@Service
public abstract class AddUserBase {

    @Value("${digiwin.token.user.verifyuserid}")
    private String verifyUserId;
    @Value("${digiwin.token.tenant.id}")
    private String verifytenantId;
    @Value("${digiwin.user.defaultserviceregion}")
    protected String defaultServiceRegion;
    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    private Long defaultSid;
    @Autowired
    protected UserV2FeignClient userV2FeignClient;
    @Autowired
    protected IamService iamService;
    @Autowired
    protected UserBaseService userBaseService;
    @Autowired
    private ITenantDao tenantDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private ITenantNotifyDao tenantNotifyDao;
    @Autowired
    private CacService cacService;
    @Autowired
    private UserNotifyConfig userNotifyConfig;
    private CommonUtils commonUtils;

    @Data
    @AllArgsConstructor
    protected class AuthorizeRes {
        private String userId;
        private String esUserId;
        @JsonProperty("isSuccess")
        private boolean success;
        private String msg;
    }

    @Data
    @AllArgsConstructor
    protected class AuthorizeParam {
        private String tenantId;
        private Long tenantSid;
        private String token;
    }

    protected abstract BaseResponse otherCheck(CountingUser countingUser, AuthorizeParam authorizeParam);

    protected abstract void buildCountingUser(List<CountingUser> countingUsers, AuthorizeParam authorizeParam);

    protected BaseResponse beforeUserAuthorized(List<CountingUser> countingUsers) {
        if (CollectionUtils.isEmpty(countingUsers)) {
            return BaseResponse.error(ResponseCode.PARAM_IS_EMPTY);
        }
        String serviceCode = countingUsers.get(0).getServiceCode();
        String tenantId = tenantDao.getTenantIdByServiceCode(RequestUtil.getHeaderSid(), serviceCode);
        if (StringUtils.isEmpty(tenantId)) {
            return BaseResponse.error(ResponseCode.TENANT_ITMS_NULL);
        }
        List<String> tenantIds = new ArrayList<>();
        tenantIds.add(tenantId);
        String token = StringUtils.isEmpty(RequestUtil.getHeaderToken()) ?
                iamService.grantAccessToken(verifyUserId, verifytenantId) : RequestUtil.getHeaderToken();
        List<TenantVO> tenants = iamService.getTenantSimpleInfo(token, tenantIds);
        if (CollectionUtils.isEmpty(tenants)) {
            return BaseResponse.error(ResponseCode.TENANT_IAM_NULL);
        }
        Long tenantSid = tenants.get(0).getSid();
        AuthorizeParam authorizeParam = new AuthorizeParam(tenantId, tenantSid, token);
        BaseResponse response = otherCheck(countingUsers.get(0), authorizeParam);
        if (!ResponseCode.SUCCESS.isSameCode(response.getCode())) {
            return response;
        }
        return BaseResponse.ok(authorizeParam);
    }

    protected void afterUserAuthorized(List<CountingUser> countingUsers) {
    }

    private void saveUserNotifyDefaultSetting(User user, Long sid, Long eid) {
        int contactCnt = tenantNotifyDao.selectUserNoticeContactByUser(user.getId());
        if (contactCnt <= 0) {
            UserNotifyContact userNotifyContact = new UserNotifyContact();
            userNotifyContact.setId(SnowFlake.getInstance().newId());
            userNotifyContact.setSid(sid);
            userNotifyContact.setEid(eid);
            userNotifyContact.setName(user.getName());
            userNotifyContact.setEmail(user.getEmail());
            userNotifyContact.setTelephone(user.getPhone());
            userNotifyContact.setWechat(user.getWechat());
            userNotifyContact.setUserId(user.getId());
            tenantNotifyDao.insertUserNotifyContact(userNotifyContact);
        }
        List<Long> modules = userNotifyConfig.getModules();
        for (Long moduleId : modules) {
            int moduleSettingCnt = tenantNotifyDao.selectModuleSettingByUserModule(user.getSid(), moduleId);
            if (moduleSettingCnt <= 0) {
                UserNotifyModuleSetting userNotifyModuleSetting = new UserNotifyModuleSetting(SnowFlake.getInstance().newId(), user.getSid(),
                        moduleId, true, LocalDateTime.now(), LocalDateTime.now());
                tenantNotifyDao.insertUserNotifyModuleSetting(userNotifyModuleSetting);
            }
            int settingResultCnt = tenantNotifyDao.selectUserNoticeSettingResultByUserModule(user.getId(), moduleId);
            if (settingResultCnt <= 0) {
                UserNotifySettingResult userNotifySettingResult = new UserNotifySettingResult(SnowFlake.getInstance().newId(), user.getSid(), user.getId(), moduleId,
                        userNotifyConfig.getWays(), userNotifyConfig.getLevels(), LocalDateTime.now(), LocalDateTime.now());
                tenantNotifyDao.insertUserNotifySettingResult(userNotifySettingResult);
            }
        }
    }

    protected BaseResponse doUserAuthorizedCore(List<CountingUser> countingUsers, AuthorizeParam authorizeParam) {
        String tenantId = authorizeParam.getTenantId();
        Long tenantSid = authorizeParam.getTenantSid();
        String token = authorizeParam.getToken();
        List<AuthorizeRes> authorizeRes = new ArrayList<>();
        boolean existFail = false;
        for (CountingUser countingUser : countingUsers) {
            String productCode = countingUser.getProductCode();
            String userId = countingUser.getUserId();
            if (StringUtils.isEmpty(userId)) {
                String msg = String.format("countingUser userId is empty,tenantId:%s", userId);
                log.warn(msg);
                authorizeRes.add(new AuthorizeRes("", "", false, msg));
                countingUser.setAuthorized(false);
                existFail = true;
                continue;
            }
            try {
                //创建服务云用户相关数据
                ResponseBase registerRes = userV2FeignClient.doAioUserRegister(token, countingUser);
                if (!ResponseCode.SUCCESS.getCode().equals(registerRes.getCode())) {
                    String msg = String.format("doAioUserRegister fail, userId:%s,tenantId:%s,msg:%s", userId, tenantId, registerRes.getErrMsg());
                    log.warn(msg);
                    authorizeRes.add(new AuthorizeRes(userId, "", false, msg));
                    countingUser.setAuthorized(false);
                    existFail = true;
                    continue;
                }
                String esUserId = JSON.parseObject(JSON.toJSONString(registerRes.getData()), String.class);
                User user = userDao.getUserById(userId);
                //创建企业运维云用户相关信息
                if (ObjectUtils.isEmpty(user)) {
                    UsersIntoTenantParam usersIntoTenantParam = UsersIntoTenantParam.builder()
                            .token(token)
                            .aioPlatformEnum(AioPlatformEnum.AIO_MIS_SSM)
                            .sid(RequestUtil.getHeaderSid())
                            .tenantSid(tenantSid)
                            .tenantId(tenantId)
                            .userId(userId)
                            .inTenant(true)
                            .productCode(productCode)
                            .build();
                    user = userBaseService.addExistUserIntoTenant(usersIntoTenantParam);
                    if (ObjectUtils.isEmpty(user)) {
                        String msg = String.format("addExistUserIntoTenant fail, userId:%s,tenantId:%s", userId, tenantId);
                        log.warn(msg);
                        authorizeRes.add(new AuthorizeRes(userId, esUserId, false, msg));
                        countingUser.setAuthorized(false);
                        existFail = true;
                        continue;
                    }
                } else {
                    userBaseService.saveUserTenantMap(user.getSid(), tenantSid, RequestUtil.getHeaderSid(),
                            true, productCode);
                    if (LongUtil.isEmpty(user.getDefaultEid()) || LongUtil.isEmpty(user.getDefaultEidSid())) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("defaultEid", tenantSid);
                        map.put("defaultEidSid", RequestUtil.getHeaderSid());
                        map.put("name", user.getName());
                        map.put("sid", user.getSid());
                        userDao.updateUserDefaultSet(map);
                    }
                }
                //保存用户联系信息
                long sidOrDefault = RequestUtil.getHeaderSidOrDefault(defaultSid);
                User finalUser = user;
                log.info("[saveUserNotifyDefaultSetting] start user:{},finalUser:{}", user, finalUser);
                CompletableFuture.runAsync(() -> saveUserNotifyDefaultSetting(finalUser, sidOrDefault, tenantSid))
                        .exceptionally(ex -> {
                            log.error("saveUserNotifyDefaultSetting error", ex);
                            return null;
                        });
                //授权应用
                CountReq countReq = new CountReq();
                countReq.setUserId(userId);
                countReq.setTenantId(tenantId);
                countReq.setCountingId(RequestUtil.getHeaderFuncAppId());
                CacRes cacRes = cacService.addUserCount(token, "", countReq);
                if (!cacRes.isSuccess()) {
                    String msg = String.format("addUserCount fail, userId:%s,tenantId:%s,countingId:%s", userId, tenantId,
                            RequestUtil.getHeaderFuncAppId());
                    log.warn(msg);
                    authorizeRes.add(new AuthorizeRes(userId, esUserId, false, msg));
                    countingUser.setAuthorized(false);
                    existFail = true;
                    continue;
                }
                authorizeRes.add(new AuthorizeRes(userId, esUserId, true, ""));
                Thread.sleep(200);
            } catch (Exception e) {
                String msg = String.format("doIamUserAuthorized fail, userId:%s,tenantId:%s,msg:%s", userId, tenantId,
                        e.getMessage());
                log.error(msg, e);
                countingUser.setAuthorized(false);
                existFail = true;
                authorizeRes.add(new AuthorizeRes(userId, "", false, msg));
            }
        }
        if (existFail) {
            return BaseResponse.error(ResponseCode.INTERNAL_ERROR, authorizeRes);
        } else {
            return BaseResponse.ok(authorizeRes);
        }
    }

    protected BaseResponse doUserAuthorizedBase(List<CountingUser> countingUsers) {
        BaseResponse response = beforeUserAuthorized(countingUsers);
        if (!ResponseCode.SUCCESS.isSameCode(response.getCode())) {
            return response;
        }
        AuthorizeParam authorizeParam = (AuthorizeParam) response.getData();
        buildCountingUser(countingUsers, authorizeParam);
        BaseResponse baseResponse = doUserAuthorizedCore(countingUsers, authorizeParam);
        afterUserAuthorized(countingUsers);
        return baseResponse;
    }
}
