package com.digiwin.escloud.issueservice.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * <AUTHOR>
 * @Date 2020/3/30-11:04
 */
@Slf4j
@Component
public class MessageUtils {

    @Autowired
    private MessageSource messageSource;

    // 简体中文 202412 代码稽核 public改成 protected
    protected static final String[] ZH_CN = {"zh_cn", "zh-cn", "zh_CN", "zh-CN"};
    public static final String ZH_CN_STANDARD = "zh_CN";
    // 繁体中文 202412 代码稽核 public改成 protected
    protected static final String[] ZH_TW = {"zh_tw", "zh-tw", "zh_TW", "zh-TW"};
    public static final String ZH_TW_STANDARD = "zh_TW";
    // 越南 202412 代码稽核 public改成 protected
    protected static final String[] VI_VN = {"vi_vn", "vi-vn", "vi_VN", "vi-VN"};
    public static final String VI_VN_STANDARD = "vi_VN";
    // 泰国 202412 代码稽核 public改成 protected
    protected static final String[] TH_TH = {"th_th", "th-th", "th_TH", "th-TH"};
    public static final String TH_TH_STANDARD = "th_TH";
    // 马来西亚 202412 代码稽核 public改成 protected
    protected static final String[] MS_MY = {"ms_my", "ms-my", "ms_MY", "ms-MY"};
    public static final String MS_MY_STANDARD = "ms_MY";
    // 英文 202412 代码稽核 public改成 protected
    protected static final String[] EN_US = {"en_us", "en-us", "en_US", "en-US"};
    public static final String EN_US_STANDARD = "en_US";

    public String getLanguage(String language){
        if (ArrayUtils.contains(ZH_CN,language)) {
            return ZH_CN_STANDARD;
        } else if (ArrayUtils.contains(ZH_TW,language)) {
            return ZH_TW_STANDARD;
        } else if (ArrayUtils.contains(VI_VN,language)) {
            return VI_VN_STANDARD;
        } else if (ArrayUtils.contains(TH_TH,language)) {
            return TH_TH_STANDARD;
        } else if (ArrayUtils.contains(EN_US,language)) {
            return EN_US_STANDARD;
        } else if (ArrayUtils.contains(MS_MY,language)) {
            return MS_MY_STANDARD;
        } else {
            return ZH_TW_STANDARD;
        }
    }

    public String get(String msgKey,String language) {
        Locale locale;
        if (ArrayUtils.contains(ZH_CN,language)) {
            locale = Locale.SIMPLIFIED_CHINESE;
        } else if (ArrayUtils.contains(ZH_TW,language)) {
            locale = Locale.TRADITIONAL_CHINESE;
        } else if (ArrayUtils.contains(VI_VN,language)) {
            locale = new Locale("vi","VN");
        } else if (ArrayUtils.contains(TH_TH,language)) {
            locale = new Locale("th","TH");
        } else if (ArrayUtils.contains(MS_MY,language)) {
            locale = new Locale("ms","MY");
        } else if (ArrayUtils.contains(EN_US,language)) {
            locale = Locale.US;
        } else {
            locale = Locale.TRADITIONAL_CHINESE;
        }
        try {
            return messageSource.getMessage(msgKey, null, locale);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("获取国际化信息失败-----");
            return msgKey;
        }
    }
}
