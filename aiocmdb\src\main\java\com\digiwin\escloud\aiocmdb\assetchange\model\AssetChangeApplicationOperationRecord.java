package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 操作记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@ApiModel(value = "AssetChangeApplicationOperationRecord对象", description = "操作记录")
public class AssetChangeApplicationOperationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("申请单ID")
    private Long applicationId;

    @ApiModelProperty("操作人用户ID")
    private String operatorUserId;

    @ApiModelProperty("操作人名称")
    private String operatorName;

    @ApiModelProperty("操作时间")
    private LocalDateTime operationTime;

    @ApiModelProperty("下一处理人用户ID")
    private String nextUserId;

    @ApiModelProperty("下一处理人名称")
    private String nextUserName;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("操作意见")
    private String operationOpinion;

    @ApiModelProperty("操作意见说明")
    private String operationExplanation;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public String getOperatorUserId() {
        return operatorUserId;
    }

    public void setOperatorUserId(String operatorUserId) {
        this.operatorUserId = operatorUserId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(LocalDateTime operationTime) {
        this.operationTime = operationTime;
    }

    public String getNextUserId() {
        return nextUserId;
    }

    public void setNextUserId(String nextUserId) {
        this.nextUserId = nextUserId;
    }

    public String getNextUserName() {
        return nextUserName;
    }

    public void setNextUserName(String nextUserName) {
        this.nextUserName = nextUserName;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationOpinion() {
        return operationOpinion;
    }

    public void setOperationOpinion(String operationOpinion) {
        this.operationOpinion = operationOpinion;
    }

    public String getOperationExplanation() {
        return operationExplanation;
    }

    public void setOperationExplanation(String operationExplanation) {
        this.operationExplanation = operationExplanation;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AssetChangeApplicationOperationRecord{" +
            "id = " + id +
            ", applicationId = " + applicationId +
            ", operatorUserId = " + operatorUserId +
            ", operatorName = " + operatorName +
            ", operationTime = " + operationTime +
            ", nextUserId = " + nextUserId +
            ", nextUserName = " + nextUserName +
            ", operationType = " + operationType +
            ", operationOpinion = " + operationOpinion +
            ", operationExplanation = " + operationExplanation +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
        "}";
    }
}
