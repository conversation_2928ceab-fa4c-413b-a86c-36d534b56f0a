package com.digiwin.escloud.issueservice.services.Impl;

import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.common.util.CollectionUtil;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.issueservice.utils.MessageUtils;
import com.digiwin.escloud.issueservice.IssueServiceApplication;
import com.digiwin.escloud.issueservice.cache.IssueChatfileConfigCache;
import com.digiwin.escloud.issueservice.cache.IssueSwitchCache;
import com.digiwin.escloud.issueservice.constant.RedisKeyConstants;
import com.digiwin.escloud.issueservice.dao.ICustomerDao;
import com.digiwin.escloud.issueservice.dao.IIssueDao;
import com.digiwin.escloud.issueservice.dao.IIssueDetailDao;
import com.digiwin.escloud.issueservice.dao.IUserDao;
import com.digiwin.escloud.issueservice.dao.Impl.IssueSyncDao;
import com.digiwin.escloud.issueservice.model.*;
import com.digiwin.escloud.issueservice.services.*;
import com.digiwin.escloud.issueservice.t.integration.workday.model.IssueKey;
import com.digiwin.escloud.issueservice.t.integration.workday.mq.issue.IssueWorkDayProducer;
import com.digiwin.escloud.issueservice.t.issuedetail.dao.IssueProcessMapper;
import com.digiwin.escloud.issueservice.t.model.cases.Module;
import com.digiwin.escloud.issueservice.t.model.common.BaseResponse;
import com.digiwin.escloud.issueservice.t.model.common.PageInfo;
import com.digiwin.escloud.issueservice.t.model.common.ResponseStatus;
import com.digiwin.escloud.issueservice.t.utils.DateUtils;
import com.digiwin.escloud.issueservice.utils.CommonUtils;
import com.digiwin.escloud.issueservice.utils.MailUtils;
import com.digiwin.escloud.issueservice.v3.dao.IIssueDaoV3;
import com.digiwin.escloud.issueservice.v3.service.impl.IssueDetailServiceV3;
import com.digiwin.escloud.messagelibrary.Model.MessageDestination;
import com.digiwin.escloud.messagelibrary.ProducerBaseInterface;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.util.StringUtil;
import com.google.gson.Gson;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.gridfs.GridFSDBFile;
import com.mongodb.gridfs.GridFSFile;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.apache.tomcat.util.http.fileupload.IOUtils.copy;

//import org.springframework.util.StringUtils;

//import com.github.pagehelper.util.StringUtil;

/**
 * Created by Administrator on 2018-01-03.
 */
@Service
public class IssueService extends ProducerBaseInterface<Boolean, Issue> implements IIssueService {

    @Autowired
    private CommonMailService commonMailService;
    @Autowired
    private MailUtils mailUtils;
    @Autowired
    private IssueProcessMapper issueProcessMapper;
    @Autowired
    private ICustomerDao customerDao;

    @Autowired
    private IssueSyncDao issueSyncDao;
    @Autowired
    private IssueWorkDayProducer workDayMq;
    @Autowired
    private IssueDetailService issueDetailService;
    @Autowired
    private ISendOpenFireMessageService openFireMessageService;
    @Autowired
    private MessageUtils messageUtils;
    private static final String ToCCMailStr = "收到来自鼎捷服务云新案件，请您协助处理，案件单详细信息如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>产品线</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合约等级</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件单号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客户名称</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>接单时间</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>联系人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>电话</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>抄送</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>问题描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToCCMailStr_TW = "收到來自<b>鼎新服務雲</b>新案件，請您協助處理，案件單詳細資訊如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>産品線</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代號</b></td>\n" +
            "\t\t\t\t\t<td width=200 >%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合約等級</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客戶名稱</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>填單時間</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>連絡人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>電話</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>抄送</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>問題描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\" border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToSubmiterMailStr = "您通过鼎捷云管家提交的问题已提交成功，客服专员会第一时间为您处理！案件单详细信息如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>产品线</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合约等级</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件单号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客户名称</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>接单时间</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>联系人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>电话</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>问题描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToSubmiterMailStr_TW = "您通過鼎新服務雲提交的問題已提交成功，客服將第一時間為您處理！案件單詳細資訊如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>産品線</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代號</b></td>\n" +
            "\t\t\t\t\t<td width=200 >%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合約等級</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客戶名稱</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>填單時間</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>連絡人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>電話</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>問題描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\" border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToSubmiterBackMailStr = "您通过鼎捷云管家提交的问题已被退回！案件单详细信息如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>产品线</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合约等级</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件单号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客户名称</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>接单时间</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>联系人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>电话</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>问题描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToSubmiterBackMailStr_TW = "您通過鼎新服務雲提交的問題已被退回！案件單詳細資訊如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>産品線</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代號</b></td>\n" +
            "\t\t\t\t\t<td width=200 >%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合約等級</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客戶名稱</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>填單時間</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>連絡人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>電話</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>問題描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\" border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToSubmiterMailStrByConfirm = "您有新的案件待审核，请您及时登录鼎新服务云进行审核！案件单详细资讯如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>产品线</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合约等级</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件单号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客户名称</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>接单时间</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>联系人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>电话</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>问题描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToSubmiterMailStrByConfirm_TW = "您有新的案件待審核，請您及時登入鼎新服務雲進行審核！案件單詳細資訊如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>産品線</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代號</b></td>\n" +
            "\t\t\t\t\t<td width=200 >%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合約等級</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客戶名稱</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>填單時間</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>連絡人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>電話</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>問題描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\" border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToCCMailAdditionalExplanation = "这是由鼎捷服务云管家中[Call-Center进度查询]中客户所反映的案件，详细内容如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>产品线</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>客服代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>客户名称</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>案件代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>结案</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"3\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>反应者</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>登录人员</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>登录日期</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>E-mail</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>电话</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>分机</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>问题描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; padding-left:10px;\"><b>处理记录</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>反应内容</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToCCMailAdditionalExplanation_TW = "這是由鼎新服務雲管家中[Call-Center進度查詢]中客戶所反應的案件，詳細內容如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>産品線</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>客服代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>客戶名稱</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>案件代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>結案</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"3\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>反應者</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>登錄人員</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>登錄日期</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>E-mail</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>電話</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>分機</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>問題描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; padding-left:10px;\"><b>處理記錄</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>反應內容</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToUserMailAdditionalExplanation = "这是由鼎捷服务云管家中[Call-Center进度查询]中您所反映的案件，客服将第一时间为您处理！详细内容如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>产品线</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>客服代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>客户名称</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>案件代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>结案</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"3\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>反应者</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>登录人员</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>登录日期</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>E-mail</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>电话</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>分机</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>问题描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; padding-left:10px;\"><b>处理记录</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>反应内容</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToUserMailAdditionalExplanation_TW = "這是由鼎新服務雲管家中[Call-Center進度查詢]中您所反應的案件，客服將第一時間為您處理！詳細內容如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>産品線</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>客服代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>客戶名稱</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>案件代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>結案</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"3\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>反應者</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>登錄人員</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>登錄日期</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=85 height=40 style=\"padding-left:10px\"><b>E-mail</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>電話</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=85><b>分機</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>問題描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; padding-left:10px;\"><b>處理記錄</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>反應內容</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToCCFollowUpMailStr = "收到来自鼎捷服务云的催单，请您尽快处理，案件单详细信息如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>产品线</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合约等级</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件单号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客户名称</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>接单时间</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>联系人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>电话</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"vertical-align: text-top; padding-left:10px;\"><b>问题描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>催单备注</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToCCFollowUpMailStr_TW = "收到來自<b>鼎新服務雲</b>的催單，請您盡快處理，案件單詳細資訊如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>産品線</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代號</b></td>\n" +
            "\t\t\t\t\t<td width=200 >%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合約等級</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客戶名稱</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>填單時間</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>連絡人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>電話</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"vertical-align: text-top; padding-left:10px;\"><b>問題描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\" border-right:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>催單備註</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\" border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToSubmiterFollowUpMailStr = "您通过鼎捷云管家提交的催单已提交成功，客服专员会第一时间为您处理！案件单详细信息如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>产品线</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合约等级</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件单号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客户名称</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>接单时间</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>联系人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>电话</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"vertical-align: text-top; padding-left:10px;\"><b>问题描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>催单备注</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToSubmiterFollowUpMailStr_TW = "您通過鼎新服務雲提交的催單已提交成功，客服將第一時間為您處理！案件單詳細資訊如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>産品線</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代號</b></td>\n" +
            "\t\t\t\t\t<td width=200 >%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合約等級</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客戶名稱</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>填單時間</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>連絡人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>電話</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"vertical-align: text-top; padding-left:10px;\"><b>問題描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\" border-right:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>催單備註</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\" border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static Log log = LogFactory.getLog(IssueService.class);
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    GridFsTemplate gridFsTemplate;
    @Autowired
    private IIssueDao issueDao;
    @Autowired
    private IssueChatfileConfigCache issueChatfileConfigCache;
    @Autowired
    private IIssueDaoV3 issueDaoV3;
    @Autowired
    private IIssueDetailDao issueDetailDao;
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IIssueNoticeService issueNoticeService;
    @Value("${digiwin.aio.dbname:aio-db}")
    private String aioDBName;
    @Value("${digiwin.issueservice.defaultSid:241199971893824}")
    Long defaultSid;
    @Autowired
    private IPromotionService promotionService;
    @Value("${attachmentfilePathPrefix}")
    private String attachmentfilePathPrefix;
    @Value("${issueAttachAddress}")
    private String issueAttachAddress;
    @Value("${digiwin.issue.defaultlanguage}")
    private String defaultLanguage;
    @Value("${digiwin.issue.connectarea}")
    private String connectArea;
    @Value("${digiwin.issue.servertimezone:08:00}")
    private String servertimezone;
    @Value("${api.bigdata.url}")
    private String bigDataUrl;
    @Autowired
    IUserDao userDao;
    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private ICloudIssueService cloudIssueService;
    @Autowired
    private IssueDetailServiceV3 issueDetailServiceV3;

    @Override
    protected Map<MessageDestination, String[]> generateMessage(Issue issue, Boolean success) {
        Map<MessageDestination, String[]> messgae = new HashMap<MessageDestination, String[]>();
        if (success) {
            String[] mx = {PrepareIssueMail(issue)};
            messgae.put(MessageDestination.MAILMESSAGEDESTINATION, mx);
        }
        return messgae;
    }

    @Override
    protected Boolean realBusiness(Issue issue) {
        return true;
    }

    @Override
    protected void exceptionHandler(Exception e) {

    }

    private boolean CustomerIssueConfirmSetting(Issue issue) {

        return customerService.GetCustomerIssueConfirmSetting(issue.getServiceCode(), issue.getProductCode());
    }

    private String GetCustomerIssueConfirmer(Issue issue) {

        return customerService.GetCustomerIssueConfirmer(issue.getServiceCode(), issue.getProductCode());
    }

    @Override
    public boolean checkConfirm(Issue issue) {
        //易聊不需走这套逻辑
        boolean isSIMIssue = issue.getSubmitWay().startsWith("SIM_") || issue.getSubmitWay().startsWith("EASY_TALK");

        if (isSIMIssue) {
            return false;
        }
        if (!isSIMIssue) {
            boolean isConfirm = CustomerIssueConfirmSetting(issue);
            if (isConfirm) {
                String confirmIds = GetCustomerIssueConfirmer(issue);
                if (!StringUtil.isEmpty(confirmIds)) {
                    return false;
                }
            } else {
                return false;
            }

        }

        return true;
    }

    /**
     * 案件提交
     *
     * @param issue
     * @return
     */
    @Override
    public int SubmitIssue(Issue issue) {
        int res = 0;
        //long issueId = 0;
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(new Date());
        //1.生成案件单
        try {
            issue.setIssueStatus(IssueStatus.Incomplete.toString());
            if (issue.isTrial()) {
                issue.setSyncStatus(IssueStatus.Trial.toString());
            } else {
                if (CheckContractIsTrial(issue.getServiceCode(), issue.getProductCode())) {
                    issue.setSyncStatus(IssueStatus.Trial.toString());
                } else {
                    //再案件信息完善之前，同步状态为"待完善"
                    issue.setSyncStatus(IssueStatus.Incomplete.toString());
                }
            }

            //如果是SIM，允许前端设置提交时间
            String submitWay = issue.getSubmitWay();
            String submitTime = issue.getSubmitTime();
            if (submitWay == null || submitTime == null || submitTime.trim().isEmpty() || !submitWay.startsWith("SIM_") || !submitWay.startsWith("EASY_TALK")) {
                issue.setSubmitTime(date);
            }
            if (StringUtils.isEmpty(issue.getServiceRegion())) {
                issue.setServiceRegion(connectArea);
            }
            if ("CN".equals(issue.getServiceRegion())) {
                if ("Submit Issue".equals(issue.getIssueDescription())) {
                    issue.setIssueDescription("提交问题");
                }
            }
            issueDao.InsertIssue(issue);

            //新增所選的預警編號.
            issueDao.batchInsertWarningId(issue.getIssueId(), issue.getCrmId(), issue.getWarningIdCollection());
            //新增跟這個 ISSUE 有關的项目任务
            issueDao.batchInsertSourceMap(issue.getIssueId(), issue.getCrmId(), issue.getIssueSourceMapList());
        } catch (Exception ex) {
            res = 1;
            log.error(ex);
            ex.printStackTrace();
            return res;
        }
//        //2.发送邮件给客户
//        SendMailToSubmiter(issue);
//        //3.完善案件资料，并通知客服
//        CompleteIssueInfo(issueId, issue);
//        //4.保存附件
//        SaveAttachments(issueId, issue);

        return res;
    }

//    private void UpdateIssueCrmId(long issueId){
//        String crmId = "ES" + String.format("%010", issueId);
//        issueDao.UpdateIssueCrmId(issueId, crmId);
//    }


    @Async
    @Override
    public void SubmitIssueAsync(Issue issue, String issueStatus, boolean isContainFileName) {
        //2.发送邮件给客户
//        try {
//            SendMailToSubmiter(issue);
//        }catch(Exception ex){
//
//        }
        //20191220 huly:案件提交后，判断是否走审核流程。
        boolean isConfirm = false;
        //易聊不需走这套逻辑
        boolean isSIMIssue = issue.getSubmitWay().startsWith("SIM_") || issue.getSubmitWay().startsWith("EASY_TALK");

        if (!isSIMIssue) {
            isConfirm = CustomerIssueConfirmSetting(issue);
        }
        Long issueId = issue.getIssueId();

        //isConfirm=true，走审核
        if (isConfirm) {
            //3.完善案件资料，并通知客服
            CompleteIssueInfo(issueId, issue, issueStatus, isConfirm);
            //4.保存附件
            SaveAttachments(issueId, issue, isContainFileName);

            //增加子单头
            saveCasedetail(issue, false);
            //5.保存處理進度
            SaveIssueProgress(issue, false);

            //6.修改案件状态为可同步状态(或已提交状态)
            UpdateSyncIssueStatus(issue, isConfirm);

            //huly: 修复漏洞/bug 注释代码 不会走这段
            /*//易聊提交的案件，不发Mail给客服與客戶
            if (isSIMIssue) {
                return;
            }*/
            //7.发送邮件给客户
            try {
                //20200703電話立案所提交的案件，不發郵件給客戶
                if (!issue.getSubmitWay().equals("CTI_Phone")) {
                    SendMailToSubmiterByConfirm(issue, isConfirm);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            //7.发送邮件和通知给审核人
            try {
                SendNoticeToConfirmer(issue);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } else {

            //3.完善案件资料，并通知客服
            CompleteIssueInfo(issueId, issue, issueStatus, isConfirm);

            //4.保存附件
            SaveAttachments(issueId, issue, isContainFileName);

            //增加子单头
            saveCasedetail(issue, isSIMIssue);
            //5.保存處理進度
            SaveIssueProgress(issue, false);

            //6.修改案件状态为可同步状态(或已提交状态)
            UpdateSyncIssueStatus(issue, isConfirm);

            //更新总工时
            issueDetailDao.updateDetailIssueTotalWorkHours(issueId);

            //出货来源的 需要上传序号表 issue_authorized_file
            if (IssueSubmitMode.SHIPMENTAUTHORIZE.toString().equals(issue.getIssueSubmitMode())) {
                insertIssueSelfUpdateSystem(issue);
            }
            //易聊提交的案件，不发Mail给客服與客戶
            if (isSIMIssue) {
                return;
            }
            //20200429 huly:T的案件，发给客户和客服的时候公用一封邮件，其他产品线保持不变
            if (!StringUtil.isEmpty(issue.getProductCode())) {
                if ("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())) {
                    //7.T的案件同时发送邮件给客户、客服
                    try {
                        SendMailToCustomerService_T(issue);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                } else {
                    //7.发送邮件给客户
                    try {
                        //20200703 電話立案所提交的案件，不發郵件給客戶
                        if (!issue.getSubmitWay().equals("CTI_Phone")) {
                            SendMailToSubmiterByConfirm(issue, isConfirm);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }

                    //8.发送邮件和通知给客服
                    try {
                        SendNoticeToCC(issue);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                    //代理商案件量达到90%的时候，需要发一封邮件给对应的业务人员，以及代理商设定里面的邮件抄送人员
                    try {
                        checkAgentNotice(issue);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }

        }
        saveCache(issue);
    }

    private void checkAgentNotice(Issue issue) {
        try {
            int count = checkAgent(issue.getServiceCode(), issue.getProductCode());
            if (count > 0)//大于0 表示 有满足代理商条件
            {
                AgentIssueSum sum = issueDao.selectAgentIssueSumarry(issue.getServiceCode(), issue.getProductCode());
                if (sum != null && sum.getAgentLimitIssueCount() != 0) {
//                    int percent = (new BigDecimal((float) sum.getUsedCount() * 100 / sum.getAgentLimitIssueCount()).setScale(0, BigDecimal.ROUND_HALF_UP).intValue());
                    //修复bug 修复使用BigDecimal.valueOf处理结果
                    int percent = BigDecimal.valueOf(sum.getUsedCount())
                            .multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(sum.getAgentLimitIssueCount()), 0, BigDecimal.ROUND_HALF_UP)
                            .setScale(0, BigDecimal.ROUND_HALF_UP)
                            .intValue();

                    if (percent >= 90) {//代理商案件量达到90%的时候，需要发一封邮件给对应的业务人员，以及代理商设定里面的邮件抄送人员
                        //查看该合约到期日是否发送过
                        CustomerServiceInfo customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
                        int emailCount = issueDao.selectAgentNotifyEmail(issue.getProductCode(), issue.getServiceCode(), customerServiceInfo.getContractExprityDate());
                        if (emailCount == 0) { //该合约内未发送过代理商通知邮件
                            SendAgentNoticeToCC(issue, sum, customerServiceInfo);
                            issueDao.saveAgentNotifyEmail(issue.getProductCode(), issue.getServiceCode(), customerServiceInfo.getContractExprityDate());
                        }

                    }
                }
            }
        } catch (Exception e) {
            log.error("checkAgentNotice fail,{}" + e.toString());
        }
    }


    private void insertIssueSelfUpdateSystem(Issue issue) {
        try {
            long id = insertIssueSelfUpdateSystemFile(issue);
            insertIssueSelfUpdateSystemLog(id, issue);
        } catch (Exception e) {
            log.error("insertIssueSelfUpdateSystem fail,{}" + e.toString());
        }

    }


    private void updateIssueProgressSyncStatus(Long issueId, String syncStatus) {
        try {
            issueDao.updateIssueProgressSyncStatus(issueId, syncStatus);
        } catch (Exception e) {
            log.error("updateIssueProgressSyncStatus fail,{}" + e.toString());
        }

    }

    private long insertIssueSelfUpdateSystemFile(Issue issue) {
        IssueSelfUpdateSystemFile issueSelfUpdateSystemFile = new IssueSelfUpdateSystemFile();
        issueSelfUpdateSystemFile.setIssueId(issue.getIssueId());
        issueSelfUpdateSystemFile.setCrmId(issue.getCrmId());
        issueSelfUpdateSystemFile.setMaderWorkNo(issue.getMaderWorkNo());
        issueSelfUpdateSystemFile.setBusinessWorkNo(issue.getBusinessWorkNo());
        issueSelfUpdateSystemFile.setSecretaryWorkNo(issue.getSecretaryWorkNo());
        issueSelfUpdateSystemFile.setShipmentSid(issue.getShipmentSid());

        switch (issue.getUpdateType()) {
            case "AUTHINSTALL": {
                issueSelfUpdateSystemFile.setUpdateType(UpdateType.AUTHINSTALL.toString());//授权安装
                if (issue.getShipmentSid() != 0) {
                    ShipmentinfoAuthorizedFile shipmentinfoAuthorizedFile = issueDetailDao.getShipmentinfoAuthorizedFile(issue.getShipmentSid());
                    if (shipmentinfoAuthorizedFile != null) {
                        issueSelfUpdateSystemFile.setFileName(shipmentinfoAuthorizedFile.getName());
                        issueSelfUpdateSystemFile.setFileUrl(shipmentinfoAuthorizedFile.getUrl());
                    }
                }
                issueSelfUpdateSystemFile.setAuthStatus(AuthStatus.doNotNeedAuth.getValue());
                break;
            }
            case "SFT_AUTHINSTALL": {
                issueSelfUpdateSystemFile.setUpdateType(UpdateType.SFT_AUTHINSTALL.toString());//SFT加购授权
                issueSelfUpdateSystemFile.setAuthStatus(AuthStatus.doNotNeedAuth.getValue());
                break;
            }
            case "SFT_RE_AUTHINSTALL": {
                issueSelfUpdateSystemFile.setUpdateType(UpdateType.SFT_RE_AUTHINSTALL.toString());//SFT序号重查
                issueSelfUpdateSystemFile.setAuthStatus(AuthStatus.waitingCheck.getValue());
                break;
            }
            default:
        }

        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(new Date());
        issueSelfUpdateSystemFile.setUploadTime(date);
        StaffUserInfo staffUserInfo = userDao.SelectStaffByWorkNo(issue.getMaderWorkNo());
        if (staffUserInfo != null) {
            issueSelfUpdateSystemFile.setUploadUser(staffUserInfo.getUserId());
        }
        issueSelfUpdateSystemFile.setUploadTime(issue.getSubmitTime());
        return issueDetailDao.insertIssueSelfUpdateSystemFile(issueSelfUpdateSystemFile);
    }

    private long insertIssueSelfUpdateSystemLog(long id, Issue issue) {
        IssueSelfUpdateSystemLog issueSelfUpdateSystemlog = new IssueSelfUpdateSystemLog();
        issueSelfUpdateSystemlog.setIssueId(issue.getIssueId());
        issueSelfUpdateSystemlog.setCrmId(issue.getCrmId());
        issueSelfUpdateSystemlog.setIsusfId(id);

        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(new Date());
        issueSelfUpdateSystemlog.setOperationTime(date);
        StaffUserInfo staffUserInfo = userDao.SelectStaffByWorkNo(issue.getMaderWorkNo());
        if (staffUserInfo != null) {
            issueSelfUpdateSystemlog.setOperationUserId(staffUserInfo.getUserId());
            issueSelfUpdateSystemlog.setOperationUser(staffUserInfo.getFullName());
        }
        issueSelfUpdateSystemlog.setOperationStatus(OperationResult.SUCCESS.toString());

        switch (issue.getUpdateType()) {
            case "AUTHINSTALL": {
                issueSelfUpdateSystemlog.setTypeId(TypeName.UPLOAD_AUTH_FILE.getStatus());//授权打包作业
                issueSelfUpdateSystemlog.setOperationResultDes(TypeName.UPLOAD_AUTH_FILE.getDesc());
                break;
            }
            case "SFT_AUTHINSTALL": {
                issueSelfUpdateSystemlog.setTypeId(TypeName.SFT_AUTHINSTALL.getStatus());//SFT加购授权
                issueSelfUpdateSystemlog.setOperationResultDes(TypeName.SFT_AUTHINSTALL.getDesc());
                break;
            }
            case "SFT_RE_AUTHINSTALL": {
                issueSelfUpdateSystemlog.setTypeId(TypeName.SFT_RE_AUTHINSTALL.getStatus());//SFT序号重查
                issueSelfUpdateSystemlog.setOperationResultDes(TypeName.SFT_RE_AUTHINSTALL.getDesc());
                break;
            }
            default:
        }
        return issueDetailDao.insertIssueSelfUpdateSystemLog(issueSelfUpdateSystemlog);
    }

    private void saveCache(Issue issue) {
        //1 用户级别：客户、产品、联系方式 必传userId，productCode不用传
//        saveHistorySelect(issue.getUserId(), issue.getServiceCode(), IssueSelectType.CUSTOMER.toString(), "");
//        saveHistorySelect(issue.getUserId(), issue.getProductCode(), IssueSelectType.PRODUCT.toString(), "");
        updateLastUserContact(issue.getUserId(), IssueSelectType.CONTACT.toString(), issue.getUserContact());
        //2.产品级别：处理人、模组/应用、问题分类 必传productCode
//        saveHistorySelect(issue.getUserId(), issue.getServiceStaff(), IssueSelectType.HANDLER.toString(), issue.getProductCode());
//        saveHistorySelect(issue.getUserId(), issue.getIssueCasedetail().getErpSystemCode(), IssueSelectType.ERPSYSTEMCODE.toString(), issue.getProductCode());
//        saveHistorySelect(issue.getUserId(), issue.getIssueCasedetail().getIssueClassification(), IssueSelectType.ISSUECLASSFICATION.toString(), issue.getProductCode());
    }

    public BaseResponse updateLastUserContact(String userId, String type, UserContact userContact) {
        log.info("userId:" + userId + " type:" + type);
        if (userContact == null) {
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }
        boolean result = false;
        String code = "";
        //联系方式id为空的时候，需要更新到联系方式表，有就不用更新到联系方式表了
        if (userContact.getId() == 0) {
            code = String.valueOf(userDao.GetUserContractId(userContact));
        } else {
            code = String.valueOf(userContact.getId());
        }
        UserContact oldUserContact = issueDaoV3.getLastUserContact(userId, type);
        if (oldUserContact == null) {
            result = issueDaoV3.insertLastUserContact(userId, code, type) > 0;
        } else {
            result = issueDaoV3.updateLastUserContact(userId, code, type) > 0;
        }
        if (result) {
            return BaseResponse.ok();
        } else {
            return BaseResponse.error(ResponseStatus.UPDATE_FAILD);
        }
    }

    @Async
    @Override
    public void SubmitIssueAsyncNew(Issue issue, String issueStatus, boolean isContainFileName) {
        //易聊不需走这套逻辑
        boolean isSIMIssue = issue.getSubmitWay().startsWith("SIM_");

        Long issueId = issue.getIssueId();

        //3.完善案件资料，并通知客服
        CompleteIssueInfoNew(issueId, issue, issueStatus, false);

        //4.保存附件
        SaveAttachments(issueId, issue, isContainFileName);

        //增加子单头 子单头的同步状态码设置成Z
        saveCasedetailNew(issue, isSIMIssue);
        //5.保存處理進度
        SaveIssueProgress(issue, false);

        //6.修改案件状态为可同步状态(或已提交状态)
        UpdateSyncIssueStatus(issue, false);
        //20240409 pini:因应T案件需同步至CRM,故不设置不同步
        //20200522 huly:东南亚T的案件，案件立案时，设置同步状态为Z
//        UpdateSyncStatusByIssueId(issue.getIssueId(), SyncStatus.DontNeedSync.toString());
        //20200629 huly:东南亚T、大陆T的案件，案件立案时，单身设置同步状态为Z
        //UpdateProcessSyncStatusByIssueId(issue.getIssueId(), SyncStatus.DontNeedSync.toString());
        if ("CN".equals(issue.getServiceRegion())) {
            workDayMq.produceMsg(new IssueKey(issueId, 1, 0));//大陆T的案件同步到workday
        }

        //易聊提交的案件，不发Mail给客服與客戶
        if (issue.getSubmitWay().startsWith("SIM_")) {
            return;
        }
        //20200429 huly:T的案件，发给客户和客服的时候公用一封邮件，其他产品线保持不变
        if (!StringUtil.isEmpty(issue.getProductCode())) {
            if ("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())) {
                //7.T的案件同时发送邮件给客户、客服
                try {
                    SendMailToCustomerService_T(issue);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            } else {
                //7.发送邮件给客户
                try {
                    SendMailToSubmiterByConfirm(issue, false);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                //8.发送邮件和通知给客服
                try {
                    SendNoticeToCC(issue);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
    }

    @Async
    @Override
    public ResponseBase UseCouponAsync(String couponNo, String issueId, String userId) {
        ResponseBase responseBase = new ResponseBase();
        try {
            responseBase = promotionService.UpdateCouponUsed(couponNo, issueId, userId);
        } catch (Exception ex) {
            responseBase = new ResponseBase();
        }
        return responseBase;
    }

    @Override
    public String getCrmIdByIssueId(long issueId) {
        return issueDao.getCrmIdByIssueId(issueId);
    }

    private void UpdateSyncIssueStatus(Issue issue, boolean isConfirm) {
        String issueStatus;
        //将同步状态改成N(可同步)
        //更新案件的同步状态案件不走审核提交 N,走审核提交 Z

        if (issue.isTrial()) {
            issueStatus = SyncStatus.Trial.toString();
        } else if (isConfirm) {
            issueStatus = SyncStatus.DontNeedSync.toString();
        } else if ("163".equals(issue.getProductCode()) || "15".equals(issue.getProductCode())) {
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            if (customerServiceInfo != null) {
                if (customerServiceInfo.isTrial() && "3".equals(customerServiceInfo.getContractSource())) {
                    issueStatus = SyncStatus.DontNeedSync.toString();
                    issue.setTrial(true);
                } else {
                    issueStatus = SyncStatus.UnSync.toString();
                }
            } else {
                issueStatus = SyncStatus.DontNeedSync.toString();
            }
        } else {
            issueStatus = SyncStatus.UnSync.toString();
            if ("888".equals(issue.getProductCode()) || "147".equals(issue.getProductCode()) || "161".equals(issue.getProductCode()) || "157".equals(issue.getProductCode())) { // 147 产品线 不需要同步
                issueStatus = SyncStatus.DontNeedSync.toString();
            }
        }
        issue.setSyncStatus(issueStatus);
        issueDao.UpdateSyncStatusByIssueId(issue.getIssueId(), issueStatus);
    }

    private void UpdateSyncReleaseIssueStatus(Issue issue, boolean isConfirm) {
        String issueStatus;
        //關聯案件，所以其案件狀態改為T(編輯後未同步)
        //更新案件的同步状态案件不走审核提交 N,走审核提交 Z

        if (issue.isTrial()) {
            issueStatus = SyncStatus.Trial.toString();
        } else if (isConfirm) {
            issueStatus = SyncStatus.DontNeedSync.toString();
        } else if ("163".equals(issue.getProductCode()) || "15".equals(issue.getProductCode())) {
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            if (customerServiceInfo != null) {
                if (customerServiceInfo.isTrial() && "3".equals(customerServiceInfo.getContractSource())) {
                    issueStatus = SyncStatus.DontNeedSync.toString();
                    issue.setTrial(true);
                } else {
                    issueStatus = SyncStatus.EditUnSync.toString();
                }
            } else {
                issueStatus = SyncStatus.DontNeedSync.toString();
            }
        } else {
            issueStatus = SyncStatus.EditUnSync.toString();
            if ("888".equals(issue.getProductCode()) || "147".equals(issue.getProductCode()) || "161".equals(issue.getProductCode()) || "157".equals(issue.getProductCode())) { // 147 产品线 不需要同步
                issueStatus = SyncStatus.DontNeedSync.toString();
            }
        }
        issue.setSyncStatus(issueStatus);
        issueDao.UpdateSyncStatusByIssueId(issue.getIssueId(), issueStatus);
    }

    private boolean CheckContractIsTrial(String ServiceCode, String ProductCode) {
        //檢查該產品線是否為試用
        CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
        //获取客户的维护合约信息
        customerServiceInfo = customerService.GetCustomerServiceInfo(ServiceCode, ProductCode);
        if (customerServiceInfo != null) {
            if (customerServiceInfo.isTrial()) {
                return true;
            }
        }
        return false;
    }

    @Async
    @Override
    public void SubmitIssueAdditionalExplanationAsync(Issue issue) {
        Date date = new Date();
        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String dt = sdf.format(date);
        //保存資料
        if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
            ;
        } else if (connectArea.equals("TW")) {

            SaveIssueAdditionalExplanationDetail(issue);
        }

        //保存附件
        SaveAdditionalExplanationAttachments(issue.getIssueId(), 0L, issue, dt);

        //发送邮件和通知给客服
        try {
            SendAdditionalExplanation(issue, dt);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        //发送邮件给客戶
        if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
        } else if (connectArea.equals("TW")) {
            try {
                SendAdditionalExplanationToUser(issue, dt);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @Async
    @Override
    public void SubmitCallMeIssueAsync(Issue issue, Issue newIssue, String userId) {
        LocalDateTime now = DateUtil.getLocalNow();
        String unsignedDateTimeStr = DateUtil.getSomeDateFormatString(now, DateUtil.UNSIGNED_DATE_TIME_FORMATTER); //sdf
        String dateTimeStr = DateUtil.getSomeDateFormatString(now, DateUtil.DATE_TIME_FORMATTER); //sdf1
        String unsignedDateStr = DateUtil.getSomeDateFormatString(now, DateUtil.UNSIGNED_DATE_FORMATTER); //sdf2
        String unsignedDateTimeHourMinuteStr = DateUtil.getSomeDateFormatString(now, DateUtil.UNSIGNED_TIME_HOUR_MINUTE_FORMATTER); //sdf3
        String dateTimeHourMinuteStr = DateUtil.getSomeDateFormatString(now, DateUtil.DATE_TIME_HOUR_MINUTE_FORMATTER); //sdf4
        StaffUserInfo staffUserInfo = userDao.SelectServiceStaffByUserId(issue.getServiceId());
        String title = "【客戶留言】";
        //保存資料 仅台湾区云管家才走此流程
        //3.5.1 流程变更
        if (staffUserInfo != null && issue.getServiceId() != null) {
            issue.setWorkNo(staffUserInfo.getWorkNo());
        }
        //1.状态=已结案
        if (IssueStatus.Closed.toString().equals(issue.getIssueStatus())) {
            //1.1 已结案-調用新增案件
            newIssue.setIssueStatus("N");
            newIssue.setCrmId("");
            if ("161".equals(newIssue.getProductCode()) || "157".equals(newIssue.getProductCode())) {
                newIssue.setSyncStatus(SyncStatus.DontNeedSync.toString());
            } else {
                newIssue.setSyncStatus(SyncStatus.UnSync.toString());
            }

            if (StringUtils.isEmpty(newIssue.getSubmitWay()) || !newIssue.getSubmitWay().contains("SCP_")) {
                newIssue.setSubmitWay("SCP_3.5.1.0729");
            }
            newIssue.setIssueProgresses(null);
            newIssue.setIssueCasedetail(null);
            newIssue.setDepartment(newIssue.getServiceDepartment());
            String desc = title + dateTimeHourMinuteStr + newIssue.getIssueDescription();
            newIssue.setIssueDescription(desc);
            //檢查原責任客服是否離職了，若離值則改默認該產品主管
            if (StringUtils.isNotEmpty(newIssue.getServiceId())) {
                String staffId = userDao.getSeviceStaffWork(newIssue.getServiceId());
                //檢查原責任客服已離職或者查無或者已換部門則改默認該產品主管
                if (StringUtils.isBlank(staffId) ||
                        (StringUtils.isNotEmpty(staffUserInfo.getDepartment()) &&
                                !staffUserInfo.getDepartment().equals(newIssue.getServiceDepartment()))) {
                    StaffUserInfo managerInfo = userDao.getProductManager(newIssue.getProductCode());
                    if (managerInfo != null) {
                        newIssue.setServiceStaff(managerInfo.getFullName());
                        newIssue.setServiceId(managerInfo.getUserId());
                        newIssue.setDepartment(managerInfo.getDepartment());
                    }
                }
            } else {
                //若原案件无责任人员则也默认该产品主管
                StaffUserInfo managerInfo = userDao.getProductManager(newIssue.getProductCode());
                if (managerInfo != null) {
                    newIssue.setServiceStaff(managerInfo.getFullName());
                    newIssue.setServiceId(managerInfo.getUserId());
                    newIssue.setDepartment(managerInfo.getDepartment());
                }
            }
            int res = SubmitIssue(newIssue); //這邊還沒有 crmid
            if (res == 0) {
                Long issueId = newIssue.getIssueId();
                System.out.print("Before do SubmitIssueAsync" + issueId);
                SubmitIssueAsync(newIssue, "N", true); // 這邊才會產生 CRMID
                System.out.print("Before do SubmitIssueAsync" + issueId);

            } else {
                System.out.print("SubmitIssue error " + Integer.toString(res));
            }
            // 寫入案件反饋表
            SaveIssueAdditionalExplanationDetail(issue);
        } else {
            boolean hasAnnex = false;//是否有夾帶附件
            if (!CollectionUtils.isEmpty(issue.getIssueAttachments())) {
                hasAnnex = true;
            }
            //2.状态=未结案
            IssueProgress issueProgress = null;
            String submitDate = unsignedDateStr;
            String processTime = unsignedDateTimeHourMinuteStr;
            //String syncStatus = "N".equals(issue.getSyncStatus()) ? "T" : "Z";
            String syncCrmStatus = "Z";
            int success = 0;
            String progressId = "";
            //2.1 请求结案-自动结案
            if (issue.getRequestCloseIssue()) {
                //2.1.1 雲管家更新結案及單身寫入結案明細
                //写入单身
                String desc = dateTimeHourMinuteStr + issue.getIssueCasedetail().getAdditionalExplanation();
                issueProgress = insertProgess(issue, userId, false, dateTimeStr, desc, ReplyType.Q.toString(), SyncStatus.DontNeedSync.toString());
                if (!"06".equals(issue.getProductCode()) && !"100".equals(issue.getProductCode())) {
                    insertProgess(issue, userId, true, dateTimeStr, "CN".equals(issue.getServiceRegion()) ? "结案" : "Close Issue", "", SyncStatus.DontNeedSync.toString());
                    //更云管家单头状态
                    success = issueDao.UpdateIssueStatusByStaff(issue.getIssueId(), IssueStatus.Closed.toString(), issue.getIssueStatus(), issue.getDepartment(), userId);
                    //更新回 influxDB 如果有預警編號的話
                    updateWarningNoticeStatus(issue.getCrmId());
                }
                //2.1.2 調用資管寫入單身及更新單頭結案
                if (issueProgress != null) {
                    //判斷若未同步至crm則不調用 改由駐派員同步去處理
                    if ("161".equals(newIssue.getProductCode()) || "157".equals(newIssue.getProductCode())) {
                        syncCrmStatus = "Z";
                    } else {
                        if ("N".equals(issue.getSyncStatus()) || "E".equals(issue.getSyncStatus())) {
                            syncCrmStatus = "N"; //需重拋單身至crm
                        } else {
                            //写入Crm单身
                            String res1 = issueDao.InsertCrmCaseProcess(issue.getCrmId(), submitDate, processTime, staffUserInfo.getWorkNo(), issueProgress, true);
                            if (StringUtils.isNotBlank(res1)) {
                                syncCrmStatus = "Y";
                                updateIssueProgress(issueProgress, res1);
                                //更新Crm单头状态为已结案(排除06及100)
                                if (!"06".equals(issue.getProductCode()) && !"100".equals(issue.getProductCode()) && success > 0) {
                                    boolean b2 = issueDao.updateCrmCase(issue.getCrmId(), submitDate, staffUserInfo.getWorkNo(), staffUserInfo.getDepartment());
                                    if (!b2) {
                                        syncCrmStatus = "C"; //僅需更新單頭狀態即可
                                    }
                                } else {
                                    syncCrmStatus = "Z";
                                }
                            } else {
                                syncCrmStatus = "N"; //需重拋單身至crm
                            }
                        }
                    }
                    //寫入案件反饋表
                    SaveIssueAdditionalExplanationDetailNew(issue, issueProgress.getId(), syncCrmStatus, dateTimeStr, hasAnnex);
                    //保存附件
                    if (hasAnnex) {
                        SaveAdditionalExplanationAttachments(issue.getIssueId(), issueProgress.getId(), issue, unsignedDateTimeStr);
                    }
                    progressId = Long.toString(issueProgress.getId());
                }
                // 更新单头客服最新回复
                issueDetailServiceV3.updateIssueNewReply(String.valueOf(issue.getIssueId()), NewReplyEnum.Null.toString());
            } else {
                //2.2 寫入雲管家處理明細調用資管寫入單身
                String desc = title + dateTimeHourMinuteStr + issue.getIssueCasedetail().getAdditionalExplanation();
                issueProgress = insertProgess(issue, userId, false, dateTimeStr, desc, ReplyType.Q.toString(), SyncStatus.DontNeedSync.toString());
                if (issueProgress != null) {
                    if ("161".equals(newIssue.getProductCode()) || "157".equals(newIssue.getProductCode())) {
                        syncCrmStatus = "Z";
                    } else {
                        if (!"N".equals(issue.getSyncStatus()) && !"E".equals(issue.getSyncStatus())) {
                            //写入Crm单身
                            String res2 = issueDao.InsertCrmCaseProcess(issue.getCrmId(), submitDate, processTime, staffUserInfo.getWorkNo(), issueProgress, false);
                            if (StringUtils.isNotBlank(res2)) {
                                syncCrmStatus = "Y";
                                updateIssueProgress(issueProgress, res2);
                            } else {
                                syncCrmStatus = "E"; //需重拋單身至crm
                            }

                        } else {
                            syncCrmStatus = "N";
                        }
                    }
                    //寫入案件反饋表
                    SaveIssueAdditionalExplanationDetailNew(issue, issueProgress.getId(), syncCrmStatus, dateTimeStr, hasAnnex);
                    //保存附件
                    if (hasAnnex) {
                        SaveAdditionalExplanationAttachments(issue.getIssueId(), issueProgress.getId(), issue, unsignedDateTimeStr);
                    }
                    progressId = Long.toString(issueProgress.getId());
                }

                // 更新单头客服最新回复 重新取案件状态，是防止案件在crm已经结案了，云管家传过来的案件状态是处理中。导致案件结案了，又出现客户的最新回复
                String issueStatus = issueDao.SelectIssueStatus(issue.getIssueId());
                if (IssueStatus.Closed.toString().equals(issueStatus) || IssueStatus.Evaluated.toString().equals(issueStatus)) {
                    //清空最新回复
                    issueDetailServiceV3.updateIssueNewReply(String.valueOf(issue.getIssueId()), NewReplyEnum.Null.toString());
                } else {
                    issueDetailServiceV3.updateIssueNewReply(String.valueOf(issue.getIssueId()), NewReplyEnum.CustomerNewReply.toString());
                }
            }
            //发送邮件和通知给客服
            try {
                SendAdditionalExplanation(issue, progressId);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            //发送邮件给客戶
            try {
                SendAdditionalExplanationToUser(issue, progressId);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

    }

    private void updateIssueProgress(IssueProgress issueProgress, String res) {
        //依crm回傳序號更新
        //"新增成功 /單號:ES0002370080 /日期:20221220 /序號:01"
        String crm_BR002 = "";
        String crm_BR003 = "";
        String[] tmpArr = res.split("/");
        if (tmpArr.length > 2) {
            for (String s : tmpArr) {
                if (s.contains("日期:")) {
                    crm_BR002 = s.replace("日期:", "").trim();
                }
                if (s.contains("序號:")) {
                    crm_BR003 = s.replace("序號:", "").trim();
                }
            }
        }
        issueDao.UpdateProcessSeq(issueProgress.getIssueId(), issueProgress.getId(), crm_BR002, crm_BR003);
    }

    private IssueProgress insertProgess(Issue issue, String userId, boolean closed, String processTime, String desc, String replyType, String syncStatus) {
        IssueProgress issueProgress = new IssueProgress();
        if (closed) {
            issueProgress.setSequeceNum(issueDao.getMaxOrder(issue.getIssueId()) + 1);
            issueProgress.setProcessType(IssueProcessType.Close.toString());
            issueProgress.setDescription(desc);
            issueProgress.setReplyType("");
        } else {
            issueProgress.setSequeceNum(issueDao.getProcessMaxSeqNum(issue.getIssueId()) + 1);
            issueProgress.setProcessType(IssueProcessType.Process.toString());
            issueProgress.setDescription(desc);
            issueProgress.setReplyType(replyType);
        }
        issueProgress.setProcessHours(0.01);
        issueProgress.setProcessor(userId);
        issueProgress.setIssueId(issue.getIssueId());
        issueProgress.setWorkno(issue.getWorkNo());
        issueProgress.setProcessTime(processTime);
        issueProgress.setHandlerId(issue.getServiceId());
        if (IssueProcessType.Process.toString().equals(issueProgress.getProcessType())) {
            issueProgress.setCurrentStatus(IssueStatus.Processing.toString());
        } else {
            issueProgress.setCurrentStatus(issue.getIssueStatus());
        }
        issueProgress.setIssueId(issue.getIssueId());
        issueProgress.setCrmId(issue.getCrmId());
        issueProgress.setSyncStatus(syncStatus);
        long progressId = issueDao.InsertIssueProgressForCC(issueProgress);
        //更新总工时
        issueDetailDao.updateDetailIssueTotalWorkHours(issue.getIssueId());
        if (closed) {
            //請求結案要多寫入issue_summary
            saveIssueSummaryForAgressClose(String.valueOf(issue.getIssueId()), processTime);
        }
        issueProgress.setId(progressId);
        return issueProgress;
    }

    @Async
    private void SendFollowUpMail(Issue issue, String desc) {
        //发送邮件给客户
        try {
            SendFollowUpMailToSubmiter(issue, desc);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        //发送邮件和通知给客服
        try {
            SendFollowUpNoticeToCC(issue, desc);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void SendFollowUpMailToSubmiter(Issue issue, String desc) {
        try {
            String[] mx = {PrepareIssueFollowUpMail(issue, desc)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    private void SendFollowUpNoticeToCC(Issue issue, String desc) {
        try {
            String[] mx = {PrepareIssueFollowUpMailToCC(issue, desc)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    //    private List<String> GetAdditionalExplanationReceivers() {
//
//    }
    //保存call-center之資訊
    private void SaveIssueAdditionalExplanationDetail(Issue issue) {
        if (LongUtil.isEmpty(issue.getIssueId())) {
            return;
        }
        if (StringUtils.isBlank(issue.getIssueDescription())) {
            return;
        }
        try {
            issueDetailDao.insertAdditionalExplanationDetailIssue(issue);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    //保存call-center之資訊_新流程
    private void SaveIssueAdditionalExplanationDetailNew(Issue issue, long progressId, String syncStatus, String syncTime, boolean syncAnnex) {
        if (LongUtil.isEmpty(issue.getIssueId())) {
            return;
        }
        if (StringUtils.isBlank(issue.getIssueDescription())) {
            return;
        }
        try {

            IssueAdditionalExplanation iae = new IssueAdditionalExplanation();
            iae.setProgessId(progressId);
            iae.setSyncStatus(syncStatus);
            iae.setIssueId(issue.getIssueId());
            iae.setCrmId(issue.getCrmId());
            iae.setSubmitTime(issue.getSubmitTime());
            iae.setDescription(issue.getIssueCasedetail().getAdditionalExplanation());
            if ("Y".equals(syncStatus)) {
                iae.setSyncTime(syncTime);
            }
            if (syncAnnex) {
                iae.setSyncAnnex("N");
            } else {
                iae.setSyncAnnex("Z");
            }
            issueDetailDao.insertAdditionalExplanationDetailIssueNew(iae);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void saveCasedetailNew(Issue issue, boolean isSIMIssue) {
        try {
            IssueCasedetail issueCasedetail = issue.getIssueCasedetail();
            if (issueCasedetail != null) {
                issueCasedetail.setIssueId(Long.toString(issue.getIssueId()));
                /*//易聊提交的案件，需要有单身，并且必须填写问题分类，因此issue_casedetail的syncstatus必须等于T
                if (isSIMIssue) {
                    issueCasedetail.setSyncStatus("T");
                } else {
                    issueCasedetail.setSyncStatus("Z");
                }*/
                //2010-1-15 huly跟偉勝沟通后确认只存T
                issueCasedetail.setSyncStatus("T");
                issueDetailDao.insertDetailIssue(issueCasedetail);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void saveCasedetail(Issue issue, boolean isSIMIssue) {
        try {
            IssueCasedetail issueCasedetail = issue.getIssueCasedetail();
            if (issueCasedetail != null) {
                issueCasedetail.setIssueId(Long.toString(issue.getIssueId()));
                /*//易聊提交的案件，需要有单身，并且必须填写问题分类，因此issue_casedetail的syncstatus必须等于T
                if (isSIMIssue) {
                    issueCasedetail.setSyncStatus("T");
                } else {
                    issueCasedetail.setSyncStatus("Z");
                }*/
                //2010-1-15 huly跟偉勝沟通后确认只存T
                issueCasedetail.setSyncStatus("T");
                if ("888".equals(issue.getProductCode()) || "147".equals(issue.getProductCode()) || "161".equals(issue.getProductCode()) || "157".equals(issue.getProductCode())) { // 147 产品线 不需要同步
                    issueCasedetail.setSyncStatus(SyncStatus.DontNeedSync.toString());
                }
                issueDetailDao.insertDetailIssue(issueCasedetail);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void SaveIssueProgress(Issue issue, boolean isRelateIssue) {
        try {
            List<IssueProgress> issueProgresses = issue.getIssueProgresses();
            if (CollectionUtils.isEmpty(issueProgresses)) { //huly: 修复漏洞/bug CollectionUtils.isEmpty
                return;
            }
            //立案单身时间，改成使用单头的时间，不再自己生成
            //DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //String date = sdf.format(new Date());
            String date = issue.getSubmitTime();
            // 20240118 任務7779 台灣區 取表issue_chatfile_config查該服務中心、該產品線是否有要做chatfile查詢(issueChatFileSearch=true) ，若有則不加"您的问题已受理"之單身
            //查該服務中心、該產品線是否有要做chatfile查詢
            boolean hasChatFileSearch = issueDao.checkIssueHasChatFileSearch(issue.getServiceRegion(),issue.getProductCode());
            for (IssueProgress progress : issueProgresses) {
                // 因台灣服務中心、4大產品線 您的問題已受理 之單身改由駐派員同步時添加，但之前的雲管家版本會有這一資料，所以要濾除
                // 20240118 任務7779 台灣區 取表issue_chatfile_config查該服務中心、該產品線是否有要做chatfile查詢(issueChatFileSearch=true) ，若有則不加"您的问题已受理"之單身
               // if ("TW".equals(connectArea) && "TW".equals(issue.getServiceRegion()) && ("02".equals(issue.getProductCode()) || "10".equals(issue.getProductCode()) || "61".equals(issue.getProductCode()) || "pos".equals(issue.getProductCode()))) {
                    if ("TW".equals(connectArea) && hasChatFileSearch && ("您的問題已受理".equals(progress.getDescription()) || "您的问题已受理".equals(progress.getDescription()) || "Accept Issue".equals(progress.getDescription()) || "คำถามของคุณได้รับการยอมรับแล้ว".equals(progress.getDescription()) || "Câu hỏi của bạn đã được chấp nhận".equals(progress.getDescription()))) {
                        continue;
                    }
             //   }
                progress.setProcessTime(date);
                //add by dukun 大陆客服要求客服易聊立案时，案件状态需为处理中N=======BEGIN=========
                if (issue.getSubmitWay().startsWith("SIM_") || issue.getSubmitWay().startsWith("EASY_TALK")) {
                    if (IssueProcessType.Process.toString().equals(progress.getProcessType()))
                        progress.setCurrentStatus(IssueStatus.Processing.toString());
                    else
                        progress.setCurrentStatus(issue.getIssueStatus());
                } else if (isRelateIssue) {
                    // 2023/03/02 關聯案件 則單身的CurrentStatus要做紀錄，不然大陸駐派員同步會有問題
                    progress.setCurrentStatus(issue.getIssueStatus());
                }
                //add by dukun 大陆客服要求客服易聊立案时，案件状态需为处理中N=======END=========
                //modify by daixy1
                //2021-12-30 易聊聊天立案 如果是立案并结案 台湾的结案单身同步状态为空 大陆不变
                if ("TW".equals(connectArea)) {
                    if (IssueProcessType.Close.toString().equals(progress.getProcessType())) {
                        progress.setSyncStatus("");
                    }
                }
                if ("888".equals(issue.getProductCode()) || "147".equals(issue.getProductCode()) || "161".equals(issue.getProductCode()) || "157".equals(issue.getProductCode())) {   // 147 产品线，不需要同步
                    progress.setSyncStatus(SyncStatus.DontNeedSync.toString());
                }
                if ("CN".equals(issue.getServiceRegion())) {
                    if ("Submit Issue".equals(progress.getDescription())) {
                        progress.setDescription("提交问题");
                    } else if ("Accept Issue".equals(progress.getDescription())) {
                        progress.setDescription("问题受理中");
                    } else if ("Resolved Issue".equals(progress.getDescription())) {
                        progress.setDescription("问题已解决");
                    } else if ("Close Issue".equals(progress.getDescription())) {
                        progress.setDescription("结案");
                    } else if ("Cancel Close Issue".equals(progress.getDescription())) {
                        progress.setDescription("取消结案");
                    } else {

                    }
                }
                issueDao.InsertIssueProgress(issue.getIssueId(), issue.getCrmId(), progress);
                //SaveIssueProgress(issue.getIssueId(), issue.getCrmId(), IssueProcessType.Process, progress.getProcessor(), progress.getDescription());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
     * 发送邮件给客服人员 抄送给其他客服、客户、CC
     *
     * @param issue
     */
    private void SendMailToCustomerService_T(Issue issue) {
        try {
            String[] mx = {PrepareIssueMailToCustomerService_T(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 发送邮件给案件提交人
     *
     * @param issue
     */
    //@Async
    private void SendMailToSubmiterByConfirm(Issue issue, boolean isConfirm) {
        try {
            String[] mx = {PrepareIssueMailByConfirm(issue, isConfirm)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 发送退回邮件给案件提交人
     *
     * @param issue
     */
    //@Async
    private void SendBackMailToSubmiter(Issue issue) {
        try {
            String[] mx = {PrepareIssueBackMail(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 发送邮件给案件提交人
     *
     * @param issue
     */
    //@Async
    private void SendMailToSubmiter(Issue issue) {
        try {
            String[] mx = {PrepareIssueMail(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * for 越南T 大陆T的案件使用
     *
     * @param issueId
     * @param issue
     */
    //@Async
    private void CompleteIssueInfoNew(long issueId, Issue issue, String issueStatus, boolean isConfirm) {
        try {
            if (isConfirm) {
                issue.setIssueStatus(IssueStatus.Confirming.toString());
            } else {
                if (org.apache.commons.lang.StringUtils.isNotBlank(issueStatus) && issue.getSubmitWay().startsWith("SIM_")) {
                    issue.setIssueStatus(issueStatus);//已結案
                } else {
                    issue.setIssueStatus(IssueStatus.Submited.toString());
                }

            }

//            } else if (issue.getIssueType().equals(IssueType.UpdateRequest.toString())){
//                List<String> userRole=userService.GetUserRole(issue.getUserId());
//                if (userRole.contains(UserRole.mis.toString()))
//                    issue.setIssueStatus(IssueStatus.Submited.toString());
//                else
//                    issue.setIssueStatus(IssueStatus.Confirming.toString());
//            }
            //3.2 获取案件提交人联系方式Id
            long userContactId = userDao.GetUserContractId(issue.getUserContact());
            //目前不直接掉用其他微服务来取得，因为逻辑相对简单，跨微服务调用容易失败
//            long userContactId = -1;
//            //嘗試3次
//            int tryCount = 3;
//            do {
//                System.out.print(issueId + " try get user contact id try count:" + tryCount);
//                try {
//                    userContactId = userService.GetUserContactId(issue.getUserContact());
//                } catch (Exception ex) {
//                    ex.printStackTrace();
//                }
//                tryCount--;
//            } while (userContactId < 0 && tryCount > 0);
            if (userContactId < 0) {
                System.out.print(issueId + " get user contact id fail!");
            }

            //3.2 更新客服信息
            if (!isConfirm) {
                if (issue.getServiceStaff() == null || issue.getServiceStaff().isEmpty()) {
                    try {
                        String area = "";
                        if (!StringUtils.isEmpty(issue.getIssueCasedetail().getErpSystemCode())) {
                            Module module = issueProcessMapper.getModuleByModuleCode(issue.getProductCode(), issue.getIssueCasedetail().getErpSystemCode());
                            if (!ObjectUtils.isEmpty(module)) {
                                area = module.getArea();
                            }
                        }

                        List<String> list = userService.GetServiceStaffInfoForT(
                                issue.getServiceCode(), issue.getProductCode() == null ? "" : issue.getProductCode()
                                , issue.getIssueCasedetail().getErpSystemCode(), area
                        );
                        //list不为空的时候，判定是否设置代理人
                        if(CollectionUtil.isNotEmpty(list)){
                            String agentWorkNo = issueProcessMapper.getAgentWorkNo(list.get(0));
                            if(StringUtils.isNotEmpty(agentWorkNo)){
                                list = Arrays.asList(agentWorkNo);
                            }
                        }
                        if(CollectionUtils.isEmpty(list)){
                            list =  issueProcessMapper.getDefaultProcess(issue.getProductCode());
                        }
                        if (!CollectionUtils.isEmpty(list)) {
                            String workno = list.get(0);
                            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByWorkNo(workno);
                            if (staffUserInfo != null) {
                                issue.setServiceStaff(staffUserInfo.getUserId());
                                issue.setDepartment(staffUserInfo.getDepartment());

                            }
                            //得到群抄送人mail
                            String worknos = "";
                            for (String temp : list) {
                                if (temp.equals(workno)) {
                                    continue;
                                }
                                worknos += temp + ",";
                            }
                            if (!StringUtils.isEmpty(worknos)) {
                                List<String> cc = userService.GetUserEmailList(worknos.substring(0, worknos.length() - 1));
                                if (!CollectionUtils.isEmpty(cc)) {
                                    String ccmail = "";
                                    for (String temp : cc) {

                                        ccmail += temp + ";";
                                    }
                                    issue.setCcGroupUser(ccmail.substring(0, ccmail.length() - 1));
                                }
                            }

                        }
                        /*System.out.print(serviceStaffGetResponse);
                        if (serviceStaffGetResponse != null && serviceStaffGetResponse.getCode().equals("0")) {
                            staffUserInfo = serviceStaffGetResponse.getStaffUserInfo();
                            //staffUserInfo = userService.GetServiceStaffInfo(issue.getServiceCode(), issue.getProductCode() == null ? "" : issue.getProductCode());
                            //设置客户群抄收人
                            issue.setCcGroupUser(serviceStaffGetResponse.getMailCC());
                            if (staffUserInfo != null) {
                                issue.setServiceStaff(staffUserInfo.getUserId());
                                issue.setDepartment(staffUserInfo.getDepartment());
                                //重新设置客户群抄收人
                                if("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())){
                                    issue.setCcGroupUser(staffUserInfo.getService_cc_staff_emails());
                                }
                            }


                            //设置是否没有责任人员
                            issue.setNoResponsiblePersion(serviceStaffGetResponse.isNoResponsiblePerson());
                        }*/
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }

            //3.3 更新产品版本
            try {
                if (issue.getProductVersion() == null || issue.getProductVersion().isEmpty()) {
                    issueDao.UpdateIssueProductVersion(issue);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

//            if (staffUserInfo.getUserId().isEmpty()){
//
//            }else {
            //组织crmId
            String crmId;
            String warningId = Optional.ofNullable(issue.getWarningId()).orElse("");
            String sourceType = Optional.ofNullable(issue.getSourceType()).orElse("");
            if (issue.getSubmitWay().startsWith("SIM_")) {
                crmId = "ESM" + String.format("%09d", issueId);
            } else {

                if (warningId.length() > 0) {
                    if (issue.getSubmitWay().equals(IssueSubmitMode.ITMS.toString())) {
                        //科維预警立案(客户)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    } else if (issue.getSubmitWay().equals(IssueSubmitMode.ITMS_Service.toString())) {
                        //科維预警立案(客服)
                        crmId = IssueCodeRoleStart.ESD.toString() + String.format("%09d", issueId);
                    } else {
                        //科維预警立案(客户)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    }

                } else if ("EDREvent".equals(sourceType)) {
                    if (issue.getSubmitWay().startsWith(IssueSubmitMode.AIOSSM.toString())) {
                        //企業運維立案(客服)
                        crmId = IssueCodeRoleStart.ESD.toString() + String.format("%09d", issueId);
                    } else if (issue.getSubmitWay().equals(IssueSubmitMode.AIEOM.toString())) {
                        //企業運維立案(客戶)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    } else {
                        //科維预警立案(客户)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    }
                } else {
                    if (issue.getIssueSubmitMode() != null) {
                        if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString()))
                            crmId = "MSG" + String.format("%09d", issueId);
                        else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString()))
                            crmId = "CON" + String.format("%09d", issueId);
                        else
                            crmId = "ES" + String.format("%010d", issueId);
                    } else
                        crmId = "ES" + String.format("%010d", issueId);
                }

            }
            issue.setCrmId(crmId);
            issueDao.UpdateIssue(issueId, crmId, issue.getServiceStaff(), issue.getDepartment(), issue.getIssueStatus(), userContactId);
            //不在这里调整案件同步状态，避免案件还未完善就被同步

            //如果有預警編號,  則更新預警編號的狀態
            this.updateWarningNoticeStatus(issue);
            System.out.print("update EsClientService Warning Notice status:" + issueId);

            //SendNoticeToCC(issue);
            String syncStatus = "";
            if ("888".equals(issue.getProductCode()) || "147".equals(issue.getProductCode()) || "161".equals(issue.getProductCode()) || "157".equals(issue.getProductCode())) {
                syncStatus = SyncStatus.DontNeedSync.toString();
            }
            if (isConfirm) {
                // 查询审核人
                String confirmIds = GetCustomerIssueConfirmer(issue);
                SaveIssueCreateLog(issueId, crmId, issue.getUserId(), issue.getSubmitTime(), confirmIds, issue.getIssueStatus(), syncStatus, issue.getServiceRegion());
            } else {
                SaveIssueCreateLog(issueId, crmId, issue.getUserId(), issue.getSubmitTime(), issue.getServiceStaff(), issue.getIssueStatus(), syncStatus, issue.getServiceRegion());
            }
//            }
            //处理issueKbShare
            dealIssueKbShare(issue);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    private void dealIssueKbShare(Issue issue){
        // 20240118 任務7779 取表issue_chatfile_config查該服務中心、該產品線是否有要做chatfile查詢(issueChatFileSearch=true) ，若有則表Issue_kbShare中該筆之FinishSearchChatFile為null，否則為N
        //查該服務中心、該產品線是否有要做chatfile查詢
        boolean hasChatFileSearch = issueChatfileConfigCache.selectIssueChatFileConfig(issue.getServiceRegion(),issue.getProductCode());
        // 檢查是否要用chatFile 查內部說明
        boolean isSearchByChatFile = issueChatfileConfigCache.getIsSearchByChatFile(RedisKeyConstants.ISSUE_SEARCH_CHATFILE);
        if (isSearchByChatFile && hasChatFileSearch) {
            if (issue.getSubmitWay().startsWith("CTI_Phone") || (issue.getIssueKbshare().getKbid() == null && StringUtils.isEmpty(issue.getIssueKbshare().getProductCode()) && issue.getSubmitWay().startsWith("SCP_"))) {
                // 要4大產品線
                // 只寫入Issue_kbShare
                IssueKbshare issueKbshare = new IssueKbshare();
                issueKbshare.setCrmId(issue.getCrmId());
                issueKbshare.setId(issue.getIssueId());
                issueKbshare.setProductCode(issue.getProductCode());
                issueKbshare.setSearchText(issue.getIssueDescription());
                issueKbshare.setSubmitTime(issue.getSubmitTime());
                issueDao.InsertIssueKbshare(issue.getIssueId(), issue.getCrmId(), issueKbshare);
            } else {
                if (issue.getIssueKbshare() != null && !StringUtils.isEmpty(issue.getIssueKbshare().getProductCode())) {
                    //只寫入Issue_Kbshare
                    IssueKbshare issueKbshare = issue.getIssueKbshare();
                    issueKbshare.setSubmitTime(issue.getSubmitTime());
                    issueDao.InsertIssueKbshare(issue.getIssueId(), issue.getCrmId(), issueKbshare);
                }
            }
        } else if(issue.getIssueKbshare() != null && !StringUtils.isEmpty(issue.getIssueKbshare().getProductCode()) && !StringUtils.isEmpty(issue.getIssueKbshare().getChatFileContent())){
            // 表示IssueKbshare有chatfile查詢的內容，所以直接紀錄於表issue_kbshare中
            IssueKbshare issueKbshare = issue.getIssueKbshare();
            issueKbshare.setSubmitTime(issue.getSubmitTime());
            issueDao.InsertIssueKbshare(issue.getIssueId(), issue.getCrmId(), issueKbshare);
        }  else {
            // 維持原本的
            //判断是否有推荐案件若有的话写入issueKbshare且一定要有處理人員工號才寫入單身(不然同步會出錯)
            if (issue.getIssueKbshare() != null && !StringUtils.isEmpty(issue.getIssueKbshare().getProductCode()) && issue.getWorkNo() != null) {
                IssueKbshare issueKbshare = issue.getIssueKbshare();
                issueKbshare.setSubmitTime(issue.getSubmitTime());
                issueKbshare.setFinishSearchChatFile("N"); //不查找
                issueDao.InsertIssueKbshare(issue.getIssueId(), issue.getCrmId(), issueKbshare);
                if (!CollectionUtils.isEmpty(issue.getIssueProgresses()) && issue.getIssueProgresses().get(0) != null) {
                    issue.getIssueProgresses().get(0).setProcessor(issue.getServiceStaff());
                    issue.getIssueProgresses().get(0).setWorkno(issue.getWorkNo());
                    issue.getIssueProgresses().get(0).setProcessHours(0.01);
                    issue.getIssueProgresses().get(0).setProcessType(IssueProcessType.Process.toString());
                    issue.getIssueProgresses().get(0).setReplyType(ReplyType.A.toString());
                    issue.getIssueProgresses().get(0).setSyncStatus(SyncStatus.EditUnSync.toString());
                    issue.getIssueProgresses().get(0).setHandlerId(issue.getServiceStaff());
                }
            }
        }
    }

    /**
     * 完善案件资料，并通知客服
     *
     * @param issueId
     * @param issue
     */
    //@Async
    private void CompleteIssueInfo(long issueId, Issue issue, String issueStatus, boolean isConfirm) {
        try {
            if (isConfirm) {
                issue.setIssueStatus(IssueStatus.Confirming.toString());
            } else {
                if (org.apache.commons.lang.StringUtils.isNotBlank(issueStatus) && (issue.getSubmitWay().startsWith("SIM_") || issue.getSubmitWay().startsWith("EASY_TALK"))) {
                    issue.setIssueStatus(issueStatus);//易聊提交案件，会直接传issueStatus
                } else if (!StringUtils.isEmpty(issueStatus) && issue.getSubmitWay().startsWith("AIOSSM")) {   //企業運維 立案  責任人員由前端提供，案件狀態也由前端提供
                    issue.setIssueStatus(issueStatus);//提交案件，会直接传issueStatus
                } else if (!StringUtils.isEmpty(issueStatus) && issue.getSubmitWay().startsWith("AIEOM")) {   //企業運維 mis立案，案件狀態也由前端提供
                    issue.setIssueStatus(issueStatus);//提交案件，会直接传issueStatus
                } else {
                    issue.setIssueStatus(IssueStatus.Submited.toString());
                    //2022-08-09：雲管家案件提交時, 寫入的案件狀態(issue.IssueStatus)需判斷mars_product.accept 的設定,當mars_product.accept=0時,寫入N, 當mars_product.accept=1時,寫入C
                    IssueSwitchCache issueSwitchCache = IssueServiceApplication.applicationContext.getBean(IssueSwitchCache.class);
                    IssueSwitch issueSwitch = issueSwitchCache.selectIssueSwitch(issue.getProductCode());
                    if (issueSwitch != null && !issueSwitch.getAccept()) {
                        issue.setIssueStatus(IssueStatus.Processing.toString());
                    }
                }

            }

//            } else if (issue.getIssueType().equals(IssueType.UpdateRequest.toString())){
//                List<String> userRole=userService.GetUserRole(issue.getUserId());
//                if (userRole.contains(UserRole.mis.toString()))
//                    issue.setIssueStatus(IssueStatus.Submited.toString());
//                else
//                    issue.setIssueStatus(IssueStatus.Confirming.toString());
//            }
            //3.2 获取案件提交人联系方式Id
            long userContactId = 0L;
            if (issue.getUserContact() != null && issue.getUserContact().getId() == 0) {
                if (StringUtils.isEmpty(issue.getUserContact().getUserId())) {
                    issue.getUserContact().setUserId(issue.getUserId());
                }
                userContactId = userDao.GetUserContractId(issue.getUserContact());
                if (userContactId < 0) {
                    System.out.print(issueId + " get user contact id fail!");
                } else {
                    issue.getUserContact().setId(userContactId);
                }
            } else {
                userContactId = issue.getUserContact().getId();
            }

            //3.2 更新客服信息
            //易聊客服立案会传处理人
            if (StringUtils.isEmpty(issue.getServiceId())) {
                if (!isConfirm) {
                    ServiceStaffGetResponse serviceStaffGetResponse = null;
                    if (issue.getServiceStaff() == null || issue.getServiceStaff().isEmpty()) {
                        try {
                            StaffUserInfo staffUserInfo = new StaffUserInfo();

                            String productCode = issue.getProductCode() == null ? "" : issue.getProductCode();

                            //3567 朱昭熹:142產品線案件分派邏輯調整，修改完成后，hotfix立即发版
                            boolean hasTipTopProductCode = Boolean.FALSE;
                            if (connectArea.equals("TW")) {
                                if ("142".equals(issue.getProductCode())) {
                                    //檢查有沒有買過 06 或 100 的產品線, 如果有, 則用 06 去查找
                                    boolean has06ProductCode = issueProcessMapper.getTipTopProductCode(issue.getServiceCode(), "06") > 0;
                                    boolean has100ProductCode = issueProcessMapper.getTipTopProductCode(issue.getServiceCode(), "100") > 0;

                                    //1059 朱昭熹:142產品線案件分派邏輯調整--20210526 add 調整規則
                                    if (has06ProductCode && has100ProductCode) {
                                        //兩個都有, 就用 100
                                        productCode = "100";
                                        hasTipTopProductCode = Boolean.TRUE;
                                    } else if (has06ProductCode) {
                                        //一個有一個沒有(合約等級不為 G), 就用那個產品線
                                        productCode = "06";
                                        hasTipTopProductCode = Boolean.TRUE;
                                    } else if (has100ProductCode) {
                                        //一個有一個沒有(合約等級不為 G), 就用那個產品線
                                        productCode = "100";
                                        hasTipTopProductCode = Boolean.TRUE;
                                    } else {
                                        //兩個都為 G 級, 就用原本的 142
                                        productCode = "142";
                                    }
                                }
                            }

                            String moduleCode = (issue.getIssueCasedetail() != null && !StringUtils.isEmpty(issue.getIssueCasedetail().getErpSystemCode())) ? issue.getIssueCasedetail().getErpSystemCode() : "";
                            serviceStaffGetResponse = userService.GetServiceStaffInfo(
                                    issue.getUserId(), issue.getServiceCode(), productCode
                                    , issue.getServiceRegion() == null ? "" : issue.getServiceRegion(), issue.getIssueDescription(), issue.getIssueClassificationNo() == null ? "" : issue.getIssueClassificationNo()
                                    , issue.getSubmitWay() == null ? "" : issue.getSubmitWay(), moduleCode, issue.getUserId() == null ? "" : issue.getUserId()
                            );

                            System.out.print(serviceStaffGetResponse);
                            if (serviceStaffGetResponse != null && serviceStaffGetResponse.getCode().equals("0")) {
                                staffUserInfo = serviceStaffGetResponse.getStaffUserInfo();
                                //staffUserInfo = userService.GetServiceStaffInfo(issue.getServiceCode(), issue.getProductCode() == null ? "" : issue.getProductCode());
                                //设置客户群抄收人
                                issue.setCcGroupUser(serviceStaffGetResponse.getMailCC());
                                if (staffUserInfo != null) {
                                    issue.setServiceStaff(staffUserInfo.getUserId());
                                    issue.setDepartment(staffUserInfo.getDepartment());
                                    issue.setWorkNo(staffUserInfo.getWorkNo());
                                    //重新设置客户群抄收人
                                    if ("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode()) || hasTipTopProductCode) {
                                        issue.setCcGroupUser(staffUserInfo.getService_cc_staff_emails());
                                    }
                                }
                                //设置是否没有责任人员
                                issue.setNoResponsiblePersion(serviceStaffGetResponse.isNoResponsiblePerson());
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            } else {
                issue.setServiceStaff(issue.getServiceId());
            }


            //3.3 更新产品版本
            try {
                if (issue.getProductVersion() == null || issue.getProductVersion().isEmpty()) {
                    issueDao.UpdateIssueProductVersion(issue);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

//            if (staffUserInfo.getUserId().isEmpty()){
//
//            }else {
            //组织crmId
            String crmId;
            String warningId = Optional.ofNullable(issue.getWarningId()).orElse("");
            String sourceType = Optional.ofNullable(issue.getSourceType()).orElse("");
            if (issue.getSubmitWay().startsWith("SIM_") || issue.getSubmitWay().startsWith("EASY_TALK")) {
                crmId = IssueCodeRoleStart.ESM.toString() + String.format("%09d", issueId);
            } else if (issue.getSubmitWay().startsWith("CTI_Phone")) {
                //電話立案
                crmId = IssueCodeRoleStart.EST.toString() + String.format("%09d", issueId);
            } else {
                if (warningId.length() > 0) {
                    if (issue.getSubmitWay().equals(IssueSubmitMode.ITMS.toString())) {
                        //科維预警立案(客户)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    } else if (issue.getSubmitWay().equals(IssueSubmitMode.ITMS_Service.toString())) {
                        //科維预警立案(客服)
                        crmId = IssueCodeRoleStart.ESD.toString() + String.format("%09d", issueId);
                    } else if (issue.getSubmitWay().startsWith(IssueSubmitMode.AIOSSM.toString())) {
                        //企業運維立案(客服)
                        crmId = IssueCodeRoleStart.ESD.toString() + String.format("%09d", issueId);
                    } else if (issue.getSubmitWay().equals(IssueSubmitMode.AIEOM.toString())) {
                        //企業運維立案(客戶)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    } else {
                        //科維预警立案(客户)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    }
                } else if ("EDREvent".equals(sourceType)) {
                    if (issue.getSubmitWay().startsWith(IssueSubmitMode.AIOSSM.toString())) {
                        //企業運維立案(客服)
                        crmId = IssueCodeRoleStart.ESD.toString() + String.format("%09d", issueId);
                    } else if (issue.getSubmitWay().equals(IssueSubmitMode.AIEOM.toString())) {
                        //企業運維立案(客戶)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    } else {
                        //科維预警立案(客户)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    }
                } else {
                    if (issue.getIssueSubmitMode() != null) {
                        if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString()))
                            crmId = IssueCodeRoleStart.MSG.toString() + String.format("%09d", issueId);
                        else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString()))
                            crmId = IssueCodeRoleStart.CON.toString() + String.format("%09d", issueId);
                        else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.Chat.toString()))
                            crmId = IssueCodeRoleStart.EM.toString() + String.format("%010d", issueId);
                        else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CCInEasyTalk.toString()))
                            crmId = IssueCodeRoleStart.EMC.toString() + String.format("%09d", issueId);
                        else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.SHIPMENTAUTHORIZE.toString()))
                            crmId = IssueCodeRoleStart.ESN.toString() + String.format("%09d", issueId);
                        /*else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.AIOSSM.toString()))
                            crmId = IssueCodeRoleStart.EMC.toString() + String.format("%09d", issueId);
                        else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.AIEOM.toString()))
                            crmId = IssueCodeRoleStart.EMC.toString() + String.format("%09d", issueId);*/
                        else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.ATHENA.toString()))
                            crmId = IssueCodeRoleStart.EA.toString() + String.format("%010d", issueId);
                        else
                            crmId = "ES" + String.format("%010d", issueId);
                    } else
                        crmId = "ES" + String.format("%010d", issueId);
                }

            }

            issue.setCrmId(crmId);
            issueDao.UpdateIssue(issueId, crmId, issue.getServiceStaff(), issue.getDepartment(), issue.getIssueStatus(), userContactId);
            //不在这里调整案件同步状态，避免案件还未完善就被同步

            //如果有預警編號,  則更新預警編號的狀態
            if (!IssueSubmitMode.ATHENA.toString().equals(issue.getIssueSubmitMode())) {
                this.updateWarningNoticeStatus(issue);
            }
            issueDao.updateIssueSourceMap(issue.getIssueId(), issue.getCrmId());
            // 如果sourceType為EDREvent表示為事件案件，則更新是件編號的狀態
            if ("EDREvent".equals(issue.getSourceType())) {
                this.insertEventNoticeStatus(issue);
            }

            System.out.print("update EsClientService Warning Notice status:" + issueId);

            //SendNoticeToCC(issue);
            String syncStatus = "";
            if ("888".equals(issue.getProductCode()) || "147".equals(issue.getProductCode()) || "161".equals(issue.getProductCode()) || "157".equals(issue.getProductCode())) {
                syncStatus = SyncStatus.DontNeedSync.toString();
            }

            if (isConfirm) {
                // 查询审核人
                String confirmIds = GetCustomerIssueConfirmer(issue);
                SaveIssueCreateLog(issueId, crmId, issue.getUserId(), issue.getSubmitTime(), confirmIds, issue.getIssueStatus(), syncStatus, issue.getServiceRegion());
            } else {
                SaveIssueCreateLog(issueId, crmId, issue.getUserId(), issue.getSubmitTime(), issue.getServiceStaff(), issue.getIssueStatus(), syncStatus, issue.getServiceRegion());
            }

            dealIssueKbShare(issue);
//            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    @Override
    public void UpdateSyncStatusByIssueId(long issueId, String syncStatus) {
        issueDao.UpdateSyncStatusByIssueId(issueId, syncStatus);
    }

    public void UpdateProcessSyncStatusByIssueId(long issueId, String syncStatus) {
        issueDao.UpdateProcessSyncStatusByIssueId(issueId, syncStatus);
    }

    /**
     * 发送邮件和通知给审核人
     *
     * @param issue
     */
    private void SendNoticeToConfirmer(Issue issue) {
        try {
            String confirmIds = GetCustomerIssueConfirmer(issue);

            if (StringUtils.isEmpty(confirmIds)) {
                return;
            }
            String[] mx = {PrepareIssueMailToConfirmer(issue, confirmIds)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 发送邮件和通知给客服CC。抄送客户
     *
     * @param issue
     */
    private void SendNoticeToCCANDCustomer(Issue issue) {
        try {
            String[] mx = {PrepareIssueMailToCCANDCustomer(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 发送邮件和通知给客服CC
     *
     * @param issue
     */
    private void SendNoticeToCC(Issue issue) {
        try {
            String[] mx = {PrepareIssueMailToCC(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 代理商：发送邮件和通知给客服CC
     *
     * @param issue
     */
    private void SendAgentNoticeToCC(Issue issue, AgentIssueSum sum, CustomerServiceInfo customerServiceInfo) {
        try {
            String[] mx = {PrepareAgentIssueMailToCC(issue, sum, customerServiceInfo)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    private String GetServiceID(long issueId) {
        try {
            Issue issue;
            issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId), "","");
            return issue.getServiceId();
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
            return "";
        }
    }

    private List<String> GetAdditionalExplanationReceivers(long issueId) {
        try {
            List<String> receivers = new ArrayList<String>();
            receivers = issueDao.SelectEmails(issueId);
            return receivers;
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
            return null;
        }
    }

    private List<String> GetAdditionalExplanationReceiversBySelfUpdateSystem(long issueId) {
        try {
            List<String> receivers = new ArrayList<String>();
            receivers = issueDao.SelectEmailsBySelfUpdateSystem(issueId);
            return receivers;
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
            return null;
        }
    }

    public int SendAdditionalExplanationBySelfUpdateSystem(Issue issue, long isusfId, String dt, String issueStatus, String updateType, String description, int typeId) {
        int res = 0;
        try {
            String[] mx = {PrepareAdditionalExplanationMailBySelfUpdateSystem(issue, isusfId, dt, issueStatus, updateType, description, typeId)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            res = 1;
            ex.printStackTrace();
            log.error(ex);
            return res;
        }
        return res;
    }

    /**
     * 发送案件补充说明邮件和通知给客服CC
     *
     * @param issue
     */
    @Override
    public int SendAdditionalExplanation(Issue issue, String dt) {
        int res = 0;
        try {
            String[] mx = {PrepareAdditionalExplanationMailToCC(issue, dt)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            res = 1;
            ex.printStackTrace();
            log.error(ex);
            return res;
        }
        return res;
    }

    public int SendAdditionalExplanationToUser(Issue issue, String dt) {
        int res = 0;
        try {
            String[] mx = {PrepareAdditionalExplanationMailToUser(issue, dt)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            res = 1;
            ex.printStackTrace();
            log.error(ex);
            return res;
        }
        return res;
    }

    /**
     * 保存案件提交进度
     *
     * @param issueId
     * @param userId
     * @param date
     */
    private void SaveIssueCreateLog(long issueId, String crmId, String userId, String date, String serviceStaff, String issueStatus, String syncStatus, String serviceRegion) {
        try {
            IssueProgress issueProgress = new IssueProgress();
            issueProgress.setProcessType(IssueProcessType.Submit.toString());
            issueProgress.setProcessor(userId);
            issueProgress.setDescription("Submit Issue");
            if ("CN".equals(serviceRegion)) {
                issueProgress.setDescription("提交问题");
            }
            issueProgress.setReplyType(ReplyType.Q.toString());
            issueProgress.setProcessTime(date);

            //云管家打通T服务云，需要存下面3个字段
            issueProgress.setCurrentStatus(issueStatus);
            issueProgress.setHandlerId(serviceStaff);
            issueProgress.setWorkno("");
            issueProgress.setSyncStatus(syncStatus);
            issueDao.InsertIssueProgress(issueId, crmId, issueProgress);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 保存案件审核进度
     *
     * @param issueId
     * @param userId
     * @param date
     */
    private void SaveIssueConfirmLog(long issueId, String userId, String date) {
        try {
            IssueProgress issueProgress = new IssueProgress();
            issueProgress.setProcessType(IssueProcessType.Confirm.toString());
            issueProgress.setProcessor(userId);
            issueProgress.setDescription("Confirm Issue");
            issueProgress.setProcessTime(date);
            issueDao.InsertIssueProgress(issueId, "", issueProgress);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 保存案件附件
     *
     * @param issueId
     * @param issue
     */
    //@Async
    private void SaveAttachments(long issueId, Issue issue, boolean isContainFileName) {
        try {
            StringBuilder sbLog = new StringBuilder();
            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
                StringBuilder sbLog2 = new StringBuilder();
                for (IssueAttachment att : issue.getIssueAttachments()) {
                    //DgwFileUtils.writeToFile(att.getAttachment(),attachmentfilePathPrefix, "issue__" + issue.getIssueId()+ "_" + Integer.toString(att.getSequeceNum()) + att.getFileType());
                    int tryCount = 3;
                    sbLog.setLength(0);
                    GridFSFile gridFSFile = null;
                    Object id = null;
                    long chunkSize = -1;
                    int sequeceNum = att.getSequeceNum() + 1;
                    String fileType = att.getFileType();
                    String fileName = "issue__" + issueId + "__" + Integer.toString(sequeceNum) + fileType;
                    if (!StringUtils.isEmpty(att.getFileName())) {
                        if (att.getFileName().lastIndexOf(".") > 0) {
                            fileName = "issue__" + issueId + "__" + att.getFileName();
                        } else {
                            fileName = "issue__" + issueId + "__" + att.getFileName() + fileType;
                        }
                    }
                    sbLog.append("issueId:");
                    sbLog.append(Long.toString(issueId));
                    sbLog.append(" sequenceNum:");
                    sbLog.append(Long.toString(sequeceNum));
                    sbLog.append(" do save attachment fileName:");
                    sbLog.append(fileName);
                    sbLog.append(" fileType:");
                    sbLog.append(fileType);
                    do {
                        sbLog2.setLength(0);
                        try {
                            InputStream inputStream = new ByteArrayInputStream(att.getAttachment());
                            DBObject metaData = new BasicDBObject();
                            metaData.put("issueId", issueId);
                            metaData.put("sequeceNum", sequeceNum);
                            metaData.put("fileType", fileType);
                            gridFSFile = gridFsTemplate.store(inputStream, fileName, fileType, metaData);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            log.error(ex);
                        }
                        if (gridFSFile != null) {
                            sbLog2.append(" chunkSize:");
                            sbLog2.append((chunkSize = gridFSFile.getChunkSize()));
                            sbLog2.append(" length:");
                            sbLog2.append(gridFSFile.getLength());
                            sbLog2.append(" fileId:");
                            sbLog2.append((id = gridFSFile.getId()));

                            //存到mysql
                            IssueAttachmentFileV3 issueAttachmentFileV3 = new IssueAttachmentFileV3();
                            issueAttachmentFileV3.setIssueId(issueId);
                            issueAttachmentFileV3.setProgressId(0L);
                            issueAttachmentFileV3.setFileId(gridFSFile.getId().toString());
                            issueAttachmentFileV3.setFileName(fileName);
                            issueAttachmentFileV3.setFileType(fileType);
                            issueAttachmentFileV3.setUrl(issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + issueAttachmentFileV3.getFileId());

                            issueDao.saveAttachmentFile(issueAttachmentFileV3);
                        }
                        sbLog2.append(" tryCount:");
                        sbLog2.append(Integer.toString(tryCount));
                        System.out.print(sbLog.toString() + sbLog2.toString() + "\n");
                        tryCount--;
                        //gridFsTemplate.findOne(Query.query(Criteria.where("_id").is(fileId))))
                    } while ((gridFSFile == null || id == null || chunkSize == 0) && tryCount > 0);
                    if (tryCount < 0) {
                        System.out.print("Save attachment fail!");
                    }
                }
            } else {
                sbLog.append("issueId:");
                sbLog.append(Long.toString(issueId));
                sbLog.append(" not attachment!");
                sbLog.append("\n");
                System.out.print(sbLog.toString());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 保存案件补充说明附件
     *
     * @param issueId
     * @param issue
     */
    //@Async
    private void SaveAdditionalExplanationAttachments(long issueId, long progressId, Issue issue, String dt) {
        try {
            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
                for (IssueAttachment att : issue.getIssueAttachments()) {
                    int tryCount = 3;
                    GridFSFile gridFSFile = null;
                    Object id = null;
                    long chunkSize = -1;
                    int sequeceNum = att.getSequeceNum() + 1;
                    String fileType = att.getFileType();
                    //與客服聯繫附件加上單身id
                    String fileName = "issue__" + issueId + "__" + progressId + "__" + Integer.toString(sequeceNum) + fileType;
                    if (!StringUtils.isEmpty(att.getFileName())) {
                        if (att.getFileName().lastIndexOf(".") > 0) {
                            fileName = "issue__" + issueId + "__" + progressId + "__" + att.getFileName();
                        } else {
                            fileName = "issue__" + issueId + "__" + progressId + "__" + att.getFileName() + fileType;
                        }
                    }
                    do {
                        try {
                            InputStream inputStream = new ByteArrayInputStream(att.getAttachment());
                            DBObject metaData = new BasicDBObject();
                            metaData.put("issueId", issueId);
                            metaData.put("sequeceNum", sequeceNum);
                            metaData.put("fileType", fileType);
                            gridFSFile = gridFsTemplate.store(inputStream, fileName, fileType, metaData);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            log.error(ex);
                        }
                        if (gridFSFile != null) {
                            chunkSize = gridFSFile.getChunkSize();
                            id = gridFSFile.getId();
                            //存到mysql
                            IssueAttachmentFileV3 issueAttachmentFileV3 = new IssueAttachmentFileV3();
                            issueAttachmentFileV3.setIssueId(issueId);
                            issueAttachmentFileV3.setProgressId(progressId);
                            issueAttachmentFileV3.setFileId(gridFSFile.getId().toString());
                            issueAttachmentFileV3.setFileName(fileName);
                            issueAttachmentFileV3.setFileType(fileType);
                            issueAttachmentFileV3.setUrl(issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + issueAttachmentFileV3.getFileId());

                            issueDao.saveAttachmentFile(issueAttachmentFileV3);
                        }
                        tryCount--;
                    } while ((gridFSFile == null || id == null || chunkSize == 0) && tryCount > 0);
                    if (tryCount < 0) {
                        System.out.print("Save attachment fail!");
                    }

                }

            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 组织发送给案件提交人的邮件,走审核时，发送给客户的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMailByConfirm(Issue issue, boolean isConfirm) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(issue.getUserContact().getUserId());//(issue.getUserId());
            if (userPersonalInfo != null && userPersonalInfo.getLanguage() != null && !userPersonalInfo.getLanguage().isEmpty()) {
                language = userPersonalInfo.getLanguage();
                userTimeZone = getTimeZoneMin(userPersonalInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
            String formatToSubmiterMailStr = commonMailService.readMailContent("issueMailCustomerSumbit.html", language);
//            String formatToSubmiterMailStr = ToSubmiterMailStr;
//            if (language.equals("zh-cn")) {
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
////                formatToSubmiterMailStr = ToSubmiterMailStr_TW;
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-THAI");
//            }

            Long issueId = issue.getIssueId();
            System.out.print("Before PrepareIssueMail.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }
            System.out.print("After PrepareIssueMail.GetIssueAttachmentFiles " + issueId);

//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToSubmiterMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone),
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription);

            //1. 获取客户mail
            //组织发送给提交者的邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(issue.getUserContact().getEmail());

            //add 添加额外的抄送人
            List<String> cc01 = new ArrayList<String>();
            if (issue.getCcUser() != null && !issue.getCcUser().trim().equals("")) {
                String ccUser = issue.getCcUser().trim();
                String[] list = ccUser.split(";");
                for (int i = 0; i < list.length; i++) {
                    cc01.add(list[i].trim());
                }
            }
            Mail mail01 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            log.info("邮件模板语言：" + languageStandard);
            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
                //【鼎捷服务云】您的问题已提交成功
                subject = messageUtils.get("IssueSubmitMailToSubmiter", languageStandard);
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToSubmiter", language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject("IssueSubmitMailToSubmiter", language, crmId, serviceCode + customerName, issueDescription, false);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        subject = mailUtils.getIssueMailSubject("CallMeIssueSubmitMailToSubmiter", language, crmId, serviceCode + customerName, issueDescription, false);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToSubmiter", language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            }
            //            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
//                //【鼎捷服务云】您的问题已提交成功
//                subject = EsCloudMailSubject.IssueSubmitMailToSubmiter.toString();
//                if (issue.getIssueSubmitMode()!=null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                    subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                }
//            } else if (connectArea.equals("TW")) {
//                //台湾区，要增加其他标题内容
//                subject = EsCloudMailSubject.IssueSubmitMailToSubmiter.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
//                if (issue.getIssueSubmitMode()!=null) {
//                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
//                        String issueCategoryDesc=IssueCategory.valueOf(issue.getIssueCategory()).getDescription();
//                        subject = EsCloudMailSubject.CallMeIssueSubmitMailToSubmiter.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
//                    }
//                    else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                        subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                    }
//                }
//            }
            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);
//            mail01.setMessage("您于" + issue.getSubmitTime() + "填写的问题已提交成功，客服人员会第一时间为您处理！");
//            if (language.equals("zh-TW")) {
//                mail01.setMessage("您於" + issue.getSubmitTime() + "填寫的問題已提交成功，客服人員會第一時間爲您處理！");
//            }

            mail01.setReceivers(receivers01);
            mail01.setCcs(cc01);
            mail01.setMailSourceType(MailSourceType.Issue);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(issue.getIssueId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);

        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给案件提交人的退回邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueBackMail(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(issue.getUserContact().getUserId());//(issue.getUserId());
            if (userPersonalInfo != null && userPersonalInfo.getLanguage() != null && !userPersonalInfo.getLanguage().isEmpty()) {
                language = userPersonalInfo.getLanguage();
                userTimeZone = getTimeZoneMin(userPersonalInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
            String attachementStr = "无";
            String formatToSubmiterMailStr = ToSubmiterBackMailStr;
            if (language.equals("zh-TW")) {
                attachementStr = "無";
                formatToSubmiterMailStr = ToSubmiterBackMailStr_TW;
            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMail.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }
            System.out.print("After PrepareIssueMail.GetIssueAttachmentFiles " + issueId);

//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToSubmiterMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone),
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription);

            //1. 获取客户mail
            //组织发送给提交者的邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(issue.getUserContact().getEmail());

            //add 添加额外的抄送人
            if (issue.getCcUser() != null && !issue.getCcUser().trim().equals("")) {
                String ccUser = issue.getCcUser().trim();
                String[] list = ccUser.split(";");
                for (int i = 0; i < list.length; i++) {
                    receivers01.add(list[i].trim());
                }
            }
            Mail mail01 = new Mail();
            String subject = "";
            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
                //【鼎捷服务云】您的问题已提交成功
                subject = EsCloudMailSubject.BACK_TO_RESPONSOR.toString();
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            } else if (connectArea.equals("TW")) {
                //台湾区，要增加其他标题内容
                subject = EsCloudMailSubject.BACK_TO_RESPONSOR.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        String issueCategoryDesc = IssueCategory.valueOf(issue.getIssueCategory()).getDescription();
                        subject = EsCloudMailSubject.CallMeIssueSubmitMailToSubmiter.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            }
            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);
//            mail01.setMessage("您于" + issue.getSubmitTime() + "填写的问题已提交成功，客服人员会第一时间为您处理！");
//            if (language.equals("zh-TW")) {
//                mail01.setMessage("您於" + issue.getSubmitTime() + "填寫的問題已提交成功，客服人員會第一時間爲您處理！");
//            }

            mail01.setReceivers(receivers01);

            mail01.setMailSourceType(MailSourceType.Issue);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(issue.getIssueId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);

        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给案件提交人的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMail(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(issue.getUserContact().getUserId());//(issue.getUserId());
            if (userPersonalInfo != null && userPersonalInfo.getLanguage() != null && !userPersonalInfo.getLanguage().isEmpty()) {
                language = userPersonalInfo.getLanguage();
                userTimeZone = getTimeZoneMin(userPersonalInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
            String formatToSubmiterMailStr = commonMailService.readMailContent("issueMailCustomerSumbit.html", language);
//            String formatToSubmiterMailStr = ToSubmiterMailStr;
//            if (language.equals("zh-cn")) {
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
//                //formatToSubmiterMailStr = ToSubmiterMailStr_TW;
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-TW");
//
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-THAI");
//            }


            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMail.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }
            System.out.print("After PrepareIssueMail.GetIssueAttachmentFiles " + issueId);

//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToSubmiterMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone),
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription);

            //1. 获取客户mail
            //组织发送给提交者的邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(issue.getUserContact().getEmail());

            //add 添加额外的抄送人
            if (issue.getCcUser() != null && !issue.getCcUser().trim().equals("")) {
                String ccUser = issue.getCcUser().trim();
                String[] list = ccUser.split(";");
                for (int i = 0; i < list.length; i++) {
                    receivers01.add(list[i].trim());
                }
            }
            Mail mail01 = new Mail();
            String subject = "";
            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
                //【鼎捷服务云】您的问题已提交成功
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToSubmiter", language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject("IssueSubmitMailToSubmiter", language, crmId, serviceCode + customerName, issueDescription, false);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        subject = mailUtils.getIssueMailSubject("CallMeIssueSubmitMailToSubmiter", language, crmId, serviceCode + customerName, issueDescription, false);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToSubmiter", language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            }
//            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
//                //【鼎捷服务云】您的问题已提交成功
//                subject = EsCloudMailSubject.IssueSubmitMailToSubmiter.toString();
//                if (issue.getIssueSubmitMode()!=null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                    subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                }
//            } else if (connectArea.equals("TW")) {
//                //台湾区，要增加其他标题内容
//                subject = EsCloudMailSubject.IssueSubmitMailToSubmiter.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
//                if (issue.getIssueSubmitMode()!=null) {
//                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
//                        String issueCategoryDesc=IssueCategory.valueOf(issue.getIssueCategory()).getDescription();
//                        subject = EsCloudMailSubject.CallMeIssueSubmitMailToSubmiter.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
//                    }
//                    else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                        subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                    }
//                }
//            }
            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);
//            mail01.setMessage("您于" + issue.getSubmitTime() + "填写的问题已提交成功，客服人员会第一时间为您处理！");
//            if (language.equals("zh-TW")) {
//                mail01.setMessage("您於" + issue.getSubmitTime() + "填寫的問題已提交成功，客服人員會第一時間爲您處理！");
//            }

            mail01.setReceivers(receivers01);

            mail01.setMailSourceType(MailSourceType.Issue);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(issue.getIssueId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);

        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给审核人的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMailToConfirmer(Issue issue, String confirmIds) {
        List<Mail> mails = new ArrayList<Mail>();
        try {

            //2. 获取mis的mail
            List<String> receivers02 = userService.GetCustomerIssueConfirmerEmail(confirmIds);

            System.out.print("confirmer email: " + receivers02);
            if (CollectionUtils.isEmpty(receivers02)) {
                return "";
            }


            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            boolean isTWArea = connectArea != null && connectArea.equals("TW");

            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
            String attachementStr = "无";
            String formatToCCMailStr = ToSubmiterMailStrByConfirm;
            if (language.equals("zh-TW")) {
                attachementStr = "無";
                formatToCCMailStr = ToSubmiterMailStrByConfirm_TW;
            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            boolean hasAttachment = false;
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                hasAttachment = true;
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
//            String mailMsg = String.format(ToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
//            String mailMsg = String.format(formatToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, issue.getSubmitTime(),
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription);

            Mail mail02 = new Mail();
            String subject = "";
            //台湾区，要增加其他标题内容
            if (isTWArea) {
                subject = EsCloudMailSubject.IssueSubmitMailToConfirmer.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        String issueCategoryDesc = IssueCategory.valueOf(issue.getIssueCategory()).getDescription();
                        subject = EsCloudMailSubject.CallMeIssueSubmitMailToCC.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = EsCloudMailSubject.CouponIssueSubmitMailToCC.getCouponIssueMailSubject(language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            } else {
                //【鼎捷服务云】您有新案件
                subject = EsCloudMailSubject.IssueSubmitMailToConfirmer.getIssueMailSubject(language, hasAttachment);
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = EsCloudMailSubject.CouponIssueSubmitMailToCC.getCouponIssueMailSubject(language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            }
            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 发送邮件给客服人员 抄送给其他客服、客户、CC
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMailToCustomerService_T(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceStaff());//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());
            System.out.print("In PrepareIssueMailToCustomerService_T");
            System.out.print(staffUserInfo.getEmail());
            System.out.print("In Staff TimeZone");
            System.out.print(staffUserInfo.getTimeZone());


            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0; //默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
//            String formatToCCMailStr = ToCCMailStr;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNewT.html", language);
//            if (language.equals("zh-CN")) {
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
////                formatToCCMailStr = ToCCMailStr_TW;
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-THAI");
//            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToCustomerService_T.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            boolean hasAttachment = false;
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                hasAttachment = true;
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToCustomerService_T.GetIssueAttachmentFiles " + issueId);
//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
//            String mailMsg = String.format(ToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
//            String mailMsg = String.format(formatToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 19),
                    userContact.getName(), userContact.getEmail(), phone, issue.getCcUser(), attachementStr,
                    issueDescription);

            Mail mail02 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(MessageUtils.ZH_CN_STANDARD, languageStandard)) {
                //【鼎捷服务云】您有新案件  标题调整，如果是紧急案件，需要改成【鼎捷服务云】【紧急】您有新案件
                subject = mailUtils.getIssueMailSubject(issue.isEmergency() ? "IssueSubmitEmergencyMailToCC" : "IssueSubmitMailToCC", language, hasAttachment);
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = mailUtils.getCouponIssueMailSubject(issue.isEmergency() ? "CouponIssueSubmitEmergencyMailToCC" : "CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject(issue.isEmergency() ? "IssueSubmitEmergencyMailToCC" : "IssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        subject = mailUtils.getIssueMailSubject(issue.isEmergency() ? "CallMeIssueSubmitEmergencyMailToCC" : "CallMeIssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = mailUtils.getCouponIssueMailSubject(issue.isEmergency() ? "CouponIssueSubmitEmergencyMailToCC" : "CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            }


            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            //t产品线增加邮件抄送人
            //設置用戶群抄收人
            if ("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())) {
                HashSet<String> ccs = new HashSet<>();
                //T的其他顺序的客服要作为抄送人
                if (!StringUtils.isEmpty(issue.getCcGroupUser())) {
                    String[] ccsArray = issue.getCcGroupUser().split(";");
                    if (ccsArray != null) {
                        for (String ccUser : ccsArray) {
                            ccs.add(ccUser);
                        }
                    }
                }
                //T的案件需要增加CC的人作为抄送人
                if (!StringUtils.isEmpty(issue.getCcUser())) {
                    String[] ccsArray = issue.getCcUser().split(";");
                    if (ccsArray != null) {
                        for (String ccUser : ccsArray) {
                            ccs.add(ccUser);
                        }
                    }
                }
                //T的案件需要增加客户作为抄送人
                if (!StringUtils.isEmpty(userContact.getEmail())) {
                    ccs.add(userContact.getEmail());
                }
                List<String> ccMailList = new ArrayList<>(ccs);
                mail02.setCcs(ccMailList);
            }

            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给客服CC的邮件,抄送给客户，审核后的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMailToCCANDCustomer(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceStaff());//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());
            System.out.print("In PrepareIssueMailToCC");
            System.out.print(staffUserInfo.getEmail());
            System.out.print("In Staff TimeZone");
            System.out.print(staffUserInfo.getTimeZone());

            //設置用戶群抄收人
            if (!"100".equals(issue.getProductCode()) && !"06".equals(issue.getProductCode())) {
                String ccGroup = issue.getCcGroupUser();
                if (ccGroup != null && !(ccGroup = ccGroup.trim()).isEmpty()) {
                    String[] list = ccGroup.split(";");
                    for (int i = 0; i < list.length; i++) {
                        receivers02.add(list[i].trim());
                    }
                }
            }


            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0; //默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
//            String formatToCCMailStr = ToCCMailStr;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html", language);
//            if (language.equals("zh-CN")) {
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
////                formatToCCMailStr = ToCCMailStr_TW;
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-THAI");
//            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            boolean hasAttachment = false;
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                hasAttachment = true;
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
//            String mailMsg = String.format(ToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
//            String mailMsg = String.format(formatToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 19),
                    userContact.getName(), userContact.getEmail(), phone, StringUtils.isEmpty(issue.getCcUser()) ? "" : issue.getCcUser(), attachementStr,
                    issueDescription);

            Mail mail02 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(MessageUtils.ZH_CN_STANDARD, languageStandard)) {
                //【鼎捷服务云】您有新案件
                subject = mailUtils.getIssueMailSubject("IssueSubmitMailToCC", language, hasAttachment);
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject("IssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        subject = mailUtils.getIssueMailSubject("CallMeIssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            }

            //台湾区，要增加其他标题内容
//            if (isTWArea) {
//                subject = EsCloudMailSubject.IssueSubmitMailToCC.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
//                if (issue.getIssueSubmitMode()!=null) {
//                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
//                        String issueCategoryDesc=IssueCategory.valueOf(issue.getIssueCategory()).getDescription();
//                        subject = EsCloudMailSubject.CallMeIssueSubmitMailToCC.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
//                    }
//                    else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                        subject = EsCloudMailSubject.CouponIssueSubmitMailToCC.getCouponIssueMailSubject(language, serviceCode + customerName, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                    }
//                }
//            } else {
//                //【鼎捷服务云】您有新案件
//                subject = EsCloudMailSubject.IssueSubmitMailToCC.getIssueMailSubject(language, hasAttachment);
//                if (issue.getIssueSubmitMode()!=null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                    subject = EsCloudMailSubject.CouponIssueSubmitMailToCC.getCouponIssueMailSubject(language, serviceCode + customerName, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                }
//            }
            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            //t产品线增加邮件抄送人
            //設置用戶群抄收人
            HashSet<String> ccs = new HashSet<String>();
            if (issue.getUserContact() != null && !StringUtils.isEmpty(issue.getUserContact().getEmail())) {
                ccs.add(issue.getUserContact().getEmail());
            }
            if ("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())) {
                if (!StringUtils.isEmpty(issue.getCcGroupUser())) {
                    String[] ccsArray = issue.getCcGroupUser().split(";");
                    if (ccsArray != null) {
                        for (String ccUser : ccsArray) {
                            ccs.add(ccUser);
                        }
                    }
                }
            }
            mail02.setCcs(new ArrayList<>(ccs));
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 代理商：需要发送给合约表的SalesCode、StaffServiceCode 以及StaffServiceCode 的主管、代理商设定的邮件抄送人员
     *
     * @param issue
     * @return
     */
    private String PrepareAgentIssueMailToCC(Issue issue, AgentIssueSum sum, CustomerServiceInfo customerServiceInfo) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //1. 获取客服mail
            List<String> receivers02 = new ArrayList<String>();
            String workNos = "";
            if (StringUtil.isNotEmpty(customerServiceInfo.getSalesCode())) {
                workNos = customerServiceInfo.getSalesCode();
            }
            if (StringUtil.isNotEmpty(workNos)) {
                if (StringUtil.isNotEmpty(customerServiceInfo.getServiceStaffCode())) {
                    workNos = workNos + "," + customerServiceInfo.getServiceStaffCode();

                    //获取客服的主管
                    String managerWorkNo = userDao.getManagerWorkNoByWorkNo(customerServiceInfo.getServiceStaffCode());
                    if (StringUtil.isNotEmpty(managerWorkNo)) {
                        workNos = workNos + "," + managerWorkNo;
                    }
                }
            } else {
                workNos = customerServiceInfo.getServiceStaffCode();
            }
            if (StringUtil.isNotEmpty(workNos)) {
                List<String> emails = userService.GetUserEmailList(workNos);
                for (String email : emails) {
                    if (StringUtil.isNotEmpty(email)) {
                        receivers02.add(email);
                    }
                }
            }
            //2获取代理商的邮件抄送人
            List<String> ccs = new ArrayList<String>();
            String emails = userDao.getAgentMail(issue.getProductCode());
            if (StringUtil.isNotEmpty(emails)) {
                String[] emailArr = emails.split(";");
                for (String email : emailArr) {
                    if (StringUtil.isNotEmpty(email) && !ccs.contains(email)) {
                        ccs.add(email);
                    }
                }
            }

            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0; //默认时区

            //组织发给客服专员的邮件
            String formatToCCMailStr = commonMailService.readMailContent("issueAgentMail.html", language);
            String mailMsg = String.format(formatToCCMailStr, sum.getAgentLimitIssueCount(), sum.getUsedCount(), issue.getServiceCode(), customerServiceInfo.getCustomerName(), customerServiceInfo.getProductName());

            Mail mail02 = new Mail();
            String subject = mailUtils.getIssueAgentMailSubject("IssueSubmitAgentMailToCC", language, issue.getServiceCode(), customerServiceInfo.getCustomerName(), customerServiceInfo.getProductName());
            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            mail02.setCcs(ccs);
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给客服CC的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMailToCC(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceStaff());//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());
            System.out.print("In PrepareIssueMailToCC");
            System.out.print(staffUserInfo.getEmail());
            System.out.print("In Staff TimeZone");
            System.out.print(staffUserInfo.getTimeZone());

            //設置用戶群抄收人
            if (!"100".equals(issue.getProductCode()) && !"06".equals(issue.getProductCode())) {
                String ccGroup = issue.getCcGroupUser();
                if (ccGroup != null && !(ccGroup = ccGroup.trim()).isEmpty()) {
                    String[] list = ccGroup.split(";");
                    for (int i = 0; i < list.length; i++) {
                        receivers02.add(list[i].trim());
                    }
                }
            }


            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0; //默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
//            String formatToCCMailStr = ToCCMailStr;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html", language);

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            boolean hasAttachment = false;
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                hasAttachment = true;
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 19),
                    userContact.getName(), userContact.getEmail(), phone, StringUtils.isEmpty(issue.getCcUser()) ? "" : issue.getCcUser(), attachementStr,
                    issueDescription);

            Mail mail02 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(MessageUtils.ZH_CN_STANDARD, languageStandard)) {
                //【鼎捷服务云】您有新案件  标题调整，如果是紧急案件，需要改成【鼎捷服务云】【紧急】您有新案件
                subject = mailUtils.getIssueMailSubject(issue.isEmergency() ? "IssueSubmitEmergencyMailToCC" : "IssueSubmitMailToCC", language, hasAttachment);
                if ("147".equals(issue.getProductCode()) || "163".equals(issue.getProductCode()) || "15".equals(issue.getProductCode())) {
                    if (customerServiceInfo != null) {
                        if (customerServiceInfo.isTrial()) {
                            //試用客戶
                            subject = "【试用客户】" + subject;
                        }
                    }
                }
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = mailUtils.getCouponIssueMailSubject(issue.isEmergency() ? "CouponIssueSubmitEmergencyMailToCC" : "CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject(issue.isEmergency() ? "IssueSubmitEmergencyMailToCC" : "IssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                if ("147".equals(issue.getProductCode()) || "163".equals(issue.getProductCode()) || "15".equals(issue.getProductCode())) {
                    if (customerServiceInfo != null) {
                        if (customerServiceInfo.isTrial()) {
                            //試用客戶
                            subject = "【試用客戶】" + subject;
                        }
                    }
                }
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        subject = mailUtils.getIssueMailSubject(issue.isEmergency() ? "CallMeIssueSubmitEmergencyMailToCC" : "CallMeIssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = mailUtils.getCouponIssueMailSubject(issue.isEmergency() ? "CouponIssueSubmitEmergencyMailToCC" : "CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            }

            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            //t产品线增加邮件抄送人
            //設置用戶群抄收人
            HashSet<String> ccs = new HashSet<String>();
            if ("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())) {
                if (!StringUtils.isEmpty(issue.getCcGroupUser())) {
                    String[] ccsArray = issue.getCcGroupUser().split(";");
                    if (ccsArray != null) {
                        for (String ccUser : ccsArray) {
                            ccs.add(ccUser);
                        }
                    }
                }
            }

            mail02.setCcs(new ArrayList<>(ccs));
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    private String PrepareAdditionalExplanationMailBySelfUpdateSystem(Issue issue, long isusfId, String dt, String issueStatus, String updateType, String description, int typeId) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            List<String> receivers = new ArrayList<>();
            if (TypeName.SFT_APPLY_SERIAL_RE_CHECK.getStatus() == typeId) {
                receivers = userDao.getMaderMailByIsusfId(isusfId);
            } else {
                receivers = GetAdditionalExplanationReceiversBySelfUpdateSystem(issue.getIssueId());

                //1 获取 秘书，业务 ，制作人邮箱
                if (IssueStatus.Closed.toString().equals(issueStatus)) { //如果结案，需要業務人員及祕書人員及出貨人員
                    List<String> receivers03 = userDao.getMailByIsusfId(isusfId);
                    for (String receiver : receivers03) {
                        if (!receivers.contains(receiver))
                            receivers.add(receiver);
                    }
                }
            }


            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0;
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceId());
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }

            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            // 获取案件处理过程
            String progressHtml = "";
            HashMap<String, String> Map_ProgressType = new HashMap<String, String>();
            progressHtml = "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:730px;text-indent: 0em;font-size: 14px;\">\n" +
                    "\t\t\t\t<tr>\n" +
                    "\t\t\t\t\t<td width=80 height=40 style=\"padding-left:10px\"><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=50><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=50><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=120><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=180><b>%s</b></td>\n" +
                    "\t\t\t\t<tr>\n";
            String processDate = messageUtils.get("ProcessDate", language);
            String number = messageUtils.get("Number", language);
            String closeIssue = messageUtils.get("CloseIssue", language);
            String processingState = messageUtils.get("ProcessingState", language);
            String processingDescription = messageUtils.get("ProcessingDescription", language);
            progressHtml = String.format(progressHtml, processDate, number, closeIssue, processingState, processingDescription);
            String issueSubmit = messageUtils.get("IssueSubmit", language);
            String issueConfirm = messageUtils.get("IssueConfirm", language);
            String issueSendBack = messageUtils.get("IssueSendBack", language);
            String issueProcess = messageUtils.get("IssueProcess", language);
            String issueClose = messageUtils.get("IssueClose", language);
            Map_ProgressType.put("Submit", issueSubmit);
            Map_ProgressType.put("Confirm", issueConfirm);
            Map_ProgressType.put("SendBack", issueSendBack);
            Map_ProgressType.put("Process", issueProcess);
            Map_ProgressType.put("Close", issueClose);

            List<IssueProgress> issueProgress = issue.getIssueProgresses();
            String processColsed = "N";
            for (IssueProgress progress : issueProgress) {
                String processIsColsed = "";
                String processType = "";
                if ((progress.getProcessType().equals("Close")) || (progress.getProcessType().equals("Process"))) {
                    processIsColsed = progress.getProcessType().equals("Close") ? "Y" : "N";
                    if (processIsColsed.equals("Y")) {
                        processColsed = "Y";
                    }
                    processType = Map_ProgressType.get(progress.getProcessType());
                    progressHtml += "\t\t\t\t<tr>\n" +
                            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\">" + progress.getProcessTime().substring(0, 10) + "</td>\n" +
                            "\t\t\t\t\t<td width=50>" + Integer.toString(progress.getSequeceNum()) + "</td>\n" +
                            "\t\t\t\t\t<td width=50>" + processIsColsed + "</td>\n" +
                            "\t\t\t\t\t<td width=120>" + processType + "</td>\n" +
                            "\t\t\t\t\t<td style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:180;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">" + progress.getDescription() + "</pre></td>\n" +
                            "\t\t\t\t<tr>\n";
                }
            }
            processColsed = "Y".equals(issueStatus) ? "Y" : "N";
            progressHtml += "\t\t\t</table>";

            String attachementStr = messageUtils.get("nothing", language);
            String formatToCCMailStr = commonMailService.readMailContent("issueMailSelfUpdateSystem.html", language);

            //組織案件附件超鏈接
            boolean hasAttachment = false;
            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
                List<IssueAttachmentFile> attachFiles = GetIssueAdditionalExplanationAttachmentFiles(Long.toString(issue.getIssueId()), dt);
                if (attachFiles != null && attachFiles.size() > 0) {
                    hasAttachment = true;
                    attachementStr = "";
                    for (IssueAttachmentFile file : attachFiles) {
                        attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                    }
                }
            }
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerName,
                    crmId, processColsed, issue.getUserContact().getName(), issue.getServiceStaff(), transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 10),
                    issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), issue.getUserContact().getPhone02(), issueDescription,
                    attachementStr, progressHtml, description
            );

            Mail mail03 = new Mail();
            String subject = "";
            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
                subject = mailUtils.getIssueMailSubject("IssueProjectUpdateMailToCC", language, hasAttachment);
                if (UpdateType.AUTHINSTALL.toString().equals(updateType) || UpdateType.SFT_AUTHINSTALL.toString().equals(updateType) || UpdateType.SFT_RE_AUTHINSTALL.toString().equals(updateType)) {
                    subject = mailUtils.getIssueMailSubject("IssueSerialNumberupdateMailToCC", language, hasAttachment);
                }
            } else {
                //台湾区，要增加其他标题内容
                if (issue.getRequestCloseIssue()) {
                    subject = mailUtils.getRequestCloseIssueMailSubject("IssueProjectUpdateMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment, issue.getRequestCloseIssue());
                    if (UpdateType.AUTHINSTALL.toString().equals(updateType) || UpdateType.SFT_AUTHINSTALL.toString().equals(updateType) || UpdateType.SFT_RE_AUTHINSTALL.toString().equals(updateType)) {
                        subject = mailUtils.getRequestCloseIssueMailSubject("IssueSerialNumberupdateMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment, issue.getRequestCloseIssue());
                    }
                } else {
                    subject = mailUtils.getIssueMailSubject("IssueProjectUpdateMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                    if (UpdateType.AUTHINSTALL.toString().equals(updateType) || UpdateType.SFT_AUTHINSTALL.toString().equals(updateType) || UpdateType.SFT_RE_AUTHINSTALL.toString().equals(updateType)) {
                        subject = mailUtils.getIssueMailSubject("IssueSerialNumberupdateMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                    }
                }
            }
            //申请序号重查
            if (TypeName.SFT_APPLY_SERIAL_RE_CHECK.getStatus() == typeId) {
                subject = mailUtils.getIssueMailSubject("IssueApplySerialReCheckMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
            }
            mail03.setSubject(subject);
            mail03.setMessage(mailMsg);
            mail03.setReceivers(receivers);
            mail03.setMailSourceType(MailSourceType.Issue);
            mail03.setUrl("");
            mail03.setSourceId(Long.toString(issue.getIssueId()));
            mail03.setPriority(1);
            mail03.setLanguage(language);
            mails.add(mail03);
            log.debug(JSON.toJSONString(mail03));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给客服CC的補充說明邮件
     *
     * @param issue
     * @return
     */
    private String PrepareAdditionalExplanationMailToCC(Issue issue, String dt) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            String serviceId = GetServiceID(issue.getIssueId());
            List<String> receivers03 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(serviceId);//(issue.getUserId());
            receivers03.add(staffUserInfo.getEmail());

            //設置用戶群抄收人
//            String ccGroup = issue.getCcGroupUser();
//            if (ccGroup != null && !(ccGroup = ccGroup.trim()).isEmpty()) {
//                String[] list = ccGroup.split(";");
//                for (int i = 0; i < list.length; i++) {
//                    receivers03.add(list[i].trim());
//                }
//            }
            List<String> receivers = new ArrayList<String>();
            receivers = GetAdditionalExplanationReceivers(issue.getIssueId());
            for (String receiver : receivers) {
                if (!receiver.equals(staffUserInfo.getEmail()))
                    receivers03.add(receiver);
            }
            //检查代理人
            StaffUserInfo staff = userService.selectServiceStaffAgent(serviceId);
            if (staff != null && !StringUtils.isEmpty(staff.getUserId()) && !StringUtils.isEmpty(staff.getEmail())) {
                receivers03.add(staff.getEmail());
            }

            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0;
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            // 获取案件处理过程
            String progressHtml = "";
            HashMap<String, String> Map_ProgressType = new HashMap<String, String>();
            progressHtml = "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:730px;text-indent: 0em;font-size: 14px;\">\n" +
                    "\t\t\t\t<tr>\n" +
                    "\t\t\t\t\t<td width=80 height=40 style=\"padding-left:10px\"><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=50><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=50><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=120><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=180><b>%s</b></td>\n" +
                    "\t\t\t\t<tr>\n";
            String processDate = messageUtils.get("ProcessDate", language);
            String number = messageUtils.get("Number", language);
            String closeIssue = messageUtils.get("CloseIssue", language);
            String processingState = messageUtils.get("ProcessingState", language);
            String processingDescription = messageUtils.get("ProcessingDescription", language);
            progressHtml = String.format(progressHtml, processDate, number, closeIssue, processingState, processingDescription);
            String issueSubmit = messageUtils.get("IssueSubmit", language);
            String issueConfirm = messageUtils.get("IssueConfirm", language);
            String issueSendBack = messageUtils.get("IssueSendBack", language);
            String issueProcess = messageUtils.get("IssueProcess", language);
            String issueClose = messageUtils.get("IssueClose", language);
            Map_ProgressType.put("Submit", issueSubmit);
            Map_ProgressType.put("Confirm", issueConfirm);
            Map_ProgressType.put("SendBack", issueSendBack);
            Map_ProgressType.put("Process", issueProcess);
            Map_ProgressType.put("Close", issueClose);
//            if (language.equals("zh-TW")) {
//                progressHtml = "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:730px;text-indent: 0em;font-size: 14px;\">\n" +
//                        "\t\t\t\t<tr>\n" +
//                        "\t\t\t\t\t<td width=80 height=40 style=\"padding-left:10px\"><b>處理日期</b></td>\n" +
//                        "\t\t\t\t\t<td width=50><b>序號</b></td>\n" +
//                        "\t\t\t\t\t<td width=50><b>結案</b></td>\n" +
//                        "\t\t\t\t\t<td width=120><b>處理狀態</b></td>\n" +
//                        "\t\t\t\t\t<td width=180><b>處理描述</b></td>\n" +
//                        "\t\t\t\t<tr>\n";
//                Map_ProgressType.put("Submit", "提交問題");
//                Map_ProgressType.put("Confirm", "MIS審核");
//                Map_ProgressType.put("SendBack", "MIS退回");
//                Map_ProgressType.put("Process", "客服人員處理中");
//                Map_ProgressType.put("Close", "客服處理完畢");
//            } else {
//                progressHtml = "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:730px;text-indent: 0em;font-size: 14px;\">\n" +
//                        "\t\t\t\t<tr>\n" +
//                        "\t\t\t\t\t<td width=80 height=40 style=\"padding-left:10px\"><b>处理日期</b></td>\n" +
//                        "\t\t\t\t\t<td width=50><b>序号</b></td>\n" +
//                        "\t\t\t\t\t<td width=50><b>结案</b></td>\n" +
//                        "\t\t\t\t\t<td width=120><b>处理状态</b></td>\n" +
//                        "\t\t\t\t\t<td width=180><b>处理描述</b></td>\n" +
//                        "\t\t\t\t<tr>\n";
//                Map_ProgressType.put("Submit", "提交问题");
//                Map_ProgressType.put("Confirm", "MIS审核");
//                Map_ProgressType.put("SendBack", "MIS退回");
//                Map_ProgressType.put("Process", "客服人员处理中");
//                Map_ProgressType.put("Close", "客服处理完毕");
//            }

            List<IssueProgress> issueProgress = issue.getIssueProgresses();
            String processColsed = "N";
            for (IssueProgress progress : issueProgress) {
                String processIsColsed = "";
                String processType = "";
                if ((progress.getProcessType().equals("Close")) || (progress.getProcessType().equals("Process"))) {
                    processIsColsed = progress.getProcessType().equals("Close") ? "Y" : "N";
                    if (processIsColsed.equals("Y")) {
                        processColsed = "Y";
                    }
                    processType = Map_ProgressType.get(progress.getProcessType());
                    progressHtml += "\t\t\t\t<tr>\n" +
                            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\">" + progress.getProcessTime().substring(0, 10) + "</td>\n" +
                            "\t\t\t\t\t<td width=50>" + Integer.toString(progress.getSequeceNum()) + "</td>\n" +
                            "\t\t\t\t\t<td width=50>" + processIsColsed + "</td>\n" +
                            "\t\t\t\t\t<td width=120>" + processType + "</td>\n" +
                            "\t\t\t\t\t<td style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:180;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">" + progress.getDescription() + "</pre></td>\n" +
                            "\t\t\t\t<tr>\n";
                }
            }
            processColsed = issue.getIssueStatus().equals("Y") ? "Y" : "N";
            progressHtml += "\t\t\t</table>";

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
//            String formatToCCMailStr = ToCCMailAdditionalExplanation;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCallCenterScheduleQuery.html", language);
//            if (language.equals("zh-CN")) {
//                formatToCCMailStr = iMailService.readMailContent("issueMailCallCenterScheduleQuery.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
//                //  formatToCCMailStr = ToCCMailAdditionalExplanation_TW;
//                formatToCCMailStr = iMailService.readMailContent("issueMailCallCenterScheduleQuery.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToCCMailStr = iMailService.readMailContent("issueMailCallCenterScheduleQuery.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToCCMailStr = iMailService.readMailContent("issueMailCallCenterScheduleQuery.html","zh-THAI");
//            }


            //組織案件附件超鏈接
            boolean hasAttachment = false;
            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
                List<IssueAttachmentFile> attachFiles = GetIssueAdditionalExplanationAttachmentFiles(Long.toString(issue.getIssueId()), dt);
                if (attachFiles != null && attachFiles.size() > 0) {
                    hasAttachment = true;
                    attachementStr = "";
                    for (IssueAttachmentFile file : attachFiles) {
                        attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                    }
                }
            }
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerName,
                    crmId, processColsed, issue.getUserContact().getName(), issue.getServiceStaff(), transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 10),
                    issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), issue.getUserContact().getPhone02(), issueDescription,
                    attachementStr, progressHtml, issue.getIssueCasedetail().getAdditionalExplanation()
            );

            Mail mail03 = new Mail();
            String subject = "";
            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
                //【鼎捷服务云】Call-Center进度查询
                subject = mailUtils.getIssueMailSubject("IssueAdditionalExplanationSubmitMailToCC", language, hasAttachment);
            } else {
                //台湾区，要增加其他标题内容
                if (issue.getRequestCloseIssue()) {
                    subject = mailUtils.getRequestCloseIssueMailSubject("IssueAdditionalExplanationSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment, issue.getRequestCloseIssue());
                } else {
                    subject = mailUtils.getIssueMailSubject("IssueAdditionalExplanationSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                }

            }
//            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
//                //【鼎捷服务云】Call-Center进度查询
//                subject = EsCloudMailSubject.IssueAdditionalExplanationSubmitMailToCC.getIssueMailSubject(language, hasAttachment);
//            } else if (connectArea.equals("TW")) {
//                //台湾区，要增加其他标题内容
//                if (issue.getRequestCloseIssue())
//                    subject = EsCloudMailSubject.IssueAdditionalExplanationSubmitMailToCC.getRequestCloseIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment, issue.getRequestCloseIssue());
//                else
//                    subject = EsCloudMailSubject.IssueAdditionalExplanationSubmitMailToCC.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
//            }
            mail03.setSubject(subject);
            mail03.setMessage(mailMsg);
            mail03.setReceivers(receivers03);
            mail03.setMailSourceType(MailSourceType.Issue);
            mail03.setUrl("");
            mail03.setSourceId(Long.toString(issue.getIssueId()));
            mail03.setPriority(1);
            mail03.setLanguage(language);
            mails.add(mail03);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给客戶的補充說明邮件
     *
     * @param issue
     * @return
     */
    private String PrepareAdditionalExplanationMailToUser(Issue issue, String dt) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //1. 获取客户mail
            //组织发送给提交者的邮件
            List<String> receivers03 = new ArrayList<String>();
            receivers03.add(issue.getUserContact().getEmail());

            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0;
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(issue.getUserContact().getUserId());//(issue.getUserId());
            if (userPersonalInfo != null && userPersonalInfo.getLanguage() != null && !userPersonalInfo.getLanguage().isEmpty()) {
                language = userPersonalInfo.getLanguage();
                userTimeZone = getTimeZoneMin(userPersonalInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            // 获取案件处理过程
            String progressHtml = "";
            HashMap<String, String> Map_ProgressType = new HashMap<String, String>();
            String languageStandard = messageUtils.getLanguage(language);
            progressHtml = "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:730px;text-indent: 0em;font-size: 14px;\">\n" +
                    "\t\t\t\t<tr>\n" +
                    "\t\t\t\t\t<td width=80 height=40 style=\"padding-left:10px\"><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=50><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=50><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=120><b>%s</b></td>\n" +
                    "\t\t\t\t\t<td width=180><b>%s</b></td>\n" +
                    "\t\t\t\t<tr>\n";
            String processDate = messageUtils.get("ProcessDate", language);
            String number = messageUtils.get("Number", language);
            String closeIssue = messageUtils.get("CloseIssue", language);
            String processingState = messageUtils.get("ProcessingState", language);
            String processingDescription = messageUtils.get("ProcessingDescription", language);
            progressHtml = String.format(progressHtml, processDate, number, closeIssue, processingState, processingDescription);
            String issueSubmit = messageUtils.get("IssueSubmit", language);
            String issueConfirm = messageUtils.get("IssueConfirm", language);
            String issueSendBack = messageUtils.get("IssueSendBack", language);
            String issueProcess = messageUtils.get("IssueProcess", language);
            String issueClose = messageUtils.get("IssueClose", language);
            Map_ProgressType.put("Submit", issueSubmit);
            Map_ProgressType.put("Confirm", issueConfirm);
            Map_ProgressType.put("SendBack", issueSendBack);
            Map_ProgressType.put("Process", issueProcess);
            Map_ProgressType.put("Close", issueClose);
            //            if (language.equals("zh-TW")) {
//                progressHtml = "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:730px;text-indent: 0em;font-size: 14px;\">\n" +
//                        "\t\t\t\t<tr>\n" +
//                        "\t\t\t\t\t<td width=80 height=40 style=\"padding-left:10px\"><b>處理日期</b></td>\n" +
//                        "\t\t\t\t\t<td width=50><b>序號</b></td>\n" +
//                        "\t\t\t\t\t<td width=50><b>結案</b></td>\n" +
//                        "\t\t\t\t\t<td width=120><b>處理狀態</b></td>\n" +
//                        "\t\t\t\t\t<td width=180><b>處理描述</b></td>\n" +
//                        "\t\t\t\t<tr>\n";
//                Map_ProgressType.put("Submit", "提交問題");
//                Map_ProgressType.put("Confirm", "MIS審核");
//                Map_ProgressType.put("SendBack", "MIS退回");
//                Map_ProgressType.put("Process", "客服人員處理中");
//                Map_ProgressType.put("Close", "客服處理完畢");
//            } else {
//                progressHtml = "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:730px;text-indent: 0em;font-size: 14px;\">\n" +
//                        "\t\t\t\t<tr>\n" +
//                        "\t\t\t\t\t<td width=80 height=40 style=\"padding-left:10px\"><b>处理日期</b></td>\n" +
//                        "\t\t\t\t\t<td width=50><b>序号</b></td>\n" +
//                        "\t\t\t\t\t<td width=50><b>结案</b></td>\n" +
//                        "\t\t\t\t\t<td width=120><b>处理状态</b></td>\n" +
//                        "\t\t\t\t\t<td width=180><b>处理描述</b></td>\n" +
//                        "\t\t\t\t<tr>\n";
//                Map_ProgressType.put("Submit", "提交问题");
//                Map_ProgressType.put("Confirm", "MIS审核");
//                Map_ProgressType.put("SendBack", "MIS退回");
//                Map_ProgressType.put("Process", "客服人员处理中");
//                Map_ProgressType.put("Close", "客服处理完毕");
//            }

            List<IssueProgress> issueProgress = issue.getIssueProgresses();
            String processColsed = "N";
            for (IssueProgress progress : issueProgress) {
                String processIsColsed = "";
                String processType = "";
                if ((progress.getProcessType().equals("Close")) || (progress.getProcessType().equals("Process"))) {
                    processIsColsed = progress.getProcessType().equals("Close") ? "Y" : "N";
                    if (processIsColsed.equals("Y")) {
                        processColsed = "Y";
                    }
                    processType = Map_ProgressType.get(progress.getProcessType());
                    progressHtml += "\t\t\t\t<tr>\n" +
                            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\">" + progress.getProcessTime().substring(0, 10) + "</td>\n" +
                            "\t\t\t\t\t<td width=50>" + Integer.toString(progress.getSequeceNum()) + "</td>\n" +
                            "\t\t\t\t\t<td width=50>" + processIsColsed + "</td>\n" +
                            "\t\t\t\t\t<td width=120>" + processType + "</td>\n" +
                            "\t\t\t\t\t<td style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:180;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">" + progress.getDescription() + "</pre></td>\n" +
                            "\t\t\t\t<tr>\n";
                }
            }
            processColsed = issue.getIssueStatus().equals("Y") ? "Y" : "N";
            progressHtml += "\t\t\t</table>";

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
//            String formatToCCMailStr = ToUserMailAdditionalExplanation;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCallCenterScheduleQueryCustomer.html", language);
//            if (language.equals("zh-CN")) {
//                formatToCCMailStr = iMailService.readMailContent("issueMailCallCenterScheduleQueryCustomer.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
//                //formatToCCMailStr = ToUserMailAdditionalExplanation_TW;
//                formatToCCMailStr = iMailService.readMailContent("issueMailCallCenterScheduleQueryCustomer.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToCCMailStr = iMailService.readMailContent("issueMailCallCenterScheduleQueryCustomer.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToCCMailStr = iMailService.readMailContent("issueMailCallCenterScheduleQueryCustomer.html","zh-THAI");
//            }

            //組織案件附件超鏈接
            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
                List<IssueAttachmentFile> attachFiles = GetIssueAdditionalExplanationAttachmentFiles(Long.toString(issue.getIssueId()), dt);
                if (attachFiles != null && attachFiles.size() > 0) {
                    attachementStr = "";
                    for (IssueAttachmentFile file : attachFiles) {
                        attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                    }
                }
            }

            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerName,
                    crmId, processColsed, issue.getUserContact().getName(), issue.getServiceStaff(), transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 10),
                    issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), issue.getUserContact().getPhone02(), issueDescription,
                    attachementStr, progressHtml, issue.getIssueCasedetail().getAdditionalExplanation()
            );

            Mail mail03 = new Mail();
            String subject = "";
            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
                //【鼎捷服务云】Call-Center进度查询
                subject = messageUtils.get("IssueAdditionalExplanationSubmitMailToCC", languageStandard);
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject("IssueAdditionalExplanationSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion());
            }
//            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
//                //【鼎捷服务云】Call-Center进度查询
//                subject = EsCloudMailSubject.IssueAdditionalExplanationSubmitMailToCC.toString();
//            } else if (connectArea.equals("TW")) {
//                //台湾区，要增加其他标题内容
//                subject = EsCloudMailSubject.IssueAdditionalExplanationSubmitMailToCC.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion());
//            }

            mail03.setSubject(subject);
            mail03.setMessage(mailMsg);
            mail03.setReceivers(receivers03);
            mail03.setMailSourceType(MailSourceType.Issue);
            mail03.setUrl("");
            mail03.setSourceId(Long.toString(issue.getIssueId()));
            mail03.setPriority(1);
            mail03.setLanguage(language);
            mails.add(mail03);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送跟催给案件提交人的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueFollowUpMail(Issue issue, String dsc) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(issue.getUserContact().getUserId());//(issue.getUserId());
            if (userPersonalInfo != null && userPersonalInfo.getLanguage() != null && !userPersonalInfo.getLanguage().isEmpty()) {
                language = userPersonalInfo.getLanguage();
                userTimeZone = getTimeZoneMin(userPersonalInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
//            String formatToSubmiterMailStr = ToSubmiterFollowUpMailStr;
            String formatToSubmiterMailStr = commonMailService.readMailContent("issueMailReminderCustomer.html", language);
//            if (language.equals("zh-CN")) {
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailReminderCustomer.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
////                formatToSubmiterMailStr = ToSubmiterFollowUpMailStr_TW;
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailReminderCustomer.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailReminderCustomer.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailReminderCustomer.html","zh-THAI");
//            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMail.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }
            System.out.print("After PrepareIssueMail.GetIssueAttachmentFiles " + issueId);

            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String submitTime = transferTimeZone(issue.getSubmitTime(), userTimeZone);
            if (issue.getSubmitTime().endsWith(".0"))
                submitTime = submitTime.substring(0, submitTime.length() - 2);
            String mailMsg = String.format(formatToSubmiterMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, submitTime,
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription, dsc);

            //1. 获取客户mail
            //组织发送给提交者的邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(issue.getUserContact().getEmail());
            //add 添加额外的抄送人
            if (issue.getCcUser() != null && !issue.getCcUser().trim().equals("")) {
                String ccUser = issue.getCcUser().trim();
                String[] list = ccUser.split(";");
                for (int i = 0; i < list.length; i++) {
                    receivers01.add(list[i].trim());
                }
            }
            Mail mail01 = new Mail();
            String subject = messageUtils.get("IssueSubmitFollowUpMailToSubmiter", language);
//            subject = EsCloudMailSubject.IssueSubmitFollowUpMailToSubmiter.toString();
            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);

            mail01.setReceivers(receivers01);

            mail01.setMailSourceType(MailSourceType.Issue);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(issue.getIssueId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);

        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给客服CC的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueFollowUpMailToCC(Issue issue, String dsc) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            String serviceId = GetServiceID(issue.getIssueId());
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(serviceId);//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());

            //設置用戶群抄收人
//            String ccGroup = issue.getCcGroupUser();
//            if (ccGroup != null && !(ccGroup = ccGroup.trim()).isEmpty()) {
//                String[] list = ccGroup.split(";");
//                for (int i = 0; i < list.length; i++) {
//                    receivers02.add(list[i].trim());
//                }
//            }

            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0;//默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
//            String formatToCCMailStr = ToCCFollowUpMailStr;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailReminderCustomerService.html", language);
//            if (language.equals("zh-CN")) {
//                formatToCCMailStr = iMailService.readMailContent("issueMailReminderCustomerService.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
//                //formatToCCMailStr = ToCCFollowUpMailStr_TW;
//                formatToCCMailStr = iMailService.readMailContent("issueMailReminderCustomerService.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToCCMailStr = iMailService.readMailContent("issueMailReminderCustomerService.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToCCMailStr = iMailService.readMailContent("issueMailReminderCustomerService.html","zh-THAI");
//            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);

            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String submitTime = transferTimeZone(issue.getSubmitTime(), userTimeZone);
            if (issue.getSubmitTime().endsWith(".0"))
                submitTime = submitTime.substring(0, submitTime.length() - 2);
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, submitTime,
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription, dsc);

            Mail mail02 = new Mail();
            String subject = messageUtils.get("IssueSubmitFollowUpMailToCC", language);
//            subject = EsCloudMailSubject.IssueSubmitFollowUpMailToCC.toString();

            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 获取案件列表
     *
     * @param issuesGetRequest 需求项目
     * @return
     */
    @Override
    public List<Issue> GetIssues(IssuesGetRequest issuesGetRequest) {
        int start = (issuesGetRequest.getPage() - 1) * issuesGetRequest.getSize();
        //Map<String,String> userInfo = userService.GetUserRole(userId);
        //Mis
//        if(department.equals("")){
//            return issueDao.SelectIssueListByServiceCode(userInfo.get("serviceCode"), productCode, status, start, count);
//        }else //普通用户
        return issueDao.SelectIssueList(issuesGetRequest.getAuthuserid(), issuesGetRequest.getProduct(), issuesGetRequest.getStatus(), issuesGetRequest.getIssueType(), start, issuesGetRequest.getSize(), issuesGetRequest.getAuthuserdept(), issuesGetRequest.getToken(), issuesGetRequest.getNewReply(), issuesGetRequest.getMyself(), issuesGetRequest.getFrom(),issuesGetRequest.getServiceCode(),issuesGetRequest.getUserCollect());
    }

    @Override
    public List<Issue> getIssuesForMis(int pageIndex, int size, String userId, String department, String issueStatus, String issueType, String startTime, String endTime, String machineRegion, String serviceCode, String productCode, String erpSystemCode, String esSearch) {
//        int start = (issuesGetRequest.getPage() - 1) * issuesGetRequest.getSize();
        //Map<String,String> userInfo = userService.GetUserRole(userId);
        //Mis
//        if(department.equals("")){
//            return issueDao.SelectIssueListByServiceCode(userInfo.get("serviceCode"), productCode, status, start, count);
//        }else //普通用户
        return issueDao.getIssuesForMis(pageIndex, size, userId, department, issueStatus, issueType, startTime, endTime, machineRegion, serviceCode, productCode, erpSystemCode, esSearch);
    }

    /**
     * 获取案件列表
     *
     * @param issuesGetRequest 需求项目
     * @return
     */
    @Override
    public List<Issue> GetIssuesbyDescription(IssuesGetRequest issuesGetRequest) {
        int start = (issuesGetRequest.getPage() - 1) * issuesGetRequest.getSize();
        //Map<String,String> userInfo = userService.GetUserRole(userId);
        //Mis
//        if(department.equals("")){
//            return issueDao.SelectIssueListByServiceCode(userInfo.get("serviceCode"), productCode, status, start, count);
//        }else //普通用户
        //return issueDao.SelectIssueListbydescription(userId, productCode, status, issueType, start, count, department, queryUserId, description,searchItem, orderByItems);
        return issueDao.SelectIssueListbydescription(issuesGetRequest.getAuthuserid(), issuesGetRequest.getProduct(), issuesGetRequest.getStatus(), issuesGetRequest.getIssueType(), start, issuesGetRequest.getSize(), issuesGetRequest.getAuthuserdept(), issuesGetRequest.getToken(), issuesGetRequest.getSearchByItems(), issuesGetRequest.getOrderByItems(), issuesGetRequest.getServiceRegion(), issuesGetRequest.getWaitingEvaluatedIssue_TW(), issuesGetRequest.getCondition_TW(), issuesGetRequest.getCondition_check_TW(), issuesGetRequest.getAgent(), issuesGetRequest.getNewReply(), issuesGetRequest.getMyself(), issuesGetRequest.getFrom(), issuesGetRequest.getServiceCode(),issuesGetRequest.getUserCollect());
    }

    /**
     * 获取案件列表
     *
     * @param userId 用户Id
     * @param status 案件状态
     * @param page   页数
     * @param count  数量
     * @return
     */
    @Override
    public List<Issue> GetIssuesByStaff(String userId, String status, int page, int count, String deptId, String queryUserId) {
        int start = (page - 1) * count;
        return issueDao.SelectIssueListByStaff(userId, status, start, count, deptId, queryUserId);
    }

    /**
     * 获取案件的处理进度
     *
     * @param issueId 案件Id
     * @return
     */
    @Override
    public Issue GetIssueProgress(String issueId, String userId, String from) {
        Issue issue = issueDao.SelectIssueByIssueId(issueId, from, userId);
        if (issue != null) {  //huly: 修复漏洞/bug 增加非空判断
            issue.setIssueCasedetail(issueDao.SelectIssueCasedetail(issueId));
            issue.setIssueProgresses(issueDao.SelectIssueProgress(issueId, userId));
            issue.setIssueAdditionalExplanation(issueDao.SelectIssueAdditionalExplanation(issueId));
            issue.setIssueSummary(issueDao.getIssueSummary(issueId));
            issue.setIssueKbshare(issueDao.SelectIssueKbshare(issueId));
        }

        return issue;
    }

    @Override
    public Issue GetIssueProgressForMis(String issueId, String userId) {
        Issue issue = issueDao.SelectIssueByIssueIdForMis(issueId);
        if (issue != null) {  //huly: 修复漏洞/bug 增加非空判断
            issue.setIssueCasedetail(issueDao.SelectIssueCasedetail(issueId));
            issue.setIssueProgresses(issueDao.SelectIssueProgress(issueId, userId));
            issue.setIssueAdditionalExplanation(issueDao.SelectIssueAdditionalExplanation(issueId));
            issue.setIssueSummary(issueDao.getIssueSummary(issueId));
        }

        return issue;
    }
//    private SimpleResponse EvaluationGrade(String evaluationgradestr) {
//        return restTemplate.postForObject("http://evaluationservice/evaluationgrade", evaluationgradestr, SimpleResponse.class);
//    }

    /**
     * 催单
     *
     * @param issueId
     * @param userId
     * @return
     */
    @Override
    public boolean RemindIssue(long issueId, String userId, String deptId) {
        return true;
    }

    /**
     * 审核案件
     *
     * @param issueId
     * @param userId
     * @return
     */
    @Override
    public boolean ConfirmIssue(long issueId, String userId, String desc, String deptId) {
        boolean result = SetIssueStatus(issueId, "", IssueStatus.Submited, IssueStatus.Confirming,
                IssueProcessType.Confirm, userId, desc, deptId, false);
        if (result) {
            //审核通过,同步状态更新为N；退回，同步状态更新为
            issueDao.UpdateSyncStatusByIssueId(issueId, SyncStatus.UnSync.toString());
            //7.发送邮件和通知给客服
            try {
                Issue issue = GetIssueProgress(String.valueOf(issueId), userId, "");
                try {
                    StaffUserInfo staffUserInfo = new StaffUserInfo();
                    String moduleCode = (issue.getIssueCasedetail() != null && !StringUtils.isEmpty(issue.getIssueCasedetail().getErpSystemCode())) ? issue.getIssueCasedetail().getErpSystemCode() : "";
                    ServiceStaffGetResponse serviceStaffGetResponse = userService.GetServiceStaffInfo(
                            issue.getUserId(), issue.getServiceCode(), issue.getProductCode() == null ? "" : issue.getProductCode()
                            , issue.getServiceRegion() == null ? "" : issue.getServiceRegion(), issue.getIssueDescription(), issue.getIssueClassificationNo() == null ? "" : issue.getIssueClassificationNo()
                            , issue.getSubmitWay() == null ? "" : issue.getSubmitWay(), moduleCode, issue.getUserId() == null ? "" : issue.getUserId()
                    );
                    System.out.print(serviceStaffGetResponse);
                    if (serviceStaffGetResponse != null && serviceStaffGetResponse.getCode().equals("0")) {
                        staffUserInfo = serviceStaffGetResponse.getStaffUserInfo();
                        //staffUserInfo = userService.GetServiceStaffInfo(issue.getServiceCode(), issue.getProductCode() == null ? "" : issue.getProductCode());
                        //设置客户群抄收人
                        issue.setCcGroupUser(serviceStaffGetResponse.getMailCC());
                        if (staffUserInfo != null) {
                            issue.setServiceStaff(staffUserInfo.getUserId());
                            issue.setDepartment(staffUserInfo.getDepartment());
                            /*//重新设置客户群抄收人
                            if("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())){
                                issue.setCcGroupUser(staffUserInfo.getService_cc_staff_emails());
                            }*/
                        }
                        //设置是否没有责任人员
                        issue.setNoResponsiblePersion(serviceStaffGetResponse.isNoResponsiblePerson());
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                issueDao.UpdateIssue(issueId, issue.getCrmId(), issue.getServiceStaff(), issue.getDepartment(), IssueStatus.Submited.toString(), Integer.parseInt(issue.getUserContactId()));
                //云管家打通T服务云，需要存下面3个字段
                IssueProgress issueProgress = new IssueProgress();
                issueProgress.setIssueId(issueId);
                issueProgress.setHandlerId(issue.getServiceStaff());
                issueProgress.setCurrentStatus(IssueStatus.Submited.toString());
                issueProgress.setProcessType(IssueProcessType.Confirm.toString());
                issueProgress.setSequeceNum(issueDao.getMaxOrder(issue.getIssueId()));
                issueDao.UpdateIssueProgress(issueProgress);
                SendNoticeToCCANDCustomer(issue);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        return result;
    }

    /**
     * 设置案件状态，并保存案件处理进度
     *
     * @param issueId
     * @param crmId
     * @param issueStatus
     * @param userId
     * @param desc
     */
    private boolean SetIssueStatus(long issueId, String crmId, IssueStatus issueStatus, IssueStatus oldIssueStatus,
                                   IssueProcessType issueProcessType, String userId, String desc,
                                   String deptId, boolean issueProgressNeedSync) {
        boolean res = false;
        try {
            int success = 0;
            //如果走审核的话，是mis人员在操作，sql中不需要拼上userid、部门的条件
            if (IssueStatus.Confirming.toString().equals(oldIssueStatus.toString())) {
                success = issueDao.UpdateIssueStatusByStaff(issueId, issueStatus.toString(), oldIssueStatus.toString(), deptId, userId);
            } else {
                success = issueDao.UpdateIssueStatus(issueId, issueStatus.toString(), oldIssueStatus.toString(), deptId, userId);
            }
            if (success > 0) {
                SaveIssueProgress(issueId, crmId, issueProcessType, userId, desc, issueProgressNeedSync);
                //云管家打通T服务云，需要存下面3个字段
                Issue issueTmp = GetIssueProgress(String.valueOf(issueId), userId, "");
                IssueProgress issueProgress = new IssueProgress();
                issueProgress.setIssueId(issueId);
                issueProgress.setCurrentStatus(issueStatus.toString());
                issueProgress.setProcessType(issueProcessType.toString());
                issueProgress.setSequeceNum(issueDao.getMaxOrder(issueId));
                issueDao.UpdateIssueProgress(issueProgress);
                res = true;
            } else
                res = false;
        } catch (Exception ex) {
            res = false;
            log.error(ex);
            ex.printStackTrace();
        }
        return res;
    }

    private boolean SaveIssueProgress(long issueId, String crmId, IssueProcessType issueProcessType, String userId,
                                      String desc, boolean issueProgressNeedSync) {
        try {
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            IssueProgress issueProgress = new IssueProgress();
            issueProgress.setProcessType(issueProcessType.toString());
            issueProgress.setProcessor(userId);
            issueProgress.setDescription(desc);
            issueProgress.setProcessTime(sdf.format(new Date()));
            issueProgress.setProcessHours(0);
            if (issueProgressNeedSync) {
                issueProgress.setSyncStatus("T");
            }
            issueProgress.setSequeceNum(issueDao.getMaxOrder(issueId));
            return issueDao.InsertIssueProgress(issueId, crmId, issueProgress) > 0;
        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
            return false;
        }
    }

    public int GetFollowUpIssueStatus(Long issueId) {
        boolean canFollowUp = false;
        try {
            String lastTime = issueDao.GetIssueFollowUpLastTime(issueId);
            if (lastTime == null) {
                canFollowUp = true;
            } else {
                if (lastTime.equals("")) {
                    canFollowUp = true;
                } else {
                    Calendar nowTime = Calendar.getInstance();
                    nowTime.add(Calendar.MINUTE, -30);
                    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date lastDateTime = sdf.parse(lastTime);
                    canFollowUp = lastDateTime.before(nowTime.getTime());
                }
            }
        } catch (Exception ex) {
            canFollowUp = false;
            log.error(ex);
            ex.printStackTrace();
        }
        return canFollowUp ? 1 : 0;
    }

    public int FollowUpIssue(Issue issue, String userId, String desc) {
        int followUpResult = 0;
        try {
            String lastTime = issueDao.GetIssueFollowUpLastTime(issue.getIssueId());
            boolean canFollowUp;
            if (lastTime == null) {
                canFollowUp = true;
            } else {
                if (lastTime.equals("")) {
                    canFollowUp = true;
                } else {
                    Calendar nowTime = Calendar.getInstance();
                    nowTime.add(Calendar.MINUTE, -30);
                    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date lastDateTime = sdf.parse(lastTime);
                    canFollowUp = lastDateTime.before(nowTime.getTime());
                }
            }
            boolean res = false;
            if (canFollowUp) {
                Issue issueTmp = GetIssueProgress(String.valueOf(issue.getIssueId()), userId, "");

                IssueProgress issueProgress = new IssueProgress();
                issueProgress.setCurrentStatus(issueTmp.getIssueStatus());
                issueProgress.setSequeceNum(issueDao.getMaxOrder(issue.getIssueId()));
                issueProgress.setProcessType(IssueProcessType.FollowUp.toString());
                issueProgress.setProcessor(userId);
                issueProgress.setReplyType(ReplyType.Q.toString());
                issueProgress.setProcessTime(com.digiwin.escloud.issueservice.t.utils.StringUtil.getCurrentTime());
                issueProgress.setHandlerId(issueTmp.getServiceId());
                issueProgress.setDescription(desc);
                res = issueDao.InsertIssueProgress(issue.getIssueId(), issueTmp.getCrmId(), issueProgress) > 0;

                //更新最新回复
                issueDetailServiceV3.updateIssueNewReply(String.valueOf(issue.getIssueId()), NewReplyEnum.CustomerNewReply.toString());
                if (res) {
                    SendFollowUpMail(issueTmp, desc);
                } else
                    followUpResult = 1;
            } else
                followUpResult = 2;
        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
            followUpResult = 1;
        }
        return followUpResult;
    }

    /**
     * 退回
     *
     * @param issueId
     * @param userId
     * @param desc
     * @return
     */
    @Override
    public boolean SendBackIssue(long issueId, String userId, String desc, String deptId) {
        boolean result = SetIssueStatus(issueId, "", IssueStatus.Returned, IssueStatus.Confirming,
                IssueProcessType.SendBack, userId, desc, deptId, false);
        if (result) {
            //审核通过,同步状态更新为N；退回，同步状态更新为
            issueDao.UpdateSyncStatusByIssueId(issueId, SyncStatus.DontNeedSync.toString());
            //7.发送邮件给客户，案件被退回
            try {
                Issue issue = GetIssueProgress(String.valueOf(issueId), userId, "");

                //云管家打通T服务云，需要存下面3个字段
                IssueProgress issueProgress = new IssueProgress();
                issueProgress.setIssueId(issueId);
                issueProgress.setHandlerId("");//退回时 ，但是的handlerid为空
                issueProgress.setCurrentStatus(IssueStatus.Returned.toString());
                issueProgress.setProcessType(IssueProcessType.SendBack.toString());
                issueProgress.setSequeceNum(issueDao.getMaxOrder(issue.getIssueId()));
                issueDao.UpdateIssueProgress(issueProgress);

                SendBackMailToSubmiter(issue);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

        }
        return result;
    }

    /**
     * 评价案件
     *
     * @param issueId
     * @param userId
     * @param desc
     * @param deptId
     * @return
     */
    @Override
    public boolean EvaluateIssue(long issueId, String userId, String desc, String deptId) {
        return SetIssueStatus(issueId, "", IssueStatus.Evaluated, IssueStatus.Closed,
                IssueProcessType.Evaluate, userId, desc, deptId, false);
    }

    /**
     * 获取用户的所有案件数量
     *
     * @param userId 用户ID
     * @return
     */
    @Override
    public IssueCount GetIssueCountByUserId(String userId, String productCode, String department, String queryUserId, String serviceRegion, String condition_TW, String condition_check_TW, String agent, String newReply, String myself, String from, String serviceCode) {
        return issueDao.SelectIssueCountByUserId(userId, productCode, department, queryUserId, "P", serviceRegion, condition_TW, condition_check_TW, agent, newReply, myself, from, serviceCode);
    }

    /**
     * 获取用户的所有案件数量
     *
     * @param issuesGetRequest 需求项目
     * @return
     */
    @Override
    public IssueCount GetIssueCountByUserIdAndDescription(IssuesGetRequest issuesGetRequest) {
        return issueDao.SelectIssueCountByUserIdAndDescription(issuesGetRequest.getAuthuserid(), issuesGetRequest.getProduct(), issuesGetRequest.getAuthuserdept(), issuesGetRequest.getToken(), "P", issuesGetRequest.getSearchByItems(), issuesGetRequest.getServiceRegion(), issuesGetRequest.getCondition_TW(), issuesGetRequest.getCondition_check_TW(), issuesGetRequest.getNewReply(), issuesGetRequest.getMyself(), issuesGetRequest.getFrom(), issuesGetRequest.getServiceCode());
    }

    @Override
    public List<Map<String, String>> getWebIssueClassification(String serviceRegion, String customerServiceCode, String productCode) {
        return issueDao.getWebIssueClassification(serviceRegion, customerServiceCode, productCode);
    }

    @Override
    public List<ServiceProductCount> getServiceProductTarget(String serviceRegion, String staffId, String refreshDate) {
        return issueDao.getServiceProductTarget(serviceRegion, staffId, refreshDate);
    }

    @Override
    public void SendMailTest(String email) {
        try {
            List<Mail> mails = new ArrayList<Mail>();
            try {
                //1. 获取客户mail
                //组织发送给提交者的邮件
                List<String> receivers01 = new ArrayList<String>();
                receivers01.add(email);
                Mail mail01 = new Mail();
                mail01.setSubject("【服务云】您的问题已提交成功");
                mail01.setMessage("您于2018-01-17填写的问题已提交成功，客户专员会第一时间为您处理！");
                mail01.setMailSourceType(MailSourceType.Issue);
                mail01.setReceivers(receivers01);
                mail01.setUrl("");
                mail01.setSourceId("18");
                mail01.setPriority(1);
                mails.add(mail01);

            } catch (Exception ex) {
                log.error(ex);
                ex.printStackTrace();
            }
            Gson gson = new Gson();

            String[] mx = {gson.toJson(mails)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
    }

    /**
     * 查看图片
     *
     * @param fileId
     * @return
     */
    @Override
    public GridFSDBFile GetIssueAttachFile(String fileId) {
        return gridFsTemplate.findOne(Query.query(Criteria.where("_id").is(fileId)));
    }

    /**
     * 获取案件附件文件列表
     *
     * @param issueId
     * @return
     */
    @Override
    public List<IssueAttachmentFile> GetIssueAttachmentFiles(String issueId) {
        List<IssueAttachmentFile> issueAttachmentFiles = new ArrayList<IssueAttachmentFile>();
        try {
            String regex = "^issue__" + issueId + "__";
            System.out.print("find attachmentFiles regex:" + regex);
            List<GridFSDBFile> gridFSDBFiles = gridFsTemplate.find(Query.query(Criteria.where("filename").regex(regex)));
            StringBuilder sbLog = new StringBuilder();
            if (gridFSDBFiles != null && gridFSDBFiles.size() > 0) {
                for (GridFSDBFile file : gridFSDBFiles) {
                    sbLog.setLength(0);
                    IssueAttachmentFile issueAttachmentFile = new IssueAttachmentFile();
                    issueAttachmentFile.setFileId(file.getId().toString());
                    issueAttachmentFile.setFileName(file.getFilename());
                    issueAttachmentFile.setFileType(file.getMetaData().get("fileType") != null ? file.getMetaData().get("fileType").toString() : "");
                    issueAttachmentFiles.add(issueAttachmentFile);
                    sbLog.append("found ");
                    sbLog.append(issueAttachmentFile.getFileName());
                    sbLog.append(" fileSize:");
                    sbLog.append(file.getLength());
                    sbLog.append(" chunkSize:");
                    sbLog.append(file.getChunkSize());
                    sbLog.append("\n");
                    System.out.print(sbLog.toString());
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return issueAttachmentFiles;
    }

    /**
     * 获取案件补充说明附件文件列表
     *
     * @param issueId
     * @return
     */
    @Override
    public List<IssueAttachmentFile> GetIssueAdditionalExplanationAttachmentFiles(String issueId, String dt) {
        List<IssueAttachmentFile> issueAttachmentFiles = new ArrayList<IssueAttachmentFile>();
        try {
            List<GridFSDBFile> gridFSDBFiles = gridFsTemplate.find(Query.query(Criteria.where("filename").regex("^issue__" + issueId + "__" + dt + "__")));
            if (gridFSDBFiles != null && gridFSDBFiles.size() > 0) {
                for (GridFSDBFile file : gridFSDBFiles) {
                    IssueAttachmentFile issueAttachmentFile = new IssueAttachmentFile();
                    issueAttachmentFile.setFileId(file.getId().toString());
                    issueAttachmentFile.setFileName(file.getFilename());
                    issueAttachmentFiles.add(issueAttachmentFile);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return issueAttachmentFiles;
    }

    /**
     * 获取案件附件
     *
     * @param issueId
     * @return
     */
    @Override
    public List<IssueAttachment> GetIssueAttachments(String issueId) {
        List<IssueAttachment> issueAttachments = new ArrayList<IssueAttachment>();
        try {
            List<GridFSDBFile> gridFSDBFiles = gridFsTemplate.find(Query.query(Criteria.where("filename").regex("^issue__" + issueId + "__")));
            if (gridFSDBFiles != null && gridFSDBFiles.size() > 0) {
                int i = 1;
                for (GridFSDBFile file : gridFSDBFiles) {
                    ByteArrayOutputStream output = new ByteArrayOutputStream();
                    try {
                        copy(file.getInputStream(), output);
                        IssueAttachment issueAttachment = new IssueAttachment();
                        issueAttachment.setSequeceNum(i);
                        issueAttachment.setAttachment(output.toByteArray());
                        String fileName = file.getFilename();
                        int lastDotIndex;
                        //处理案件后缀，优先从fileName抓取
                        if (org.apache.commons.lang.StringUtils.isNotBlank(fileName) &&
                                (lastDotIndex = fileName.lastIndexOf(".")) > -1) {
                            issueAttachment.setFileType(fileName.substring(lastDotIndex, fileName.length()));
                        } else {
                            //兼容旧逻辑
                            issueAttachment.setFileType(file.getContentType());
                        }
//                        issueAttachment.setFileId(file.getId().toString());
//                        issueAttachment.setFileName(file.getFilename());
                        issueAttachments.add(issueAttachment);
                        i++;
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        output = null;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return issueAttachments;
    }

    /**
     * 获取案件附件
     *
     * @param issueId
     * @return
     */
    @Override
    public List<IssueAttachment> GetIssueCallCenterAttachments(String issueId, String progressId) {
        List<IssueAttachment> issueAttachments = new ArrayList<IssueAttachment>();
        try {
            List<GridFSDBFile> gridFSDBFiles = gridFsTemplate.find(Query.query(Criteria.where("filename").regex("^issue__" + issueId + "__" + progressId + "__")));
            if (gridFSDBFiles != null && gridFSDBFiles.size() > 0) {
                int i = 1;
                for (GridFSDBFile file : gridFSDBFiles) {
                    ByteArrayOutputStream output = new ByteArrayOutputStream();
                    try {
                        copy(file.getInputStream(), output);
                        IssueAttachment issueAttachment = new IssueAttachment();
                        issueAttachment.setSequeceNum(i);
                        issueAttachment.setFileName(file.getFilename());
                        issueAttachment.setAttachment(output.toByteArray());
                        String fileName = file.getFilename();
                        int lastDotIndex;
                        //处理案件后缀，优先从fileName抓取
                        if (org.apache.commons.lang.StringUtils.isNotBlank(fileName) &&
                                (lastDotIndex = fileName.lastIndexOf(".")) > -1) {
                            issueAttachment.setFileType(fileName.substring(lastDotIndex, fileName.length()));
                        } else {
                            //兼容旧逻辑
                            issueAttachment.setFileType(file.getContentType());
                        }
//                        issueAttachment.setFileId(file.getId().toString());
//                        issueAttachment.setFileName(file.getFilename());
                        issueAttachments.add(issueAttachment);
                        i++;
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        output = null;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return issueAttachments;
    }

    /*
     * 分页获取客服案件集合,只显示新案件
     * */
    @Override
    public List<IssueDetailInfo> getIssuesInfoListByUserAndStatus(String issueStatus, int pageIndex, int size, String userId, String deptId
            , String issueType, String queryUserId, String startTime, String endTime, String machineRegion, String additionalExplanationReadType, String serviceContact, String projectUpdate, String serialNumberUpdate, String custLevel, String serviceCode, String productCode, String employeeId, String crmId, String supportId) {
        List<IssueDetailInfo> serviceResult = issueDao.getNewIssuesInfoListByUserAndStatus(issueStatus, pageIndex, size, userId, deptId, issueType, queryUserId, startTime, endTime, machineRegion, additionalExplanationReadType, serviceContact, projectUpdate, serialNumberUpdate, custLevel, serviceCode, productCode, employeeId, crmId, supportId);
        return serviceResult;
    }

    @Override
    public List<IssueDetailInfo> getAllIssueDetails(String issueStatus, int pageIndex, int size, String userId, String deptId
            , String issueType, String queryUserId, String startTime, String endTime, String machineRegion, String custLevel, String contractState, String serviceCode, String productCode, String employeeId, String crmId, String additionalExplanationReadType, String issueDescription) {
        List<ProductInfo> productList = new ArrayList<>();
        if (!StringUtils.isEmpty(deptId)) {
            //主管查看的案件列表只能限定在他授权的产品线范围内，先获取授权产品线
            productList = userService.getProductList(userId);
            if (CollectionUtils.isEmpty(productList)) {
                return new ArrayList<>();
            }
        }
        List<IssueDetailInfo> serviceResult = issueDao.getAllIssueDetails(issueStatus, pageIndex, size, userId, deptId, issueType, queryUserId, startTime, endTime, machineRegion, custLevel, contractState, serviceCode, productCode, productList, employeeId, crmId, additionalExplanationReadType, issueDescription);
        return serviceResult;
    }

    @Override
    public List<IssueDetailInfo> getIssuesInfoListByUserAndStatusForCustomerServiceManager(String issueStatus, int pageIndex, int size, String userId, String deptId
            , String issueType, String queryUserId, String startTime, String endTime, String machineRegion, String custLevel, String serviceCode, String productCode, String employeeId, String crmId, String additionalExplanationReadType, String classificationStr) {
        //主管查看的案件列表只能限定在他授权的产品线范围内，先获取授权产品线
        List<ProductInfo> productList = userService.getProductList(userId);
        if (CollectionUtils.isEmpty(productList)) {
            return new ArrayList<>();
        }
        List<IssueProductCodeClassificationData> issueProductCodeClassificationList = getIssueProductCodeClassification(classificationStr);
        List<IssueDetailInfo> serviceResult = issueDao.getIssuesInfoListByUserAndStatusForCustomerServiceManager(issueStatus, pageIndex, size, userId, deptId, issueType, queryUserId, startTime, endTime, machineRegion, custLevel, serviceCode, productCode, productList, employeeId, crmId, additionalExplanationReadType, issueProductCodeClassificationList);
        return serviceResult;
    }

    /*
     * 客服主管查看的案件列表总数量
     * */
    @Override
    public int getIssuesInfoCountForCustomerServiceManager(String issueStatus, String userId, String deptId
            , String issueType, String queryUserId, String startTime, String endTime, String machineRegion, String custLevel, String serviceCode, String productCode, String employeeId, String crmId, String additionalExplanationReadType, String classificationStr) {
        //主管查看的案件列表只能限定在他授权的产品线范围内，先获取授权产品线
        List<ProductInfo> productList = userService.getProductList(userId);
        if (CollectionUtils.isEmpty(productList)) {
            return 0;
        }
        List<IssueProductCodeClassificationData> issueProductCodeClassificationList = getIssueProductCodeClassification(classificationStr);
        int count = issueDao.getIssuesInfoCountForCustomerServiceManager(issueStatus, userId, deptId, issueType, queryUserId, startTime, endTime, machineRegion, custLevel, serviceCode, productCode, productList, employeeId, crmId, additionalExplanationReadType, issueProductCodeClassificationList);
        return count;
    }

    public List<IssueProductCodeClassificationData> getIssueProductCodeClassification(String classificationStr) {
        List<IssueProductCodeClassificationData> issueProductCodeClassificationList = new ArrayList<>();
        if (!StringUtils.isEmpty(classificationStr)) {
            //  productCode-classificationId, productCode-classificationId.....
            List<String> tmpClassification = Arrays.asList(classificationStr.split(","));
            if (!CollectionUtils.isEmpty(tmpClassification)) {
                //表示有資料，則在分  productCode:classificationId
                tmpClassification.stream().forEach(x -> {
                    List<String> tmpProductCodeClassification = Arrays.asList(x.split("-"));
                    if (!CollectionUtils.isEmpty(tmpProductCodeClassification) && 2 == (long) tmpProductCodeClassification.size()) {
                        // 表示有分成productCode 與classificationId
                        IssueClassificationData issueClassificationData = new IssueClassificationData();
                        issueClassificationData.setProductCode(tmpProductCodeClassification.get(0));
                        issueClassificationData.setIssueClassification(tmpProductCodeClassification.get(1));

                        if (!CollectionUtils.isEmpty(issueProductCodeClassificationList)) {
                            // 表示裡面有資料，則要找出相同productCode
                            //stream().filter(x -> serverId == x.getServerId() && name.equals(x.getDevice())).findFirst().orElse(null);
                            IssueProductCodeClassificationData tmpIssueProductCodeClassificationData = issueProductCodeClassificationList.stream().filter(p -> p.getProductCode().equals(tmpProductCodeClassification.get(0))).findFirst().orElse(null);
                            if (!ObjectUtils.isEmpty(tmpIssueProductCodeClassificationData)) {
                                // 表示找到
                                tmpIssueProductCodeClassificationData.getIssueClassification().add(issueClassificationData);
                            } else {
                                //// 表示找不到
                                IssueProductCodeClassificationData issueProductCodeClassificationData = new IssueProductCodeClassificationData();
                                issueProductCodeClassificationData.setProductCode(tmpProductCodeClassification.get(0));
                                List<IssueClassificationData> issueClassificationDataList = new ArrayList<>();
                                issueClassificationDataList.add(issueClassificationData);
                                issueProductCodeClassificationData.setIssueClassification(issueClassificationDataList);
                                issueProductCodeClassificationList.add(issueProductCodeClassificationData);
                            }
                        } else {
                            IssueProductCodeClassificationData issueProductCodeClassificationData = new IssueProductCodeClassificationData();
                            issueProductCodeClassificationData.setProductCode(tmpProductCodeClassification.get(0));
                            List<IssueClassificationData> issueClassificationDataList = new ArrayList<>();
                            issueClassificationDataList.add(issueClassificationData);
                            issueProductCodeClassificationData.setIssueClassification(issueClassificationDataList);
                            issueProductCodeClassificationList.add(issueProductCodeClassificationData);
                        }
                    }
                });
            }
        }
        return issueProductCodeClassificationList;
    }

    /*
     * 获取客服案件总数，只显示新案件
     * */
    @Override
    public int getIssuesInfoCountByUserAndStatus(String issueStatus, String userId, String deptId
            , String issueType, String queryUserId, String startTime, String endTime, String machineRegion, String additionalExplanationReadType, String serviceContact, String projectUpdate, String serialNumberUpdate, String custLevel, String serviceCode, String productCode, String employeeId, String crmId, String supportId) {
        int count = issueDao.getNewIssuesInfoCountByUserAndStatus(issueStatus, userId, deptId, issueType, queryUserId, startTime, endTime, machineRegion, additionalExplanationReadType, serviceContact, projectUpdate, serialNumberUpdate, custLevel, serviceCode, productCode, employeeId, crmId, supportId);
        return count;
    }

    /*
     * 客服主管查看的案件列表总数量
     * */
    @Override
    public int StaffIssueCountV2(String issueStatus, String userId, String deptId
            , String issueType, String queryUserId, String startTime, String endTime, String machineRegion, String custLevel, String contractState, String serviceCode, String productCode, String employeeId, String crmId, String additionalExplanationReadType, String issueDescription) {
        List<ProductInfo> productList = new ArrayList<>();
        if (!StringUtils.isEmpty(deptId)) {
            //主管查看的案件列表只能限定在他授权的产品线范围内，先获取授权产品线
            productList = userService.getProductList(userId);
            if (CollectionUtils.isEmpty(productList)) {
                return 0;
            }
        }

        int count = issueDao.getIssuesInfoCountV2(issueStatus, userId, deptId, issueType, queryUserId, startTime, endTime, machineRegion, custLevel, contractState, serviceCode, productCode, productList, employeeId, crmId, additionalExplanationReadType, issueDescription);
        return count;
    }

    /*
     * 获取產品線為科維之案件总数，只显示新案件
     * */
    @Override
    public int getIssuesInfoCountByKewei(String issueStatus, String issueType, String additionalExplanationReadType, String productCode) {
        int count = issueDao.getNewIssuesInfoCountByKewei(issueStatus, issueType, additionalExplanationReadType, productCode);
        return count;
    }

    @Override
    public List<IssueDetailInfo> getWebIssuesInfoList(String issueStatus, String productCode, String codeOrName, int pageIndex, int size
            , String issueType, String startTime, String endTime) {
        List<IssueDetailInfo> serviceResult = issueDao.getWebIssuesInfoList(issueStatus, productCode, codeOrName, pageIndex, size, issueType, startTime, endTime);
        return serviceResult;
    }

    @Override
    public int getWebIssuesCount(String issueStatus, String productCode, String codeOrName, String issueType, String startTime, String endTime) {
        return issueDao.getWebIssuesCount(issueStatus, productCode, codeOrName, issueType, startTime, endTime);
    }

    @Override
    public List<String> getCustomerIssueServiceRegionInfo(String customerServiceCode) {
        return issueDao.getCustomerIssueServiceRegionInfo(customerServiceCode);
    }

    @Override
    public List<String> getMachineRegionByStaffId(String staffId) {
        return issueDao.getMachineRegionByStaffId(staffId);
    }

    private int getTimeZoneMin(String utimezone) {
        //将时区转成分钟
        int TimeZoneH = 0;
        int TimeZoneM = 0;
        int userTimeZone = 0;
        if (StringUtils.isNotBlank(utimezone)) //huly: 修复漏洞/bug == 改成equals
        {
            try {
                String[] list = utimezone.split(":");
                if (list.length > 1) {
                    TimeZoneH = Integer.valueOf(list[0]);
                    TimeZoneM = Integer.valueOf(list[1]);
                    userTimeZone = TimeZoneH * 60 + TimeZoneM;
                }
            } catch (Exception e) {
                e.printStackTrace();

            }
        }
        return userTimeZone;

    }

    /**
     * 转换时区
     *
     * @param srcTime
     * @param userTimezone
     * @return
     */
    @Override
    public String transferTimeZone(String srcTime, int userTimezone) {
        int serverTimezone = getTimeZoneMin(servertimezone); //server时区转成分钟
        if (serverTimezone == userTimezone || userTimezone == 0) {
            return srcTime;
        } else {
            try {
                int timezone = serverTimezone - userTimezone;
                DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = sdf.parse(srcTime);

                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.set(Calendar.MINUTE,
                        calendar.get(Calendar.MINUTE) - timezone); // 加減時區
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(calendar.getTime());

            } catch (Exception e) {
                e.printStackTrace();
                return srcTime;
            }
        }


    }

    /*將call-centeer內容標示為已讀
     * */
    @Override
    public int UpdateAdditionalExplanationReadType(String issueId) {
        return issueDao.UpdateAdditionalExplanationReadType(issueId);
    }

    /*修改问题描述* */
    @Override
    @Transactional
    public int UpdateIssueDescription(IssueDetailInfo issue) {
        int i = issueDao.UpdateIssueDescription(issue);
        boolean resDesc = i > 0;
        boolean resIssue = false;
        if (resDesc) {
            String issueSyncStatus = issueDao.getIssueSyncStatus(Long.toString(issue.getIssueId()));
            if (SyncStatus.SyncSuccess.toString().equals(issueSyncStatus) || SyncStatus.WaitSync.toString().equals(issueSyncStatus))
                resIssue = issueDao.UpdateSyncStatusByIssueId(issue.getIssueId(), SyncStatus.EditUnSync.toString()) > 0;
        }
        return i;
    }

    /*储存子单头表issue_summary */
    @Override
    public int saveIssueSummary(String issueId, String type, String processTime) {
        IssueSummary is = issueDao.getIssueSummary(issueId);
        int result = 0;
        if (is != null) {
            //已存在則修改更新
            result = issueDao.updateIssueSummary(issueId, type, processTime);
            //判断已解决若未有已接单则更新已接单时间
            if (result == 1 && type.equals("resolved") && is.getAcceptTime() == null) {
                issueDao.updateIssueSummary(issueId, "accept", processTime);
            }
            //判断結案若未有已解決则更新已解決时间
            if (result == 1 && type.equals("closed") && is.getResolvedTime() == null) {
                issueDao.updateIssueSummary(issueId, "resolved", processTime);
            }
        } else {
            //不存在则新增一笔至子单头表
            result = issueDao.insertIssueSummary(issueId, type, processTime);
            if (result == 1 && type.equals("resolved")) {
                issueDao.updateIssueSummary(issueId, "accept", processTime);
            }
            //判断結案若未有已解決则更新已解決时间
            if (result == 1 && type.equals("closed")) {
                issueDao.updateIssueSummary(issueId, "accept", processTime);
                issueDao.updateIssueSummary(issueId, "resolved", processTime);
            }
        }
        return result;
    }

    /*储存子单头表issue_summary */
    @Override
    public int saveIssueSummaryForAgressClose(String issueId, String processTime) {
        IssueSummary is = issueDao.getIssueSummary(issueId);
        int result = 0;
        if (is != null) {
            //已存在則修改更新
            result = issueDao.updateIssueSummaryForAgreeClose(issueId, processTime);
        } else {
            //不存在则新增一笔至子单头表
            result = issueDao.insertIssueSummaryForAgreeClose(issueId, processTime);
        }
        return result;
    }

    /*查询子单头表issue_summary */
    @Override
    public IssueSummary queryIssueSummary(String issueId) {
        return issueDao.getIssueSummary(issueId);
    }

    @Override
    public PageInfo<IssueDetailInfo> getAllIssueDetails(String role, String workNo, String color, String issueStatus, int pageNum, int pageSize) {


        //1 管理员 2 客服主管 3 一般客服
        //默认角色是一般客服
        String deptCode = "";
        if (StringUtils.isEmpty(role)) {
            role = "3";
        }
        if ("1".equals(role)) {
            workNo = "";
            deptCode = "";
        } else if ("2".equals(role)) {
            StaffUserInfo staffUserInfo = userDao.SelectStaffByWorkNo(workNo);
            if (!ObjectUtils.isEmpty(staffUserInfo)) {
                deptCode = staffUserInfo.getDepartment();
            }
            workNo = "";

        } else {
            deptCode = "";
        }
        Page page = PageHelper.startPage(pageNum, pageSize);
        issueDao.getIssuesDetailInfoByWorkNoFromNewIssue(deptCode, workNo, color, issueStatus);
        return new PageInfo<IssueDetailInfo>(page);
    }

    @Override
    public TrialIssueLimitResponse SelectTrialSubmitedIssueCount(String serviceCode, String productCode) {
        TrialIssueLimitResponse trialIssueLimitResponse = new TrialIssueLimitResponse();
        //判別客代、產品線的對應的試用狀態，若為試用中才繼續往下找，否則(過期、已轉正)
        try {
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            //获取客户的维护合约信息
            customerServiceInfo = customerService.GetCustomerServiceInfo(serviceCode, productCode);
            //判別試用狀態與合約日期
            if (customerServiceInfo != null) {
                //判別合約日期是否到期，是則表示無合約不可提交，否則判別是否為試用，是則繼續，否則表示為正式用戶
                if (customerServiceInfo.getContractExprityDate() != null && !"".equals(customerServiceInfo.getContractExprityDate())) {
                    Date nowDate = new Date();
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                    String nowDateStr = dateFormat.format(nowDate);
                    if (nowDateStr.compareTo(customerServiceInfo.getContractExprityDate()) <= 0) {
                        //合約未到期
                        if (customerServiceInfo.isTrial()) {
                            if (customerServiceInfo.getContractStartDate() != null && !"".equals(customerServiceInfo.getContractStartDate())) {
                                //試用中，找出已提交之案件數(在試用期間內案件數)
                                int SubmitedIssueCount = issueDao.SelectSubmitedIssueCount(serviceCode, productCode, customerServiceInfo.getContractStartDate(), customerServiceInfo.getContractExprityDate());
                                trialIssueLimitResponse.setTrialStatus("TRY_TRIAL");
                                int LimitIssueCount = 5;
                                trialIssueLimitResponse.setLimitIssueCount(LimitIssueCount); //試用中可提案之案件數設定為5筆
                                trialIssueLimitResponse.setSubmitedIssueCount(SubmitedIssueCount);
                                trialIssueLimitResponse.setRemaindIssueCount(LimitIssueCount - SubmitedIssueCount < 0 ? 0 : LimitIssueCount - SubmitedIssueCount);
                                trialIssueLimitResponse.setCode("0");
                            } else {
                                //無合約起始日期所以定義為已過期
                                trialIssueLimitResponse.setTrialStatus("EXPIRED_TRIAL");
                                trialIssueLimitResponse.setLimitIssueCount(0);
                                trialIssueLimitResponse.setSubmitedIssueCount(0);
                                trialIssueLimitResponse.setRemaindIssueCount(0);
                                trialIssueLimitResponse.setCode("0");
                            }
                        } else {
                            //非試用中則表示為正式合約，不限制案件提交數
                            trialIssueLimitResponse.setTrialStatus("TRIAL_TO_FORMAL");
                            trialIssueLimitResponse.setLimitIssueCount(0);
                            trialIssueLimitResponse.setSubmitedIssueCount(0);
                            trialIssueLimitResponse.setRemaindIssueCount(0);
                            trialIssueLimitResponse.setCode("0");
                        }
                    } else {
                        //合約到期
                        trialIssueLimitResponse.setTrialStatus("EXPIRED_TRIAL");
                        trialIssueLimitResponse.setLimitIssueCount(0);
                        trialIssueLimitResponse.setSubmitedIssueCount(0);
                        trialIssueLimitResponse.setRemaindIssueCount(0);
                        trialIssueLimitResponse.setCode("0");
                    }
                } else {
                    //無合約截止日期，則表示無合約或合約到期
                    trialIssueLimitResponse.setTrialStatus("EXPIRED_TRIAL");
                    trialIssueLimitResponse.setLimitIssueCount(0);
                    trialIssueLimitResponse.setSubmitedIssueCount(0);
                    trialIssueLimitResponse.setRemaindIssueCount(0);
                    trialIssueLimitResponse.setCode("0");
                }
            } else {
                trialIssueLimitResponse.setCode("1");
                trialIssueLimitResponse.setErrMsg("獲取客戶訊息失敗");
                log.error("找不到客戶訊息，serviceCode:" + serviceCode + "  productCode:" + productCode);
            }
        } catch (Exception ex) {
            trialIssueLimitResponse.setCode("1");
            trialIssueLimitResponse.setErrMsg("獲取客戶案件試用訊息失敗" + ex.getMessage());
            log.error(ex);
        }
        return trialIssueLimitResponse;
    }

    @Override
    public boolean CheckCustomerContract(String serviceCode, String productCode) {
        try {
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            //获取客户的维护合约信息
            customerServiceInfo = customerService.GetCustomerServiceInfo(serviceCode, productCode);
            //判別試用狀態與合約日期
            if (customerServiceInfo != null) {
                //判別合約日期是否到期，是則表示無合約不可提交，否則判別是否為試用，是則繼續，否則表示為正式用戶
                if (customerServiceInfo.getContractStartDate() != null && !"".equals(customerServiceInfo.getContractStartDate()) && customerServiceInfo.getContractExprityDate() != null && !"".equals(customerServiceInfo.getContractExprityDate())) {
                    Date nowDate = new Date();
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                    String nowDateStr = dateFormat.format(nowDate);
                    if (nowDateStr.compareTo(customerServiceInfo.getContractStartDate()) >= 0 && nowDateStr.compareTo(customerServiceInfo.getContractExprityDate()) <= 0) {
                        //合約未到期
                        return true;
                    } else {
                        //合約到期
                        log.info("客戶合約到期，serviceCode:" + serviceCode + "  productCode:" + productCode);
                        return false;
                    }
                } else {
                    //無合約截止日期，則表示無合約或合約到期
                    log.info("找不到客戶合約日期訊息，serviceCode:" + serviceCode + "  productCode:" + productCode);
                    return false;
                }
            } else {
                log.info("找不到客戶合約訊息，serviceCode:" + serviceCode + "  productCode:" + productCode);
                return false;
            }
        } catch (Exception ex) {
            log.error(ex);
            return false;
        }
    }

    @Override
    public boolean CheckCustomerContractExist(String serviceCode, String productCode) {
        CustomerServiceInfo info = customerDao.SelectCustomerServiceInfo(serviceCode, productCode);
        return ObjectUtils.isEmpty(info) ? false : true;
    }
    /**
     * 存儲預警項編號
     *
     * @param issue
     */
    @Override
    public void batchInsertWarningId(Issue issue) {
        issueDao.batchInsertWarningId(issue.getIssueId(), issue.getCrmId(), issue.getWarningIdCollection());
    }


    /**
     * 更新預警項
     *
     * @param issue
     */
    @Override
    public void updateWarningNoticeStatus(Issue issue) {
        //更新預警編號, 與立案編號的關係
        issueDao.updateWarningIdGroup(issue.getIssueId(), issue.getCrmId());

        if (issue.getSubmitWay().startsWith(IssueSubmitMode.AIOSSM.toString()) || issue.getSubmitWay().equals(IssueSubmitMode.AIEOM.toString())) {
            //處理跟這個 ISSUE 有關的 預警編號.
            List<String> data = Optional.ofNullable(issueDao.getWarningIdByCrmId(issue.getCrmId())).orElse(new ArrayList<>());
            //兼容T的已完成状态
            if ("7".equals(issue.getIssueStatus()) || "10".equals(issue.getIssueStatus())) {
                issue.setIssueStatus(IssueStatus.Closed.toString());
            }
            data.forEach(warningkey -> this.updateWarningNoticeStatus_bigData(warningkey, issue.getIssueStatus(), issue.getCrmId()));
        } else if (issue.getSubmitWay().equals(IssueSubmitMode.ITMS_Service.toString()) || issue.getSubmitWay().equals(IssueSubmitMode.ITMS.toString())) {
            //兼容舊版
            this.updateWarningNoticeStatus(issue.getWarningId(), issue.getIssueStatus(), issue.getCrmId());

            //處理跟這個 ISSUE 有關的 預警編號.
            List<String> data = Optional.ofNullable(issueDao.getWarningIdByCrmId(issue.getCrmId())).orElse(new ArrayList<>());
            data.forEach(warningId -> this.updateWarningNoticeStatus(warningId, issue.getIssueStatus(), issue.getCrmId()));
        }
    }

    @Override
    public void updateWarningNoticeStatus_bigData(Issue issue) {
        //處理跟這個 ISSUE 有關的 預警編號.
        List<String> data = Optional.ofNullable(issueDao.getWarningIdByCrmId(issue.getCrmId())).orElse(new ArrayList<>());
        data.forEach(warningId -> this.updateWarningNoticeStatus(warningId, issue.getIssueStatus(), issue.getCrmId()));
    }

    /**
     * 更新預警項
     *
     * @param crmId
     */
    @Async
    public void updateWarningNoticeStatus(String crmId) {
        Optional.ofNullable(issueSyncDao.SelectIssue(crmId)).ifPresent(issue -> this.updateWarningNoticeStatus(issue));
    }

    /**
     * 更新預警項
     *
     * @param warningId
     * @param issueStatus
     */
    private void updateWarningNoticeStatus(String warningId, String issueStatus, String crmId) {
        warningId = Optional.ofNullable(warningId).orElse("");
        if (warningId.length() == 0) {
            return;
        } else {
            String url = "http://esclientservice/warning/machine/notice/process/status";
            Map<String, String> m = new HashMap<>();
            m.put("warningNotificationId", warningId);

            if (IssueStatus.Closed.isSame(issueStatus)) {
                m.put("processStatus", "closeIssue");
            } else if (IssueStatus.Superseded.isSame(issueStatus)) {
                // 修改预警状态为 unsolved 方便后续继续立案
                m.put("processStatus", "unsolved");
            } else {
                m.put("processStatus", "createIssue");
            }
            m.put("remark", "");
            m.put("crmId", crmId);
            restTemplate.patchForObject(url, Arrays.asList(m), Void.class);
        }
    }

    /**
     * 更新預警項
     *
     * @param warningItemkey
     * @param issueStatus
     * @param crmId
     */
    private void updateWarningNoticeStatus_bigData(String warningItemkey, String issueStatus, String crmId) {
        warningItemkey = Optional.ofNullable(warningItemkey).orElse("");
        if (warningItemkey.length() == 0) {
            return;
        } else {
            //"https://ddp-dataapi.digiwincloud.com.cn/hbase/save";
            String url = bigDataUrl + "hbase/save";

            HashMap<String, Object> map = new HashMap<>();
            map.put("tableName", "warning");
            map.put("rowKey", warningItemkey);
            map.put("cf", "info");
            HashMap<String, Object> dataMap = new HashMap<>();
            if (IssueStatus.Closed.isSame(issueStatus)) {
                dataMap.put("status", "solved");
            } else if (IssueStatus.Superseded.isSame(issueStatus)) {
                dataMap.put("status", "unsolved");
            } else {
                dataMap.put("status", "createIssue");
            }
            dataMap.put("crmId", crmId);
            map.put("data", dataMap);
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.postForObject(url, map, HashMap.class);
        }
    }

    /**
     * 更新事件狀態
     *
     * @param crmId
     */
    @Async
    public void updateEventNoticeStatus(String crmId) {
        String EDREventIssue = issueSyncDao.checkIssueIsEdrEvent(crmId);
        if (!StringUtils.isEmpty(EDREventIssue)) {
            Optional.ofNullable(issueSyncDao.SelectIssue(crmId)).ifPresent(issue -> this.updateEventNoticeStatus(issue));
        }

    }

    public void insertEventNoticeStatus(Issue issue) {
        //更新事件編號, 與立案編號的關係
        //處理跟這個 ISSUE 有關的 事件編號.
        List<String> data = Optional.ofNullable(issueDao.getEdrEventIdByIssueId(issue.getIssueId())).orElse(new ArrayList<>());
        data.forEach(eventId_serverId -> this.insertEventNoticeStatus(eventId_serverId, issue.getIssueStatus(), issue.getCrmId(), issue.getServiceCode(), defaultSid));
    }

    /**
     * 更新事件狀態
     *
     * @param issue
     */
    @Override
    public void updateEventNoticeStatus(Issue issue) {
        //更新事件編號, 與立案編號的關係
        //處理跟這個 ISSUE 有關的 事件編號.
        List<String> data = Optional.ofNullable(issueDao.getEdrEventIdByIssueId(issue.getIssueId())).orElse(new ArrayList<>());
        data.forEach(eventId_serverId -> this.updateEventNoticeStatus(eventId_serverId, issue.getIssueStatus(), issue.getCrmId(), issue.getServiceCode(), defaultSid));
    }

    @Override
    public void updateEventNoticeStatus(IssueStatusSyncData issue) {
        //更新事件編號, 與立案編號的關係
        //處理跟這個 ISSUE 有關的 事件編號.
        List<String> data = Optional.ofNullable(issueDao.getEdrEventIdByCrmId(issue.getCrmId())).orElse(new ArrayList<>());
        data.forEach(eventId_serverId -> this.updateEventNoticeStatus(eventId_serverId, issue.getIssueStatus(), issue.getCrmId(), issue.getServiceCode(), defaultSid));
    }


    private void insertEventNoticeStatus(String eventId_serverId, String issueStatus, String crmId, String serviceCode, long sid) {
        eventId_serverId = Optional.ofNullable(eventId_serverId).orElse("");
        if (eventId_serverId.length() == 0) {
            return;
        } else {
            // 因立案可以是不同的客服代號，所以serverId不一定等於serviceCode對應的serverId，所以調整所傳的Id有包含其serviceId，並以_做區分(evendId_serviceId)
            String[] list = eventId_serverId.split("_");
            String eventId = "";
            String serverId = "";
            if (list.length > 1) {
                eventId = list[0];
                serverId = list[1];
            }
            // 藉由servcieCode找出serviceId
            //String serverId = issueDao.getServerId(aioDBName,serviceCode, sid);
            if (!StringUtils.isEmpty(serverId)) {
                //藉由serviceId、eventId找出id，若沒有就要產生id
                Long id = issueDao.getIdByEventId(aioDBName, serverId, eventId, sid);
                if (null == id) {
                    id = SnowFlake.getInstance().newId();
                }
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("aioDBName", aioDBName);
                map.put("id", id);
                if (IssueStatus.Closed.isSame(issueStatus)) {
                    map.put("status", "solved");
                } else if (IssueStatus.Superseded.isSame(issueStatus)) {
                    map.put("status", "invalided");
                } else {
                    map.put("status", "createIssue");
                }
                map.put("sid", sid);
                map.put("serverId", serverId);
                map.put("eventId", eventId);
                map.put("crmId", crmId);

                issueDao.insertEventIssueStatus(map);

                id = SnowFlake.getInstance().newId();
                map.put("id", id);
                issueDao.insertEventDetailIssueStatus(map);
            } else {
                log.error("serviceId is empty from eventId_serverId:" + eventId_serverId + " sid:" + sid);
            }
        }
    }

    /**
     * 更新事件
     *
     * @param eventId_serverId
     * @param issueStatus
     * @param crmId
     */
    private void updateEventNoticeStatus(String eventId_serverId, String issueStatus, String crmId, String serviceCode, long sid) {
        eventId_serverId = Optional.ofNullable(eventId_serverId).orElse("");
        if (eventId_serverId.length() == 0) {
            return;
        } else {
            // 因立案可以是不同的客服代號，所以serverId不一定等於serviceCode對應的serverId，所以調整所傳的Id有包含其serviceId，並以_做區分(evendId_serviceId)
            String[] list = eventId_serverId.split("_");
            String eventId = "";
            String serverId = "";
            if (list.length > 1) {
                eventId = list[0];
                serverId = list[1];
            }
            // 藉由servcieCode找出serviceId
            //String serverId = issueDao.getServerId(aioDBName,serviceCode, sid);
            if (!StringUtils.isEmpty(serverId)) {
                //藉由serviceId、eventId找出id，若沒有就要產生id
                Long id = issueDao.getIdByEventId(aioDBName, serverId, eventId, sid);
                if (null == id) {
                    id = SnowFlake.getInstance().newId();
                }
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("aioDBName", aioDBName);
                map.put("id", id);
                if (IssueStatus.Closed.isSame(issueStatus)) {
                    map.put("status", "solved");
                } else if (IssueStatus.Superseded.isSame(issueStatus)) {
                    map.put("status", "invalided");
                } else {
                    map.put("status", "createIssue");
                }
                map.put("sid", sid);
                map.put("serverId", serverId);
                map.put("eventId", eventId);
                map.put("crmId", crmId);

                issueDao.insertEventIssueStatus(map);
                issueDao.updateEventDetailIssueStatus(map);
            } else {
                log.error("serviceId is empty from eventId_serverId:" + eventId_serverId + " sid:" + sid);
            }
        }
    }

    @Override
    public boolean updateIssueService(String userId, String issueId, String serviceId, String productCode) {
        String issueSyncStatus = issueDao.getIssueSyncStatus(issueId);
        issueSyncStatus = SyncStatus.SyncSuccess.toString().equals(issueSyncStatus) || SyncStatus.WaitSync.toString().equals(issueSyncStatus) ? SyncStatus.EditUnSync.toString() : issueSyncStatus;
        StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(serviceId);
        boolean result = issueDao.updateIssueService(issueId, serviceId, issueSyncStatus, staffUserInfo.getDepartment(), productCode);

        if (result) {
            try {
                Issue oldIssue = GetIssueProgress(issueId, userId, "");
                String[] mx = {PrepareIssueMailToNewCC(userId, oldIssue)};
                simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            } catch (Exception ex) {
                log.error(ex.toString());
            }

        }
        return result;
    }

    /**
     * 组织发送给新客服CC的邮件（变更处理人）
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMailToNewCC(String userId, Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceId());//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());
            System.out.print("In PrepareIssueMailToNewCC");
            System.out.print(staffUserInfo.getEmail());
            System.out.print("In Staff TimeZone");
            System.out.print(staffUserInfo.getTimeZone());

            StaffUserInfo oldProcessor = userService.GetServiceStaffInfoByUserId(userId);//旧的处理人
            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0; //默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
//            String formatToCCMailStr = ToCCMailStr;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html", language);

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToNewCC.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            boolean hasAttachment = false;
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                hasAttachment = true;
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToNewCC.GetIssueAttachmentFiles " + issueId);
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 19),
                    userContact.getName(), userContact.getEmail(), phone, StringUtils.isEmpty(issue.getCcUser()) ? "" : issue.getCcUser(), attachementStr,
                    issueDescription);

            Mail mail02 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(MessageUtils.ZH_CN_STANDARD, languageStandard)) {
                //【鼎捷服务云】您有新案件
                subject = mailUtils.getIssueMailSubjectForChangeCC(issue.isEmergency() ? "IssueSubmitEmergencyMailToCC" : "IssueSubmitMailToCC", language, oldProcessor, hasAttachment);

            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubjectForChangeCC(issue.isEmergency() ? "IssueSubmitEmergencyMailToCC" : "IssueSubmitMailToCC", language, oldProcessor, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
            }

            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    @Override
    @Transactional
    public boolean cancelCloseIssue(String issueId, long progressId) {
        Issue issue = issueDao.SelectIssueByIssueId(issueId, "","");

        String issueSyncStatus = issueDao.getIssueSyncStatus(issueId);
        issueSyncStatus = SyncStatus.SyncSuccess.toString().equals(issueSyncStatus) || SyncStatus.WaitSync.toString().equals(issueSyncStatus) ? SyncStatus.EditUnSync.toString() : issueSyncStatus;
        boolean resIssue = issueDao.cancelCloseIssue(issueId, issueSyncStatus);
        boolean resProgress = false;
        if (resIssue) {
            //147 不同步，设置Z,其他保留默认值T
            if ("888".equals(issue.getProductCode()) || "147".equals(issue.getProductCode()) || "161".equals(issue.getProductCode()) || "157".equals(issue.getProductCode())) {
                issueSyncStatus = SyncStatus.DontNeedSync.toString();
            } else {
                //若原先案件同步狀態為不同步，則也要為不同步
                if (!SyncStatus.DontNeedSync.toString().equals(issueSyncStatus)) {
                    issueSyncStatus = SyncStatus.EditUnSync.toString();
                }
            }
            resProgress = issueDao.updateCurrentStatus(progressId, IssueStatus.Processing.toString(), issueSyncStatus);
        }

        if ("147".equals(issue.getProductCode())) {
            updateBigDataIssueStatus(issue.getCrmId());
        }
        //更新事件狀態
        String EDREventIssue = issueSyncDao.checkIssueIsEdrEvent(issue.getCrmId());
        if (!StringUtils.isEmpty(EDREventIssue)) {
            this.updateEventNoticeStatus(issue);
        }

        return resIssue && resProgress;
    }

    public void updateBigDataIssueStatus(String crmId) {
        Runnable runnable = () -> {
            updateWarningNoticeStatus(crmId);
        };
        commonUtils.asyncRun(runnable);
    }

    @Override
    @Transactional
    public boolean cancelClose(long issueId, String crmId, String userId) {
        String issueSyncStatus = issueDao.getIssueSyncStatus(String.valueOf(issueId));
        issueSyncStatus = SyncStatus.SyncSuccess.toString().equals(issueSyncStatus) || SyncStatus.WaitSync.toString().equals(issueSyncStatus) ? SyncStatus.EditUnSync.toString() : issueSyncStatus;
        boolean resIssue = issueDao.cancelCloseIssue(String.valueOf(issueId), issueSyncStatus);
        if (resIssue) {
            Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId), "",userId);
            //更新事件狀態
            String EDREventIssue = issueSyncDao.checkIssueIsEdrEvent(issue.getCrmId());
            if (!StringUtils.isEmpty(EDREventIssue)) {
                this.updateEventNoticeStatus(issue);
            }

            IssueProgress issueProgress = new IssueProgress();

            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            issueProgress.setProcessTime(sdf.format(new Date()));
            issueProgress.setCrmId(crmId);
            issueProgress.setCurrentStatus(IssueStatus.Processing.toString());
            issueProgress.setReplyType(ReplyType.A.toString());
            issueProgress.setProcessType(IssueProcessType.CancelClose.toString());
            issueProgress.setDescription("Cancel Close Issue");
            if ("CN".equals(issue.getServiceRegion())) {
                issueProgress.setDescription("取消结案");
            }
            issueProgress.setProcessor(userId);
            issueProgress.setSequeceNum(issueDao.getMaxOrder(issueId));

            //更新最新回复
            issueDetailServiceV3.updateIssueNewReply(String.valueOf(issue.getIssueId()), NewReplyEnum.ServiceNewReply.toString());

            return issueDao.InsertIssueProgress(issueId, issueProgress.getCrmId(), issueProgress) > 0;
        }
        return false;
    }

    @Override
    public boolean checkIssueIsSyncCrm(String serviceRegion, String productCode, String issueStatus) {
        Integer syncCRM = issueDao.checkIssueIsSyncCrm(serviceRegion, productCode, issueStatus);
        return syncCRM != null && syncCRM == 1;
    }

    /**
     * 由信箱找出UserId
     *
     * @param mail
     * @param serviceCode
     * @return
     */
    @Override
    public String findUserIdbyMail(String mail, String serviceCode) {
        String userId = issueDao.getUserIdbyMail(mail, serviceCode);
        return userId != null ? userId : "";
    }

    /**
     * 由客代找出公司電話
     *
     * @param serviceCode
     * @return
     */
    @Override
    public String findDefaultPhonebyServiceCode(String serviceCode) {
        String phone = issueDao.getDefaultPhonebyServiceCode(serviceCode);
        return phone != null ? phone : "";
    }

    /**
     * 由電話找出mail
     *
     * @param serviceCode
     * @param phone
     * @param extension
     * @param mobilePhone
     * @return
     */
    @Override
    public String findMailbyPhone(String serviceCode, String phone, String extension, String mobilePhone) {
        //整理電話格式，將特殊符號都移除
        String regEx = "[\\s\n`~!\\-@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。， 、？]";
        //可以在中括号内加上任何想要替换的字符，实际上是一个正規表达式
        String newString = "";//这里是将特殊字符换为newString字符串,""代表直接去掉

        //移除國際碼
        phone = phone.replaceAll("\\+81|\\+84|\\+86|\\+60|\\+62|\\+66|\\+886", "");
        //移除特殊符號
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(phone);
        phone = m.replaceAll(newString);
        //phone = phone.replaceAll(regEx,newString);
        extension = extension.replaceAll("\\+81|\\+84|\\+86|\\+60|\\+62|\\+66|\\+886", "");//replace("+81","").replace("+84","").replace("+86","").replace("+60","").replace("+62","").replace("+66","").replace("+886","");
        Matcher e = p.matcher(extension);
        extension = e.replaceAll(newString);
        //extension = extension.replaceAll(regEx,newString);
        Matcher mp = p.matcher(mobilePhone);
        mobilePhone = mp.replaceAll(newString);
        //mobilePhone = mobilePhone.replaceAll(regEx,newString);

        String mail = issueDao.getMailbyPhone(serviceCode, phone, extension, mobilePhone);
        // 檢查所抓到的是不是mail格式
        String mailRegex = "^([a-z0-9A-Z\\u4e00-\\u9fa5]+[-|_#+!~$%&//.]?)+\\@[A-Za-z0-9]([\\.\\_\\-]?[A-Za-z0-9])+\\.+([A-Za-z])+$";
        Pattern mailPattern = Pattern.compile(mailRegex);

        if (mail == null || !mailPattern.matcher(mail).find()) {
            mail = issueDao.getMailbyPhoneFromMars_userpersonalinfo(serviceCode, phone, extension, mobilePhone);
        }
        return mail != null ? mail.trim() : "";
    }

    /**
     * 完善聯繫人資訊
     *
     * @return
     */
    @Override
    public void CompleteUserContact(Issue issue) {
        // 20220812 明助說會請大人物傳email，所以當有email時直接以此找userId(若找不到就不管)，若無email或不符合email格式就藉由電話查找email與userId

        //檢查email是否符合格式  若符合，則找userId  若不符合則由電話找email與userId
        String regex = "^([a-z0-9A-Z\\u4e00-\\u9fa5]+[-|_#+!~$%&//.]?)+\\@[A-Za-z0-9]([\\.\\_\\-]?[A-Za-z0-9])+\\.+([A-Za-z])+$";

        Pattern p = Pattern.compile(regex);
        if (p.matcher(issue.getUserContact().getEmail()).find()) {
            // 表示有符合的email
            UserContact uc = issue.getUserContact();
            // 找出userId
            String mail = issue.getUserContact().getEmail();
            String userId = issueDao.getUserIdbyMail(mail, issue.getServiceCode());
            if (StringUtils.isNotBlank(userId)) { //huly: 修复漏洞/bug StringUtils.isNotBlank
                uc.setUserId(userId);
                issue.setUserId(userId);
            }
            issue.setUserContact(uc);
        } else {
            String mail = findMailbyPhone(issue.getServiceCode(), issue.getUserContact().getPhone01(), issue.getUserContact().getExtension(), issue.getUserContact().getPhone02());
            if (StringUtils.isNotBlank(mail)) { //huly: 修复漏洞/bug StringUtils.isNotBlank
                UserContact uc = issue.getUserContact();
                if (p.matcher(mail).find()) {
                    uc.setEmail(mail);
                } else {
                    uc.setEmail("<EMAIL>");
                }
                // 找出userId
                String userId = issueDao.getUserIdbyMail(mail, issue.getServiceCode());
                if (StringUtils.isNotBlank(userId)) { //huly: 修复漏洞/bug StringUtils.isNotBlank
                    uc.setUserId(userId);
                    issue.setUserId(userId);
                }
                issue.setUserContact(uc);
            } else {
                UserContact uc = issue.getUserContact();
                uc.setEmail("<EMAIL>");
                issue.setUserContact(uc);
            }
        }
    }

    /**
     * 获取案件列表
     *
     * @return
     */
    @Override
    public IssueSourceMapResult getIssueSourceMapList(String issueStatus, String sourceType, String sourceId, String userId) {

        Map<String, Object> map = new HashMap<>();
        map.put("issueStatus", issueStatus);
        map.put("sourceType", sourceType);
        map.put("sourceId", sourceId);
        map.put("userId", userId);
        map.put("connectArea", connectArea);

        log.info(">>>>>========================查询案件开始======================<<<<<");
        List<Issue> issueList = issueDao.getIssueSourceMapList(map);
        long count = issueDao.getIssueSourceMapCount(map);

        IssueSourceMapResult issueSourceMapResult = new IssueSourceMapResult();
        issueSourceMapResult.setCount(count);
        issueSourceMapResult.setIssueList(issueList);
        log.info(">>>>>========================查询案件结束======================<<<<<");

        return issueSourceMapResult;
    }

    /**
     * 获取案件汇总量（处理中，已结案）
     *
     * @return
     */
    @Override
    public IssueSum getIssueSum(String userId, String department, String serviceCode) {
        log.info("userId=" + userId);
        log.info("department=" + department);
        log.info("serviceCode=" + serviceCode);
        Map<String, Object> map = new HashMap<>();
//        map.put("serviceCode",serviceCode);
        if (department == null || department.isEmpty()) {
            map.put("department", "");
            map.put("userId", userId);
        } else {
            map.put("department", department);
            map.put("userId", "");
        }
        return issueDao.getIssueSum(map);
    }

    @Override
    public Object updateSyncStatus4Test(String issueId) {
        return issueDao.updateSyncStatus4Test(issueId);
    }

    /**
     * 修改ProgressAndFile
     *
     * @param issue
     * @return
     */
    @Override
    public void updateIssueProgressAndFile(Issue issue) {
        try {
            Long issueId = issue.getIssueId();
            //保存附件
            SaveAttachments(issueId, issue, true);
            //.保存處理進度
            SaveIssueProgress(issue, true);
            //.修改案件状态为編輯後未同步状态
            UpdateSyncReleaseIssueStatus(issue, false);
            //UpdateSyncIssueStatus(issue, false);
            updateCaseDetailSyncStatus(issueId);
            //更新总工时
            issueDetailDao.updateDetailIssueTotalWorkHours(issueId);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 更新案件狀態為未同步
     */
    @Override
    public Object updateCaseDetailSyncStatus(long issueId) {
        return issueDao.updateCaseDetailSyncStatus(issueId);
    }

    /**
     * 获取案件列表
     *
     * @param serviceCode 客服代號
     * @param page        頁碼
     * @param size        大小
     * @param searchKey   搜尋字
     * @return
     */
    @Override
    public List<Issue> GetIssuesbyServiceCodeAndSearchKey(String serviceCode, int page, int size, String searchKey, String serviceRegion) {
        int start = (page - 1) * size;
        return issueDao.SelectIssueListbyServiceCodeAndSearchKey(serviceCode, start, size, searchKey, serviceRegion);
    }

    /**
     * 获取用户的所有案件数量
     *
     * @param serviceCode 客服代號
     * @param searchKey   搜尋字
     * @return
     */
    @Override
    public IssueCount GetIssueCountByServiceCodeAndSearchKey(String serviceCode, String searchKey, String serviceRegion) {
        return issueDao.SelectIssueCountByServiceCodeAndSearchKey(serviceCode, searchKey, serviceRegion);
    }

    /**
     * 获取CallCenter未同步的案件单单身单头列表
     *
     * @return
     */
    @Override
    public void SyncCallCenterIssues() {
        List<Issue> unSyncissueList = issueSyncDao.SelectUnSyncCallCenterIssueList();
        List<Issue> unSyncProgressList = issueSyncDao.SelectUnSyncCallCenterProgressList();
        Date date = new Date();
        DateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");
        DateFormat sdf3 = new SimpleDateFormat("HHmm");
        DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String submitDate = sdf2.format(date);
        String processTime = sdf3.format(date);

        //先同步未同步的單身
        for (Issue issue : unSyncProgressList) {
            System.out.print("Before do SyncCallCenterProgress" + issue.getCrmId());
            String workNo = issue.getServiceStaff();
            //查询CRM状态
            String BQ024 = issueDao.readCrmCaseStatus(issue.getCrmId());
            if (!StringUtils.isEmpty(BQ024)) {
                //crm存在
                for (IssueProgress issueProgress : issue.getIssueProgresses()) {
                    Date processDate = null;
                    try {
                        processDate = format1.parse(issueProgress.getProcessTime());
                        submitDate = sdf2.format(processDate);
                        processTime = sdf3.format(processDate);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    Boolean scpClosed = !"Y".equals(BQ024) && "Y".equals(issue.getIssueStatus());
                    Boolean crmClosed = "Y".equals(BQ024) && !"Y".equals(issue.getIssueStatus());
                    //写入Crm单身
                    String res = issueDao.InsertCrmCaseProcess(issue.getCrmId(), submitDate, processTime, workNo, issueProgress, scpClosed);
                    if (StringUtils.isBlank(res)) {
                        System.out.print("Before do SyncCallCenterProgress fail" + issue.getCrmId());
                        issueDao.UpdateIssueAdditionalExplanationSync(issue.getIssueId(), issueProgress.getId(), "E");
                        continue;
                    }
                    updateIssueProgress(issueProgress, res);
                    issueDao.UpdateIssueAdditionalExplanationSync(issue.getIssueId(), issueProgress.getId(), "Y");
                    if (scpClosed) {
                        System.out.print("Before do SyncCallCenterIssues" + issue.getCrmId());
                        boolean b = issueDao.updateCrmCase(issue.getCrmId(), submitDate, workNo, issue.getDepartment());
                        if (!b) {
                            issueDao.UpdateIssueAdditionalExplanationSync(issue.getIssueId(), issueProgress.getId(), "C");
                            System.out.print("Before do SyncCallCenterIssues fail" + issue.getCrmId());
                        }
                        continue;
                    }
                    //crm已結案 而雲管家未結案則更新單頭狀態
                    if (crmClosed) {
//                        insertProgess(issue, issue.getServiceId(), true, processTime, "Close Issue From SyncCrm", "", SyncStatus.DontNeedSync.toString());
                        //更云管家单头状态
                        issueDao.UpdateIssueStatusByStaff(issue.getIssueId(), IssueStatus.Closed.toString(), issue.getIssueStatus(), issue.getDepartment(), issue.getServiceId());
                        //更新回 influxDB 如果有預警編號的話
                        updateWarningNoticeStatus(issue.getCrmId());
                    }
                }
            }
        }
        //同步已結案狀態未同步
        for (Issue issue : unSyncissueList) {
            String workNo = issue.getServiceStaff();
            IssueProgress issueProgress = null;
            if (!CollectionUtils.isEmpty(issue.getIssueProgresses())) {
                issueProgress = getCloseProcess(issue.getIssueProgresses());
                if (issueProgress != null) {
                    Date processDate = null;
                    try {
                        processDate = format1.parse(issueProgress.getProcessTime());
                        submitDate = sdf2.format(processDate);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }

                }
                //同步前检查是否已存在crm
                long progressId = issueProgress != null ? issueProgress.getId() : 0L;
                String BQ024 = issueDao.readCrmCaseStatus(issue.getCrmId());
                if (StringUtils.isEmpty(BQ024)) {
                    //沒同步至CRM 則更新狀態為Z
                    if ("Z".equals(issue.getSyncStatus())) {
                        issueDao.UpdateIssueAdditionalExplanationSync(issue.getIssueId(), progressId, "Z");
                    }
                    continue;
                }
                //crm未結案雲管家已結案則更新結案狀態
                if (!"Y".equals(BQ024) && "Y".equals(issue.getIssueStatus())) {
                    System.out.print("Before do SyncCallCenterIssues" + issue.getCrmId());
                    boolean b = issueDao.updateCrmCase(issue.getCrmId(), submitDate, workNo, issue.getDepartment());
                    if (b) {
                        issueDao.UpdateIssueAdditionalExplanationSync(issue.getIssueId(), progressId, "Y");
                    } else {
                        System.out.print("Before do SyncCallCenterIssues fail" + issue.getCrmId());

                    }
                    continue;
                }
                //crm狀態已為已結案 雲管家還未則修改雲管家狀態則不再同步
                if ("Y".equals(BQ024) && !"Y".equals(issue.getIssueStatus())) {
//                    insertProgess(issue, issue.getServiceId(), true, processTime, "Close Issue From SyncCrm", "", SyncStatus.DontNeedSync.toString());
                    //更云管家单头状态
                    issueDao.UpdateIssueStatusByStaff(issue.getIssueId(), IssueStatus.Closed.toString(), issue.getIssueStatus(), issue.getDepartment(), issue.getServiceId());
                    //更新回 influxDB 如果有預警編號的話
                    updateWarningNoticeStatus(issue.getCrmId());

                    issueDao.UpdateIssueAdditionalExplanationSync(issue.getIssueId(), progressId, "Z");
                    continue;
                }
                //若都非以上情況則修改狀態z 略過
                issueDao.UpdateIssueAdditionalExplanationSync(issue.getIssueId(), progressId, "Z");
            }


        }
    }

    private IssueProgress getCloseProcess(List<IssueProgress> issueProgressList) {
        IssueProgress issueProgress = null;
        for (IssueProgress p : issueProgressList) {
            if (Objects.isNull(issueProgress)) { //202412 代码稽核 ObjectUtils.isEmpty(issueProgress) 改成 Objects.isNull(issueProgress)
                issueProgress = p;
                continue;
            }
            if (p.getId() > issueProgress.getId()) {
                issueProgress = p;
            }
        }
        return issueProgress;
    }

    @Override
    public List<Issue> issueFullContentSearch(String msgContent, String userId, String serviceCode, String productCode, String localId) {
        return issueDao.issueFullContentSearch(msgContent, userId, serviceCode, productCode, localId);
    }

    @Override
    public long saveShipment(String serviceCode, String productCode, String shipmentNum, String orderName, MultipartFile multipartFile) {
        long shipmentSid = saveShipmenInfo(serviceCode, productCode, shipmentNum, orderName);
        if (shipmentSid > 0 && multipartFile != null) {
            saveAuthorizedFile(shipmentSid, multipartFile);
        }
        return shipmentSid;
    }

    @Override
    public long saveShipment_SFT(String serviceCode, String productCode, String shipmentNum, String orderName, String serial) {
        long shipmentSid = saveShipmenInfo(serviceCode, productCode, shipmentNum, orderName);
        if (StringUtil.isNotEmpty(serial)) {
            saveAuthorizedSerail(shipmentSid, serial.split(","));
        }
        return shipmentSid;
    }

    private long saveShipmenInfo(String serviceCode, String productCode, String shipmentNum, String orderName) {
        long shipmentSid = 0L;
        try {
            String customerCode = issueDao.getCustomerCodeByServiceCode(serviceCode);
            Map<String, Object> map = new HashMap<>();
            map.put("serviceCode", serviceCode);
            map.put("customerCode", customerCode);
            map.put("productCode", productCode);
            map.put("remark", shipmentNum);
            LocalDateTime now = DateUtil.getLocalNow();
            map.put("shipmentTime", DateUtil.getSomeDateFormatString(now, DateUtil.DATE_TIME_FORMATTER));
            HashMap resultMap = cloudIssueService.saveShipmentForTT(map);
            if (resultMap == null || resultMap.get("code") == null || resultMap.get("sid") == null || !"0".equals(resultMap.get("code").toString())) {
                log.error("saveShipmenInfo error");
                return shipmentSid;
            }
            return Long.valueOf(resultMap.get("sid").toString());
        } catch (Exception e) {
            log.error("" + e.toString());
        }
        return shipmentSid;
    }

    private void saveAuthorizedFile(long shipmentSid, MultipartFile multipartFile) {
        try {
            HashMap resultMap = cloudIssueService.saveAuthorizedFile(shipmentSid, connectArea, multipartFile);
            if (resultMap == null || resultMap.get("code") == null || !"0".equals(resultMap.get("code").toString())) {
                log.error("saveAuthorizedFile error");
            }
        } catch (Exception e) {
            log.error("" + e.toString());
        }
    }

    private void saveAuthorizedSerail(long shipmentSid, String[] serail) {
        try {
            if (serail != null) {
                Map<String, Object> map = new HashMap<>();
                map.put("shipmentSid", shipmentSid);
                map.put("serail", serail);
                issueDao.saveAuthorizedSerail(map);
            }
        } catch (Exception e) {
            log.error("saveAuthorizedSerail fail:" + e.toString());
        }
    }

    private void updateAuthorizedSerail(long shipmentSid, List<ShipmentInfoSerial> serail) {
        try {
            if (CollectionUtil.isNotEmpty(serail)) {
                serail.stream().forEach(k -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("shipmentSid", shipmentSid);
                    map.put("serial", k.getSerial());
                    map.put("status", k.getStatus());
                    map.put("remark", k.getRemark());
                    issueDao.updateAuthorizedSerail(map);
                });
            }
        } catch (Exception e) {
            log.error("updateAuthorizedSerail fail:" + e.toString());
        }
    }

    @Override
    public UserPersonalInfo getUserInfoForShipment(String serviceCode, String orderName) {
        Map<String, Object> map = new HashMap<>();
        map.put("serviceCode", serviceCode);
        map.put("orderName", orderName);
        return userDao.getUserInfoForShipment(map);
    }

    @Override
    public BaseResponse addServiceContact(IssueProgress issueProgress) {
        try {
            //寫一筆單身(需做案件單身同步至客服CRM)
            long progressId = insertIssueProgress(issueProgress);
            //寫入表issue_service_contact中
            insertIssueServiceContact(progressId, issueProgress);
            //更新最新回复
            issueDetailServiceV3.updateIssueNewReply(String.valueOf(issueProgress.getIssueId()), NewReplyEnum.ServiceNewReply.toString());
            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("addServiceContact error issueId:" + issueProgress.getIssueId(), e);
        }
        return BaseResponse.error(ResponseStatus.INSERT_FAILD);
    }

    @Override
    public BaseResponse getServiceContacts(long issueId) {
        try {
            return BaseResponse.ok(issueDetailDao.getServiceContacts(issueId));
        } catch (Exception e) {
            log.error("getServiceContact error issueId:" + issueId, e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }

    @Override
    public BaseResponse getSelfUpdateSystemFiles(long issueId, String invalidStatus) {
        try {
            return BaseResponse.ok(issueDetailDao.getSelfUpdateSystemFiles(issueId, invalidStatus));
        } catch (Exception e) {
            log.error("getSelfUpdateSystemFiles error issueId:" + issueId, e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }

    @Override
    public BaseResponse getSelfUpdateSystemFileLogs(long isusfId, String isClient) {
        try {
            return BaseResponse.ok(issueDetailDao.getSelfUpdateSystemFileLogs(isusfId, isClient));
        } catch (Exception e) {
            log.error("getSelfUpdateSystemFileLogs error isusfId:" + isusfId, e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }

    @Override
    public BaseResponse insertSelfUpdateSystemLog(long issueId, IssueSelfUpdateSystemLog issueSelfUpdateSystemLog) {
        try {
            //新增一笔log
            issueSelfUpdateSystemLog.setIssueId(issueId);
            issueDetailDao.insertIssueSelfUpdateSystemLog(issueSelfUpdateSystemLog);

            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("getSelfUpdateSystemFileLogs error issueId:" + issueId, e);
        }
        return BaseResponse.error(ResponseStatus.INSERT_FAILD);
    }

    @Override
    @Transactional
    public BaseResponse sftCheck(String crmId, int approve, String maderWorkNo, String description) {
        int count = issueDao.getCountByCrmId(crmId);
        if (count == 0) {
            return BaseResponse.error(ResponseStatus.ISSUE_DETAIL_IS_NULL);
        }
        String issueStatus = issueDao.getIssueStatus(crmId);
        if (IssueStatus.Closed.toString().equals(issueStatus)) {
            return BaseResponse.error(ResponseStatus.ISSUE_IS_CLOSE);
        }
        String date = DateFormatUtils.format(new Date(), DateUtils.DEFAULT_FORMAT);

        IssueSelfUpdateSystemFile issueSelfUpdateSystemFile = issueDetailDao.getIssueSelfUpdateSystemFile(crmId);
        issueSelfUpdateSystemFile.setOperationTime(date);
        //案件新增一笔单身
        IssueProgress progress = new IssueProgress();
        IssueSelfUpdateSystemLog log = new IssueSelfUpdateSystemLog();

        log.setIssueId(issueSelfUpdateSystemFile.getIssueId());
        log.setCrmId(crmId);
        log.setTypeId(TypeName.SFT_RE_AUTH_CHECK.getStatus());
        log.setIsusfId(issueSelfUpdateSystemFile.getId());
        log.setOperationTime(date);

        StaffUserInfo staffUserInfo = userDao.SelectStaffByWorkNo(maderWorkNo);
        if (staffUserInfo != null) {
            issueSelfUpdateSystemFile.setOperationUser(staffUserInfo.getFullName());
            log.setOperationUser(staffUserInfo.getFullName());
            log.setOperationUserId(staffUserInfo.getUserId());
            progress.setWorkno(maderWorkNo);
            progress.setProcessor(staffUserInfo.getUserId());
        }

        if (1 == approve) {
            issueSelfUpdateSystemFile.setOperationStatus("PASS");
            issueSelfUpdateSystemFile.setOperationResultDes(OperationResult.SFT_CHECK_PASS.toString());
            issueSelfUpdateSystemFile.setCheckerWorkNo(maderWorkNo);
            issueSelfUpdateSystemFile.setAuthStatus(AuthStatus.checkPass.getValue());
            log.setOperationStatus("PASS");
            log.setOperationResultDes(OperationResult.SFT_CHECK_PASS.toString());
            progress.setDescription("【產品序號更新狀態】：同意序號重查,請您按下[更新序號]按鈕進行重新授權。");
        } else {
            issueSelfUpdateSystemFile.setOperationStatus("REJECT");
            issueSelfUpdateSystemFile.setOperationResultDes(OperationResult.SFT_CHECK_REJECT.toString());
            issueSelfUpdateSystemFile.setCheckerWorkNo(maderWorkNo);
            issueSelfUpdateSystemFile.setAuthStatus(AuthStatus.checkNotPass.getValue());
            log.setOperationStatus("REJECT");
            log.setOperationResultDes(OperationResult.SFT_CHECK_REJECT.toString());
            progress.setDescription("【產品序號更新狀態】：內部審核不通過(" + date + ")，請洽服務人員。");
        }
        progress.setIssueId(issueSelfUpdateSystemFile.getIssueId());
        progress.setCrmId(crmId);
        progress.setSequeceNum(issueDao.getMaxOrder(issueSelfUpdateSystemFile.getIssueId()) + 1);
        progress.setProcessType(IssueProcessType.Process.toString());
        progress.setProcessTime(date);
        progress.setDescription(description);
        progress.setOpenToSubmit(true);
        progress.setSyncStatus("P");
        progress.setReplyType(ReplyType.Q.toString());
        issueProcessMapper.insertIssueProgress(progress);
        issueDetailDao.insertIssueSelfUpdateSystemLog(log);
        issueDetailDao.updateIssueSelfUpdateSystemFile(issueSelfUpdateSystemFile);

        //标记客服最新回复
        issueDetailServiceV3.updateIssueNewReply(String.valueOf(issueSelfUpdateSystemFile.getIssueId()), NewReplyEnum.CustomerNewReply.toString());

        return BaseResponse.ok();
    }

    /**
     * 程式更新等操作
     *
     * @param issueId
     * @param isusfId
     * @param issueSelfUpdateSystemLogReq
     * @return
     */
    @Override
    @Transactional
    public BaseResponse insertIssueSelfUpdateSystemLog(long issueId, long isusfId, String userId, IssueSelfUpdateSystemLogReq issueSelfUpdateSystemLogReq) {
        if (ObjectUtils.isEmpty(issueSelfUpdateSystemLogReq.getIssueSelfUpdateSystemFile())
                || ObjectUtils.isEmpty(issueSelfUpdateSystemLogReq.getIssueSelfUpdateSystemLog())) {
            BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }
        IssueSelfUpdateSystemFile issueSelfUpdateSystemFile = issueSelfUpdateSystemLogReq.getIssueSelfUpdateSystemFile();
        IssueSelfUpdateSystemLog issueSelfUpdateSystemLog = issueSelfUpdateSystemLogReq.getIssueSelfUpdateSystemLog();
        IssueProgress progress = issueSelfUpdateSystemLogReq.getIssueProgress();
        if (progress == null) progress = new IssueProgress();

        //作废时，如果程式更新有记录操作时间，即issue_self_update_system。operationTime有值时，不允许作废
        if (OperationResult.INVALID.toString().equals(issueSelfUpdateSystemLog.getOperationResultDes()) && isusfId > 0L) {
            IssueSelfUpdateSystemFile file = issueDetailDao.getSelfUpdateSystemFile(isusfId);
            if (file != null && StringUtils.isNotBlank(file.getOperationTime())) {
                return BaseResponse.error(ResponseStatus.NOT_ALLOW_INVALID);
            }

        }
        //新增一笔log
        issueSelfUpdateSystemLog.setIssueId(issueId);
        issueSelfUpdateSystemLog.setIsusfId(isusfId);
        issueDetailDao.insertIssueSelfUpdateSystemLog(issueSelfUpdateSystemLog);
        //更新序号组状态
        switch (issueSelfUpdateSystemFile.getUpdateType()) {
            case "AUTHINSTALL": { //无须处理
                break;
            }
            case "SFT_AUTHINSTALL": {//需要更新授权状态,不管是全部更新完成还是部分更新完成，都需要更新状态
                if (OperationResult.UPDATE_SUCCESS_MUST_AGREE.toString().equals(issueSelfUpdateSystemFile.getOperationResultDes())
                        || OperationResult.UPDATE_FAIL.toString().equals(issueSelfUpdateSystemFile.getOperationResultDes())) {
                    updateAuthorizedSerail(issueSelfUpdateSystemFile.getShipmentSid(), issueSelfUpdateSystemFile.getSerial());
                }
                break;
            }
            case "SFT_RE_AUTHINSTALL": {//无须处理
                break;
            }
            default:
        }
        if (isusfId == 0L) {
            //新增
            issueSelfUpdateSystemFile.setIssueId(issueId);
            issueDetailDao.insertIssueSelfUpdateSystemFile(issueSelfUpdateSystemFile);
        } else {
            //更新单头信息
            issueSelfUpdateSystemFile.setId(isusfId);
            issueSelfUpdateSystemFile.setIssueId(issueId);
            issueDetailDao.updateIssueSelfUpdateSystemFile(issueSelfUpdateSystemFile);
        }

        //案件新增一笔单身
        progress.setIssueId(issueId);
        progress.setSequeceNum(issueDao.getMaxOrder(issueId) + 1);
        issueProcessMapper.insertIssueProgress(progress);

        if (IssueStatus.Closed.toString().equals(progress.getCurrentStatus())) { //如果结案，需要更新单头
            issueDao.updateIssueStatus(issueId, IssueStatus.Closed.toString(), "H");
            //结案清空最新回复
            issueDetailServiceV3.updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.Null.toString());
        } else {
            //不是结案的时候，根据replyType来判定，A：客服最新回复 ;Q: 客户最新回复；
            if (ReplyType.A.toString().equals(progress.getReplyType())) {
                issueDetailServiceV3.updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.ServiceNewReply.toString());
            } else if (ReplyType.Q.toString().equals(progress.getReplyType())) {
                issueDetailServiceV3.updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.CustomerNewReply.toString());
            } else {

            }
        }
        //发送邮件
        Boolean sendMail = issueDao.isSendMailByTypeId(issueSelfUpdateSystemLog.getTypeId());
        if (sendMail != null && sendMail.booleanValue() == true) {
            //todo  :发送结案邮件 客戶、處理人以及業務人員  秘书 制作人
            String description = progress.getDescription();
            String issueStatus = progress.getCurrentStatus();
            String updateType = issueSelfUpdateSystemFile.getUpdateType();
            Runnable runnable = () -> sendMailBySelfUpdateSystem(issueId, isusfId, userId, issueStatus, updateType, description, issueSelfUpdateSystemLog.getTypeId());
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            try {
                executorService.execute(runnable);
            } catch (Exception ex) {
                log.error("asyncRun", ex);
            } finally {
                executorService.shutdown();
            }
        }
        return BaseResponse.ok();
    }

    public void sendMailBySelfUpdateSystem(long issueId, long isusfId, String userId, String issueStatus, String updateType, String description, int typeId) {
        //发送邮件和通知给客服
        try {
            LocalDateTime now = DateUtil.getLocalNow();
            String unsignedDateTimeStr = DateUtil.getSomeDateFormatString(now, DateUtil.UNSIGNED_DATE_TIME_FORMATTER); //sdf
            Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId), "",userId);
            issue.setIssueProgresses(issueDao.SelectIssueProgress(String.valueOf(issueId), userId));
            SendAdditionalExplanationBySelfUpdateSystem(issue, isusfId, unsignedDateTimeStr, issueStatus, updateType, description, typeId);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public BaseResponse updateServiceContactRead(long issueId, String userId) {
        try {
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(userId);
            String userName = (userPersonalInfo != null && StringUtil.isNotEmpty(userPersonalInfo.getName())) ? userPersonalInfo.getName() : "";
            return BaseResponse.ok(issueDetailDao.updateServiceContactRead(issueId, userId, userName));
        } catch (Exception e) {
            log.error("upateServiceContactRead error issueId:" + issueId, e);
        }
        return BaseResponse.error(ResponseStatus.UPDATE_FAILD);
    }

    @Override
    public BaseResponse updateSystemFileRead(long issueId, String userId) {
        try {
            return BaseResponse.ok(issueDetailDao.updateSystemFileRead(issueId, userId));
        } catch (Exception e) {
            log.error("updateSystemFileRead error issueId:" + issueId, e);
        }
        return BaseResponse.error(ResponseStatus.UPDATE_FAILD);
    }

    @Override
    public void replyToContact() {
        log.info("获取约定日为当天，在台湾crm客服有通知客户的留言 开始");
        List<AgreeDateReplyDetail> list = issueDetailDao.getNewAgreeDateReply();
        if (CollectionUtils.isEmpty(list)) {
            log.info("无约定日为当天，在台湾crm客服有通知客户的留言");
            return;
        }
        Runnable runnable = () -> replyToContact(list);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("asyncRun", ex);
        } finally {
            executorService.shutdown();
            log.info("获取约定日为当天，在台湾crm客服有通知客户的留言 结束");
        }

    }

    @Override
    public void boardcastIssueToService() {
        log.info("获取约定日为当天及明天的案件，并推播给客服人员和支援人员 开始");
        List<AgreeDateReplyDetail> list = issueDetailDao.getNewAgreeDateIssue();
        if (CollectionUtils.isEmpty(list)) {
            log.info("无约定日为当天及明天的案件，无需推播");
            return;
        }
        Runnable runnable = () -> boardcastIssueToService(list);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("asyncRun", ex);
        } finally {
            executorService.shutdown();
            log.info("获取约定日为当天及明天的案件，并推播给客服人员和支援人员 结束");
        }

    }

    public void boardcastIssueToService(List<AgreeDateReplyDetail> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        //分组查询当前案件处理人有哪些
        log.info("服务人员和支援人员总量：" + list.size());
        String today = DateUtil.getSomeDateFormatString(DateUtil.getLocalToday(), DateUtil.DATE_FORMATTER);//今天
        String tomorrow = DateUtil.getSomeDateFormatString(DateUtil.getLocalToday().minusDays(-1), DateUtil.DATE_FORMATTER);//明天
        String domain = openFireMessageService.getOpenFireServerInfo().getDomain();

        Map<String, List<AgreeDateReplyDetail>> serviceIdMap = list.stream()
                .filter(k -> StringUtils.isNotEmpty(k.getAgreeDateSubmiterId()))
                .collect(Collectors.groupingBy(AgreeDateReplyDetail::getAgreeDateSubmiterId));

        for (Map.Entry<String, List<AgreeDateReplyDetail>> entry : serviceIdMap.entrySet()) {
            try {
                // 处理人id
                String serviceId = entry.getKey();
                if (StringUtil.isEmpty(serviceId)) {
                    continue;
                }
                List<AgreeDateReplyDetail> allList = entry.getValue();//取的是serviceId的今天和明天的所有案件
                if (CollectionUtils.isEmpty(allList)) {
                    continue;
                }

                // 推播
                IssuePushMessage issueUnprocessMessage = new IssuePushMessage(domain);
                issueUnprocessMessage.setIssuePushType("agreed_date");
                issueUnprocessMessage.setToday_crmIds(allList.stream()
                        .filter(k -> today.equals(k.getExpectedCompletionDate()))
                        .map(k -> k.getCrmId())
                        .collect(Collectors.toList()));
                issueUnprocessMessage.setTomorrow_crmIds(allList.stream()
                        .filter(k -> tomorrow.equals(k.getExpectedCompletionDate()))
                        .map(k -> k.getCrmId())
                        .collect(Collectors.toList()));
                issueUnprocessMessage.setToUserJid(serviceId + "@" + domain);
                log.info("开始推播给用户：" + serviceId);
                openFireMessageService.sendMsg(issueUnprocessMessage);
            } catch (Exception ex) {
                log.error("boardcastIssueToService:", ex);
            }
        }
    }

    public void replyToContact(List<AgreeDateReplyDetail> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(agreeDateReplyDetail -> {
            try {
                //寫一筆單身(需做案件單身同步至客服CRM)
                long progressId = insertIssueProgress(agreeDateReplyDetail);
                //寫入表issue_service_contact中
                insertIssueServiceContact(progressId, agreeDateReplyDetail);
                //將表issue_casedetail中hasNewAgreeDateReply標示為false
                issueDetailDao.updateIssueCaseDetail(Long.valueOf(agreeDateReplyDetail.getIssueId()));

                //更新最新回复
                String issueStatus = issueDao.SelectIssueStatus(Long.valueOf(agreeDateReplyDetail.getIssueId()));
                if (IssueStatus.Closed.toString().equals(issueStatus) || IssueStatus.Evaluated.toString().equals(issueStatus)) {
                    //清空最新回复
                    issueDetailServiceV3.updateIssueNewReply(agreeDateReplyDetail.getIssueId(), NewReplyEnum.Null.toString());
                } else {
                    //标记客服最新回复
                    issueDetailServiceV3.updateIssueNewReply(agreeDateReplyDetail.getIssueId(), NewReplyEnum.ServiceNewReply.toString());
                }
            } catch (Exception e) {
                log.error("replyToContact error issueId:" + agreeDateReplyDetail.getIssueId(), e);
            }
        });
    }

    //寫入表issue_service_contact中
    public void insertIssueServiceContact(long progressId, IssueProgress progress) {
        IssueServiceContact issueServiceContact = new IssueServiceContact();
//        issueServiceContact.setIssueId(Long.valueOf(progress.getIssueId()));
        issueServiceContact.setIssueId(progress.getIssueId()); //修复bug  类型重复转换
        issueServiceContact.setDescription(progress.getDescription());
        issueServiceContact.setIssueProgressId(progressId);
        issueServiceContact.setSource(IssueServiceContactSource.SIM.toString());
        StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(progress.getProcessor());//(issue.getUserId());
        if (staffUserInfo != null) {
            issueServiceContact.setSubmiter(staffUserInfo.getFullName());//人名
        }
        issueDetailDao.insertIssueServiceContact(issueServiceContact);
    }

    //寫入表issue_service_contact中
    public void insertIssueServiceContact(long progressId, AgreeDateReplyDetail agreeDateReplyDetail) {
        IssueServiceContact issueServiceContact = new IssueServiceContact();
        issueServiceContact.setIssueId(Long.valueOf(agreeDateReplyDetail.getIssueId()));
        issueServiceContact.setDescription(agreeDateReplyDetail.getAgreeDateReply());
        issueServiceContact.setIssueProgressId(progressId);
        issueServiceContact.setSource(IssueServiceContactSource.CRM.toString());
        issueServiceContact.setSubmiter(agreeDateReplyDetail.getAgreeDateSubmiterName());//人名
        issueDetailDao.insertIssueServiceContact(issueServiceContact);
    }

    //寫入表issue_progress中
    public long insertIssueProgress(IssueProgress progress) {
        IssueProgress issueProgress = new IssueProgress();
//        issueProgress.setIssueId(Long.valueOf(progress.getIssueId()));
        issueProgress.setIssueId(progress.getIssueId());  //修复bug  类型重复转换
        issueProgress.setCrmId(progress.getCrmId());
//        issueProgress.setSequeceNum(issueDao.getMaxOrder(Long.valueOf(progress.getIssueId())) + 1);
        issueProgress.setSequeceNum(issueDao.getMaxOrder(progress.getIssueId()) + 1); //修复bug  类型重复转换
        issueProgress.setProcessType(IssueProcessType.Process.toString());
        issueProgress.setCurrentStatus(IssueStatus.Processing.toString());
        issueProgress.setProcessor(progress.getProcessor());
        StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issueProgress.getProcessor());//(issue.getUserId());
        if (staffUserInfo != null) {
            issueProgress.setWorkno(staffUserInfo.getWorkNo());
        }
        issueProgress.setDescription("【客服留言】" + progress.getDescription());
        issueProgress.setReplyType(ReplyType.A.toString());
        issueProgress.setSyncStatus("P");
        issueProcessMapper.insertIssueProgress(issueProgress);

        return issueProgress.getId();
    }

    //寫入表issue_progress中
    public long insertIssueProgress(AgreeDateReplyDetail agreeDateReplyDetail) {
        IssueProgress issueProgress = new IssueProgress();
        issueProgress.setIssueId(Long.valueOf(agreeDateReplyDetail.getIssueId()));
        issueProgress.setCrmId(agreeDateReplyDetail.getCrmId());
        issueProgress.setSequeceNum(issueDao.getMaxOrder(Long.valueOf(agreeDateReplyDetail.getIssueId())));
        issueProgress.setProcessType(IssueProcessType.Process.toString());
        issueProgress.setCurrentStatus(IssueStatus.Processing.toString());
        issueProgress.setProcessor(agreeDateReplyDetail.getAgreeDateSubmiterId());
        issueProgress.setWorkno(agreeDateReplyDetail.getAgreeDateSubmiter());
        issueProgress.setDescription("【客服留言】" + agreeDateReplyDetail.getAgreeDateReply());
        issueProgress.setReplyType(ReplyType.A.toString());
        issueProgress.setSyncStatus("P");
        issueProcessMapper.insertIssueProgress(issueProgress);

        return issueProgress.getId();
    }

    @Override
    public int checkAgent(String serviceCode, String productCode) {
        return issueDao.checkAgent(serviceCode, productCode);
    }

    @Override
    public AgentIssueSum selectAgentIssueSumarry(String serviceCode, String productCode) {
        AgentIssueSum sum = issueDao.selectAgentIssueSumarry(serviceCode, productCode);
        if (sum != null && sum.getAgentLimitIssueCount() != 0) {
//            sum.setPercent((new BigDecimal((float) sum.getUsedCount() * 100 / sum.getAgentLimitIssueCount()).setScale(0, BigDecimal.ROUND_HALF_UP).intValue()) + "%");
            //修复bug 可能存在精度丢失问题
            BigDecimal percentBigDecimal = BigDecimal.valueOf(sum.getUsedCount())
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(sum.getAgentLimitIssueCount()), 0, BigDecimal.ROUND_HALF_UP);
            sum.setPercent(percentBigDecimal.toString() + "%");
        }
        return sum;
    }

    @Override
    public BaseResponse getIssueCustomerInfo(long issueId) {
        try {
            return BaseResponse.ok(issueDao.getIssueCustomerInfo(issueId));
        } catch (Exception e) {
            log.error("getIssueCustomerInfo error issueId:" + issueId, e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }

    @Override
    public com.digiwin.escloud.common.model.ResponseBase checkEdrEventIssue(List<Map<String, String>> list) {
        Map<String, Object> result = new HashMap<>();
        if (CollectionUtils.isEmpty(list)) return com.digiwin.escloud.common.model.ResponseBase.ok(result);
        list.forEach(k -> {
            Map<String, Object> map = new HashMap<>();
            map.put("sourceId", k.get("eventId") + "_");
            map.put("rawId", k.get("rawId"));
            map.put("sourceType", IssueSourceType.EDREvent.toString());
            int count = issueDao.getEdrEventIssue(map);
            if (count > 0) {
                result.put(k.get("eventId"), false);  //false表示不可立案
            }/*else {
                result.put(k.get("eventId"),true);
            }*/
        });
        return com.digiwin.escloud.common.model.ResponseBase.ok(result);
    }

    @Override
    public BaseResponse getEventIssues(int pageNum, int pageSize, String eventId) {
        try {
            Page page = PageHelper.startPage(pageNum, pageSize);
            issueDao.getEventIssues(eventId);
            return BaseResponse.ok(new PageInfo<EventIssue>(page));
        } catch (Exception e) {
            log.error("getEventIssues error eventId:" + eventId, e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }

    @Override
    public String changeSFTProductCode(String serviceCode) {
        List<CustomerServiceInfo> css = issueDao.getCustomerServices(serviceCode);
        if (CollectionUtils.isEmpty(css)) {
            throw new NullPointerException(serviceCode + "沒有購買任何合約，不進行立案");
        }
        List<String> contractStates = Arrays.asList("A", "B", "C");
        CustomerServiceInfo cs1 = css.stream().filter(k -> "02".equals(k.getProductCode())).findFirst().orElse(null);
        CustomerServiceInfo cs2 = css.stream().filter(k -> "10".equals(k.getProductCode())).findFirst().orElse(null);

        if (Objects.isNull(cs1) && Objects.isNull(cs2)) {
            throw new NullPointerException(serviceCode + "沒有購買02或10產品線，不進行立案");
        } else if (Objects.nonNull(cs1) && contractStates.contains(cs1.getContractState())) {
            return "02";
        } else if (Objects.nonNull(cs2) && contractStates.contains(cs2.getContractState())) {
            return "10";
        } else if (Objects.nonNull(cs1)) {
            return "02";
        } else if (Objects.nonNull(cs2)) {
            return "10";
        } else {
            throw new NullPointerException(serviceCode + "沒有購買任何合約，不進行立案");
        }
    }

    @Override
    public List<IssueUnresolvedCount> selectUnresolvedIssueList(String userId, String productCode, String department, String queryUserId, String customerServiceCode) {
//        未结案总数，改为由前端加总
//        IssueUnresolvedCount issueUnresolvedCount = new IssueUnresolvedCount();
//        List<Integer> collect = issueUnresolved.stream().sorted(Comparator.comparing(e -> e.getUnresolvedCount()))
//                .map(IssueUnresolvedCount::getUnresolvedCount).collect(Collectors.toList());
//        int sum = 0;
//        for (int num : collect) {
//            sum = sum + num;
//        }
//        issueUnresolvedCount.setUnresolvedCount(sum);
//        issueUnresolvedCount.setProductCode("total");
//        issueUnresolved.add(issueUnresolvedCount);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("productCode", productCode);
        map.put("customerServiceCode", customerServiceCode);
        if (StringUtils.isEmpty(department)) {
            map.put("department", "");
            map.put("userId", userId);
        } else {
            map.put("department", department);
            map.put("userId", "");
        }
        return issueDao.selectUnresolvedIssueList(map);
    }

    @Override
    public List<IssueCountStatistic> selectIssueStatisticList(String userId, String productCode, String department, String startDate, String endDate, String customerServiceCode, String queryUserId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("productCode", productCode);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("customerServiceCode", customerServiceCode);
        if (StringUtils.isEmpty(department)) {
            map.put("department", "");
            map.put("userId", userId);
        } else {
            map.put("department", department);
            map.put("userId", "");
        }
        List<IssueCountStatistic> issueCountStatistics = issueDao.selectIssueStatisticList(map);
        List<IssueCountStatistic> issueCountStatisticList = new ArrayList<>();

//      SQL只有查询出有资料的日期，所以要处理没有资料的日期(例如: 1/1有搜寻到1笔资料，但1/2没有资料，则1/2就不会查到0笔)
        LocalDate localStartDate = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDate localEndDate = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        long days = ChronoUnit.DAYS.between(localStartDate, localEndDate);
        if (days <= 30) {
//            查出天(近一周、近一个月)
            LocalDate currentDate = localStartDate;
            while (!currentDate.isAfter(localEndDate)) {
                IssueCountStatistic issueCountStatistic = new IssueCountStatistic();
                issueCountStatistic.setDateFormat(currentDate.toString());
                issueCountStatistic.setIssueCount("0");
                issueCountStatisticList.add(issueCountStatistic);
                currentDate = currentDate.plusDays(1);
            }
        } else {
//            查出年月(近一年)
            YearMonth startYearMonth = YearMonth.from(localStartDate);
            YearMonth endYearMonth = YearMonth.from(localEndDate);
            YearMonth currentYearMonth = startYearMonth;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            while (!currentYearMonth.isAfter(endYearMonth)) {
                String formattedYearMonth = currentYearMonth.format(formatter);
                IssueCountStatistic issueCountStatistic = new IssueCountStatistic();
                issueCountStatistic.setDateFormat(formattedYearMonth);
                issueCountStatistic.setIssueCount("0");
                issueCountStatisticList.add(issueCountStatistic);
                currentYearMonth = currentYearMonth.plusMonths(1);
            }
        }
        Set<String> dateFormats = issueCountStatistics.stream()
                .map(IssueCountStatistic::getDateFormat)
                .collect(Collectors.toSet());
//      不包含在dateFormats集合中的issueCountStatistic物件，加入到issueCountStatistics集合中
        issueCountStatisticList.stream().filter(issueCounts -> !dateFormats.contains(issueCounts.getDateFormat()))
                .forEach(issueCountStatistics::add);
        return issueCountStatistics.stream().sorted(Comparator.comparing(e -> e.getDateFormat())).collect(Collectors.toList());
    }

    @Override
    public List<IssueProductCodeCount> selectIssueProductCodeStatisticList(String userId, String productCode, String department, String startDate, String endDate, String customerServiceCode, String queryUserId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("productCode", productCode);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("customerServiceCode", customerServiceCode);
        if (StringUtils.isEmpty(department)) {
            map.put("department", "");
            map.put("userId", userId);
        } else {
            map.put("department", department);
            map.put("userId", "");
        }
        return issueDao.selectIssueProductCodeStatisticList(map);
    }
    @Override
    public BaseResponse updateIssueKbShareChatFileHelp(long issueId, String chatFileHelp) {
        try {
            return BaseResponse.ok(issueDetailDao.updateIssueKbShareChatFileHelp(issueId, chatFileHelp));
        } catch (Exception e) {
            log.error("updateIssueKbShareChatFileHelp error issueId:" + issueId, e);
        }
        return BaseResponse.error(ResponseStatus.UPDATE_FAILD);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse updateUserId(IssueChangeSubmiterHistory issueChangeSubmiterHistory) {
        try {
            if (ObjectUtils.isEmpty(issueChangeSubmiterHistory.getIssueId())) {
                return BaseResponse.error(ResponseStatus.ISSUEID_IS_NULL);
            }
            // 查詢出原本案件的紀錄後更改紀錄
            issueChangeSubmiterHistory.setBeforeSubmiter(issueDao.selectIssueById(issueChangeSubmiterHistory.getIssueId()));
            int insertState = issueDao.insertIssueChangeSubmiterHistory(issueChangeSubmiterHistory);
            if (insertState <= 0) {
                return BaseResponse.error(ResponseStatus.INSERT_FAILD);
            }
            // 更改userId
            int issueState = issueDao.updateUserId(issueChangeSubmiterHistory);
            if(issueState <= 0) {
                return BaseResponse.error(ResponseStatus.UPDATE_FAILD);
            }
            return BaseResponse.ok(issueState);
        } catch (Exception ex) {
            return BaseResponse.error(ResponseStatus.UPDATE_FAILD);
        }
    }
    @Override
    public BaseResponse selectIssueChangeSubmiterHistory(Long issueId) {
        if (ObjectUtils.isEmpty(issueId) || issueId <= 0) {
            return BaseResponse.error(ResponseStatus.ISSUEID_IS_NULL);
        }
        return BaseResponse.ok(issueDao.selectIssueChangeSubmiterHistory(issueId));
    }

    @Override
    public BaseResponse selectIssueByServiceCode(String serviceCode) {
        if (StringUtils.isEmpty(serviceCode)) {
            return BaseResponse.error(ResponseStatus.SERVICE_CODE_IS_NULL);
        }
        return BaseResponse.ok(issueDao.selectUserPersonalInfoByUserId(serviceCode));
    }

}
