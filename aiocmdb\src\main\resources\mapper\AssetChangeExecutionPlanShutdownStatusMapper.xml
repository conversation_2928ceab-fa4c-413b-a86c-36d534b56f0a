<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeExecutionPlanShutdownStatusMapper">

    <!-- 插入执行计划停机状况 -->
    <insert id="insert" parameterType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeExecutionPlanShutdownStatus">
        INSERT INTO asset_change_execution_plan_shutdown_status (
            id, planId, isShutdownRequired, estimatedShutdownStartTime, estimatedShutdownEndTime, 
            shutdownDuration, shutdownExplanation, createTime, updateTime
        ) VALUES (
            #{id}, #{planId}, #{isShutdownRequired}, #{estimatedShutdownStartTime}, #{estimatedShutdownEndTime},
            #{shutdownDuration}, #{shutdownExplanation}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入执行计划停机状况 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO asset_change_execution_plan_shutdown_status (
            id, planId, isShutdownRequired, estimatedShutdownStartTime, estimatedShutdownEndTime, 
            shutdownDuration, shutdownExplanation, createTime, updateTime
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.planId}, #{item.isShutdownRequired}, #{item.estimatedShutdownStartTime}, 
             #{item.estimatedShutdownEndTime}, #{item.shutdownDuration}, #{item.shutdownExplanation}, 
             #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 根据计划ID删除执行计划停机状况 -->
    <delete id="deleteByPlanId" parameterType="long">
        DELETE FROM asset_change_execution_plan_shutdown_status WHERE planId = #{planId}
    </delete>

    <!-- 根据计划ID列表批量删除执行计划停机状况 -->
    <delete id="deleteByPlanIdList" parameterType="java.util.List">
        DELETE FROM asset_change_execution_plan_shutdown_status 
        WHERE planId IN
        <foreach collection="planIdList" item="planId" open="(" separator="," close=")">
            #{planId}
        </foreach>
    </delete>

    <!-- 根据计划ID查询执行计划停机状况 -->
    <select id="selectByPlanId" parameterType="long" resultType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeExecutionPlanShutdownStatus">
        SELECT * FROM asset_change_execution_plan_shutdown_status WHERE planId = #{planId}
    </select>

</mapper>
