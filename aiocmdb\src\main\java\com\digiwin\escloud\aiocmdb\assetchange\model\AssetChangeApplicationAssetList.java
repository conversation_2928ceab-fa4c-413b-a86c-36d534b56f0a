package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 变更资产清单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Data
@ApiModel(value = "AssetChangeApplicationAssetList对象", description = "变更资产清单")
public class AssetChangeApplicationAssetList implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("申请单ID")
    private Long applicationId;

    @ApiModelProperty("资产类别ID")
    private Long assetCategoryId;

    @ApiModelProperty("型号代码")
    private String modelCode;

    @ApiModelProperty("资产ID")
    private Long assetId;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;



    @Override
    public String toString() {
        return "AssetChangeApplicationAssetList{" +
            "id = " + id +
            ", applicationId = " + applicationId +
            ", assetCategoryId = " + assetCategoryId +
            ", modelCode = " + modelCode +
            ", assetId = " + assetId +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
        "}";
    }
}
