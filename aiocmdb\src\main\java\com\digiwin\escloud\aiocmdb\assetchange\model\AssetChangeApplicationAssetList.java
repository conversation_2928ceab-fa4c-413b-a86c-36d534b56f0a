package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 变更资产清单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@ApiModel(value = "AssetChangeApplicationAssetList对象", description = "变更资产清单")
public class AssetChangeApplicationAssetList implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("申请单ID")
    private Long applicationId;

    @ApiModelProperty("资产类别ID")
    private Long assetCategoryId;

    @ApiModelProperty("型号代码")
    private String modelCode;

    @ApiModelProperty("资产ID")
    private String assetId;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public Long getAssetCategoryId() {
        return assetCategoryId;
    }

    public void setAssetCategoryId(Long assetCategoryId) {
        this.assetCategoryId = assetCategoryId;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getAssetId() {
        return assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AssetChangeApplicationAssetList{" +
            "id = " + id +
            ", applicationId = " + applicationId +
            ", assetCategoryId = " + assetCategoryId +
            ", modelCode = " + modelCode +
            ", assetId = " + assetId +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
        "}";
    }
}
