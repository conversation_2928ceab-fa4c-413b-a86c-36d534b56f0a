package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 停机状况
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@ApiModel(value = "AssetChangeExecutionPlanShutdownStatus对象", description = "停机状况")
public class AssetChangeExecutionPlanShutdownStatus implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("计划ID")
    private Long planId;

    @ApiModelProperty("是否需要停机")
    private Boolean isShutdownRequired;

    @ApiModelProperty("预计停机开始时间")
    private LocalDateTime estimatedShutdownStartTime;

    @ApiModelProperty("预计停机结束时间")
    private LocalDateTime estimatedShutdownEndTime;

    @ApiModelProperty("停机时长")
    private Double shutdownDuration;

    @ApiModelProperty("停机说明")
    private String shutdownExplanation;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Boolean getIsShutdownRequired() {
        return isShutdownRequired;
    }

    public void setIsShutdownRequired(Boolean isShutdownRequired) {
        this.isShutdownRequired = isShutdownRequired;
    }

    public LocalDateTime getEstimatedShutdownStartTime() {
        return estimatedShutdownStartTime;
    }

    public void setEstimatedShutdownStartTime(LocalDateTime estimatedShutdownStartTime) {
        this.estimatedShutdownStartTime = estimatedShutdownStartTime;
    }

    public LocalDateTime getEstimatedShutdownEndTime() {
        return estimatedShutdownEndTime;
    }

    public void setEstimatedShutdownEndTime(LocalDateTime estimatedShutdownEndTime) {
        this.estimatedShutdownEndTime = estimatedShutdownEndTime;
    }

    public Double getShutdownDuration() {
        return shutdownDuration;
    }

    public void setShutdownDuration(Double shutdownDuration) {
        this.shutdownDuration = shutdownDuration;
    }

    public String getShutdownExplanation() {
        return shutdownExplanation;
    }

    public void setShutdownExplanation(String shutdownExplanation) {
        this.shutdownExplanation = shutdownExplanation;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AssetChangeExecutionPlanShutdownStatus{" +
            "id = " + id +
            ", planId = " + planId +
            ", isShutdownRequired = " + isShutdownRequired +
            ", estimatedShutdownStartTime = " + estimatedShutdownStartTime +
            ", estimatedShutdownEndTime = " + estimatedShutdownEndTime +
            ", shutdownDuration = " + shutdownDuration +
            ", shutdownExplanation = " + shutdownExplanation +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
        "}";
    }
}
