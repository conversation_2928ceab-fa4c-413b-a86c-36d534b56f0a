package com.digiwin.escloud.userv2.chatheader.service;

import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.userv2.chatheader.model.ChatHeaderSetting;

public interface ISettingService {
    BaseResponse getChatHeaderSettings(String serviceRegion, String productCode, String lang, int page, int size);

    BaseResponse getChatHeaderSetting(String serviceRegion, String productCode, String lang);

    BaseResponse getChatHeaderSettingDetail(long id);

    BaseResponse saveChatHeaderSetting(ChatHeaderSetting chatHeader);

    BaseResponse updateChatHeaderSetting(ChatHeaderSetting chatHeader);

    BaseResponse deleteChatHeaderSetting(long id);

}
