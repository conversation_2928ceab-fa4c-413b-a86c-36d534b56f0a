<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationAssetListMapper">

    <!-- 插入变更资产清单 -->
    <insert id="insert" parameterType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationAssetList">
        INSERT INTO asset_change_application_asset_list (
            id, applicationId, assetCategoryId, modelCode, assetId, createTime, updateTime
        ) VALUES (
            #{id}, #{applicationId}, #{assetCategoryId}, #{modelCode}, #{assetId}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入变更资产清单 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO asset_change_application_asset_list (
            id, applicationId, assetCategoryId, modelCode, assetId, createTime, updateTime
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.applicationId}, #{item.assetCategoryId}, #{item.modelCode}, 
             #{item.assetId}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 根据申请单ID删除变更资产清单 -->
    <delete id="deleteByApplicationId" parameterType="long">
        DELETE FROM asset_change_application_asset_list WHERE applicationId = #{applicationId}
    </delete>

    <!-- 根据申请单ID查询变更资产清单 -->
    <select id="selectByApplicationId" parameterType="long" resultType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationAssetList">
        SELECT * FROM asset_change_application_asset_list WHERE applicationId = #{applicationId}
    </select>

</mapper>
