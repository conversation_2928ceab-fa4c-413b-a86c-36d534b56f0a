@startuml
title 网安稽查报告生成详细时序图 (数据库报告方案)

participant "NetworkSecurityExamServiceImpl" as NetworkService
participant "AiopsExamService" as ExamService
participant "AiopsExamRecordMapper" as ExamMapper
participant "DbReportService" as DbService
participant "DbDataFactory" as DbFactory
participant "BigDataUtil" as BigData
participant "WarningService" as WarningService
participant "RestUtil" as RestUtil
participant "NetworkSecurityExamEsReport" as EsReport
participant "MySQL" as MySQL
participant "ElasticSearch" as ES
participant "后台服务(AI服务)" as AIService

== 异步报告生成主流程 ==

NetworkService -> NetworkService: CompletableFuture.runAsync(() -> saveNetworkSecurityExamReport2Es(data))
activate NetworkService

note right of NetworkService: 1. 获取请求头信息
NetworkService -> NetworkService: RequestUtil.getHeaderAppCode()
NetworkService -> NetworkService: record.getEid()

note right of NetworkService: 2. 查询组织类数据 (新流程中将删除)
NetworkService -> NetworkService: getProjectType(NETWORK_SECURITY_EXAM_PORJECT_ORGANIZATIONAL, null, null, true, null, eid)
activate NetworkService

NetworkService -> NetworkService: getProjectTypeList(categoryCode, categoryName, parentCode, filterNullModel)
NetworkService -> ExamMapper: selectProjectType(categoryCode, categoryName, parentCode, filterNullModel, null)
ExamMapper -> MySQL: SELECT * FROM network_security_examination_project_type WHERE category_code = ?
MySQL --> ExamMapper: 返回项目类型列表
ExamMapper --> NetworkService: List<NetworkSecurityExaminationProjectType>

NetworkService -> NetworkService: getModelCodeListFromProjectTypes(projectTypes)
NetworkService -> NetworkService: queryModelCount(modelCodeList, eid)
NetworkService -> BigData: srQuery(buildUnionSql(...))
note right: 构建UNION SQL查询各模型数据计数
BigData --> NetworkService: List<Map<String, Object>>

NetworkService -> NetworkService: processEtlDetails(projectType, eid, modelPKMap)
deactivate NetworkService

NetworkService -> NetworkService: queryModelDetail(modelCodeList, whereClause)
NetworkService -> BigData: srQuery(buildUnionSql(...))
note right: 查询组织类详细数据
BigData --> NetworkService: List<Map<String, Object>> organizationalDataList

NetworkService -> RestUtil: getModelDetail(modelCode)
RestUtil --> NetworkService: BaseResponse<ModelDetail>

NetworkService -> NetworkService: doModelData(organizationalData, modelDetail, record)
note right: 处理模型数据，构建OrganizationalModel

== doModelData 详细处理流程 ==

loop 处理每个ModelFieldGroup
    NetworkService -> NetworkService: 创建OrganizationalModelMap

    loop 处理每个ModelFieldMapping
        alt 如果是FieldSet类型
            NetworkService -> NetworkService: 处理字段集合
            loop 处理每个subfield
                NetworkService -> NetworkService: 创建subDataMap
                note right: 设置name, fieldSetName, fieldSetCode, code, value等
                NetworkService -> NetworkService: map.get(subfield.getFieldCode())
                note right: 从organizationalData获取字段值
            end
        else 如果是单个Field
            NetworkService -> NetworkService: 创建dataMap
            note right: 设置name, code, value, sort等
            NetworkService -> NetworkService: map.get(field.getFieldCode())
        end
    end

    alt 如果是DataContent分组
        NetworkService -> NetworkService: 按fieldSetName分组处理
        NetworkService -> NetworkService: createDataContent()
        NetworkService -> NetworkService: mergeAdjacentElements()
    else 其他分组
        NetworkService -> NetworkService: 直接添加到organizationalModelMapList
    end
end

NetworkService -> NetworkService: record.setOrganizationalData(model)

note right of NetworkService: 3. 查询体检记录信息
NetworkService -> ExamMapper: selectAiopsExamRecordReport(record.getAerId())
ExamMapper -> MySQL: SELECT * FROM aiops_exam_record WHERE id = ?
MySQL --> ExamMapper: AiopsExamRecord
ExamMapper --> NetworkService: AiopsExamRecord

note right of NetworkService: 4. 查询预警数据
NetworkService -> WarningService: getDailyWarningInfo(appCode, eid, startDate, endDate)
activate WarningService

WarningService -> BigData: srQuery(warningSQL)
note right: 查询StarRocks预警数据\nSELECT date_format(wd.warningtime, '%Y-%m-%d') AS warningDate,\nwd.warningLevel AS level, COUNT(*) AS count\nFROM warning w INNER JOIN warningdetail wd\nWHERE w.eid = ? AND wd.warningtime BETWEEN ? AND ?
BigData --> WarningService: List<Map<String, Object>>

WarningService --> NetworkService: List<Map<String, Object>> warningData
deactivate WarningService

NetworkService -> NetworkService: record.setWarningData(warningData)
NetworkService -> NetworkService: record.setGenerationTime(LocalDateTime.now())

== 数据库报告服务处理 (新流程中的数据库报告方案) ==

note over NetworkService, AIService: 根据活动图，新流程将使用数据库报告服务查询指标检测值和参考值

alt 如果record.getType() == "Database"
    NetworkService -> DbService: getDbReportData(String.valueOf(record.getId()), dbType, productCode)
    activate DbService

    DbService -> DbFactory: getDbReportData(id, dbTypeCode)
    activate DbFactory

    DbFactory -> ES: esService.findById(id, clazz)
    note right: 从ES查询数据库报告数据
    ES --> DbFactory: DefaultOracleDbReport
    DbFactory --> DbService: Object dbReportData
    deactivate DbFactory

    alt 如果是DefaultOracleDbReport类型
        DbService -> DbService: processActualValue.processValue(avDefaultValue, targetKey)
        note right: 处理检测值内容\n按规则拼接检测值 如 c: 10% d: 20%

        DbService -> BigData: srQuery(sql)
        note right: 查询StarRocks获取指标检测值
        BigData --> DbService: List<Map<String, Object>>

        DbService -> MySQL: 查询参考值数据
        note right: 从report_reference_value表查询
        MySQL --> DbService: ReportReferenceValue

        DbService -> DbService: 整理参考值、检测值数据
        note right: 按照一定规则拼接检测值\n需重写ProcessActualValue接口的行为
    end

    DbService --> NetworkService: DefaultOracleDbReport
    deactivate DbService

    NetworkService -> NetworkService: report.getAssessmentConclusion()
    NetworkService -> NetworkService: record.setExamReportContent(resSuggestion)
end

== AI服务生成报告结论 (新流程中将添加) ==

note over NetworkService, AIService: 根据活动图，新流程将调用AI服务生成报告结论

NetworkService -> NetworkService: 组合报告数据和分数数据、预警数据

NetworkService -> AIService: 发起报告结论生成请求
note right: 调用aioai项目的AI服务
activate AIService

AIService -> AIService: 发起生成报告结论API
note right: 调用IndepthAI服务

AIService -> AIService: 阻塞等待报告结论
note right: 等待第三方IndepthAI服务响应

AIService --> NetworkService: 返回报告结论
deactivate AIService

== 存储报告到ES ==

NetworkService -> EsReport: generateReport(record)
activate EsReport

EsReport -> EsReport: BeanUtils.copyProperties(reportRecord, report)
EsReport -> EsReport: report.setId(reportRecord.getId() + "")
EsReport -> EsReport: report.setReportType("Complete")

EsReport -> ES: save(report, false)
note right: 存储报告详情到ES\n新流程中不再存储组织类数据\n现在报告指标分数数据实时查询，因检测值的不同查询逻辑需存储ES
ES --> EsReport: AiopsExamRecordsEsReportRecord

EsReport --> NetworkService: String reportId
deactivate EsReport

== 更新报告状态 ==

NetworkService -> ExamMapper: updateReportStatus(record.getId(), ReportStatus.UNDER_EVA.getIndex(), LocalDateTime.now())
ExamMapper -> MySQL: UPDATE aiops_exam_records_report_record SET status = ?, generation_time = ? WHERE id = ?
note right: 更新报告记录状态为评估中
MySQL --> ExamMapper: 更新成功
ExamMapper --> NetworkService: 更新完成

deactivate NetworkService

== 补充：体检记录分数查询流程 (getReportScore方法) ==

note over NetworkService, MySQL: 当前流程中的getReportScore方法详细实现

NetworkService -> ExamService: getReportScore(record.getAerId(), scale)
activate ExamService

ExamService -> ExamMapper: selectAiopsExamRecordReport(aerId)
ExamMapper -> MySQL: SELECT * FROM aiops_exam_record WHERE id = ?
MySQL --> ExamMapper: AiopsExamRecord
ExamMapper --> ExamService: AiopsExamRecord

ExamService -> ExamService: aiopsExamService.getAeById(aer.getAeId())
ExamService -> MySQL: SELECT * FROM aiops_exam WHERE id = ?
MySQL --> ExamService: AiopsExam

ExamService -> ExamMapper: selectIndexTypeByAeid(aer.getAeId())
ExamMapper -> MySQL: SELECT * FROM aiops_exam_index_type WHERE ae_id = ?
MySQL --> ExamMapper: List<AiopsExamIndexType>
ExamMapper --> ExamService: List<AiopsExamIndexType>

ExamService -> ExamMapper: selectAiopsExamItem(aerId)
ExamMapper -> MySQL: SELECT aiops_item FROM aiops_exam_item_map WHERE aer_id = ?
MySQL --> ExamMapper: List<AiopsExamItemMap>
ExamMapper --> ExamService: List<AiopsExamItemMap>

ExamService -> ExamService: 过滤启用的体检项目映射
ExamService -> ExamService: 计算各指标类型分数
ExamService -> ExamService: 构建AiopsExamRecordsReportRecord

ExamService --> NetworkService: BaseResponse<AiopsExamRecordsReportRecord>
deactivate ExamService

@enduml
