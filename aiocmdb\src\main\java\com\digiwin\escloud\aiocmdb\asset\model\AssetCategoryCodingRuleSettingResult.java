package com.digiwin.escloud.aiocmdb.asset.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 资产类别编码规则设定结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@ApiModel(value = "AssetCategoryCodingRuleSettingResult对象", description = "资产类别编码规则设定结果表")
public class AssetCategoryCodingRuleSettingResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("对象类型")
    private String objType;

    @ApiModelProperty("对象ID")
    private Long objId;

    @ApiModelProperty("规则ID")
    private Long ruleId;

    @ApiModelProperty("规则设定值")
    private String ruleSettingValue;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    private String ruleNumber;

    private String categoryClassificationCode;
}
