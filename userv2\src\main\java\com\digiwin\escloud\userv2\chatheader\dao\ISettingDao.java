package com.digiwin.escloud.userv2.chatheader.dao;

import com.digiwin.escloud.userv2.chatheader.model.ChatHeaderSetting;

import java.util.List;
import java.util.Map;

public interface ISettingDao {
    List<ChatHeaderSetting> getChatHeaderSettings(Map<String, Object> param);

    ChatHeaderSetting getChatHeaderSetting(Map<String, Object> param);

    ChatHeaderSetting getChatHeaderSettingDetail(Map<String, Object> param);

    long saveChatHeaderSetting(ChatHeaderSetting ChatHeader);

    int updateChatHeaderSetting(ChatHeaderSetting ChatHeader);

    int deleteChatHeaderSetting(Map<String, Object> param);

}
