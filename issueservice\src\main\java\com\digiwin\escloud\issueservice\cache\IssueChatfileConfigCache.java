package com.digiwin.escloud.issueservice.cache;

import com.digiwin.escloud.issueservice.constant.RedisKeyConstants;
import com.digiwin.escloud.issueservice.dao.IIssueDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 *  IssueChatfileConfig cache
 */
@Component
public class IssueChatfileConfigCache {
    @Autowired
    private IIssueDao issueDao;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    //默认1天失效
    public boolean selectIssueChatFileConfig(String serviceRegion, String productCode) {
        Optional<String> opt = Optional.ofNullable(stringRedisTemplate.opsForValue().get(RedisKeyConstants.ISSUE_CHATFILE_CONFIG+serviceRegion+"_"+productCode));
        if (opt.isPresent()) {
            return "true".equals(opt.get());
        } else {
            boolean result = issueDao.checkIssueHasChatFileSearch(serviceRegion, productCode);
            stringRedisTemplate.opsForValue().set(RedisKeyConstants.ISSUE_CHATFILE_CONFIG+serviceRegion+"_"+productCode, result?"true":"false",1, TimeUnit.DAYS);
            return result;
        }
    }

    public void clearIssueChatFileConfig(String serviceRegion, String productCode) {
        stringRedisTemplate.delete(RedisKeyConstants.ISSUE_CHATFILE_CONFIG+serviceRegion+"_"+productCode);
        System.out.println("delete " + serviceRegion + serviceRegion  + RedisKeyConstants.ISSUE_CHATFILE_CONFIG + " cache.");
    }

    //默认1天失效
    public boolean getIsSearchByChatFile(String key) {
        Optional<String> opt = Optional.ofNullable(stringRedisTemplate.opsForValue().get(key));
        if (opt.isPresent()) {
            return "true".equals(opt.get());
        } else {
            boolean result = issueDao.getIsSearchByChatFile(key);
            stringRedisTemplate.opsForValue().set(key, result?"true":"false",1, TimeUnit.DAYS);
            return result;
        }
    }

    public void clearIsSearchByChatFile(String key) {
        stringRedisTemplate.delete(key);
        System.out.println("delete " + key + " cache.");
    }
}
