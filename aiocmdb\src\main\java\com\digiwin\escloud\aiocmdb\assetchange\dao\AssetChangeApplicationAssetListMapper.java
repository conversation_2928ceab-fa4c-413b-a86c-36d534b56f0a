package com.digiwin.escloud.aiocmdb.assetchange.dao;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationAssetList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 变更资产清单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Mapper
public interface AssetChangeApplicationAssetListMapper {

    /**
     * 插入变更资产清单
     *
     * @param assetList 变更资产清单
     * @return 影响行数
     */
    int insert(AssetChangeApplicationAssetList assetList);

    /**
     * 批量插入变更资产清单
     *
     * @param assetListList 变更资产清单列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<AssetChangeApplicationAssetList> assetListList);

    /**
     * 根据申请单ID删除变更资产清单
     *
     * @param applicationId 申请单ID
     * @return 影响行数
     */
    int deleteByApplicationId(@Param("applicationId") Long applicationId);

    /**
     * 根据申请单ID查询变更资产清单
     *
     * @param applicationId 申请单ID
     * @return 变更资产清单列表
     */
    List<AssetChangeApplicationAssetList> selectByApplicationId(@Param("applicationId") Long applicationId);
}
