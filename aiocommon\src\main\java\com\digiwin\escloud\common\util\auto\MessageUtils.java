package com.digiwin.escloud.common.util.auto;

import com.fasterxml.jackson.core.JsonProcessingException;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.EncodedResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Date 2020/3/30-11:04
 */
@Slf4j
@Component
public class MessageUtils {

    @Autowired
    private MessageSource messageSource;

    // 简体中文
    protected static final String[] ZH_CN = {"zh_cn", "zh-cn", "zh_CN", "zh-CN"}; //huly: 修复漏洞/bug public 改成 protected
    public static final String ZH_CN_STANDARD = "zh_CN";
    // 繁体中文
    protected static final String[] ZH_TW = {"zh_tw", "zh-tw", "zh_TW", "zh-TW"}; //huly: 修复漏洞/bug public 改成 protected
    public static final String ZH_TW_STANDARD = "zh_TW";
    // 越南
    protected static final String[] VI_VN = {"vi_vn", "vi-vn", "vi_VN", "vi-VN"}; //huly: 修复漏洞/bug public 改成 protected
    public static final String VI_VN_STANDARD = "vi_VN";
    // 泰国
    protected static final String[] TH_TH = {"th_th", "th-th", "th_TH", "th-TH"}; //huly: 修复漏洞/bug public 改成 protected
    public static final String TH_TH_STANDARD = "th_TH";
    // 马来西亚
    protected static final String[] MS_MY = {"ms_my", "ms-my", "ms_MY", "ms-MY"}; //huly: 修复漏洞/bug public 改成 protected
    public static final String MS_MY_STANDARD = "ms_MY";
    // 英文
    protected static final String[] EN_US = {"en_us", "en-us", "en_US", "en-US"}; //huly: 修复漏洞/bug public 改成 protected
    public static final String EN_US_STANDARD = "en_US";

    private final static ObjectMapper objectMapper = new ObjectMapper();

    public String getLanguage(String language){
        if (ArrayUtils.contains(ZH_CN,language)) {
            return ZH_CN_STANDARD;
        } else if (ArrayUtils.contains(ZH_TW,language)) {
            return ZH_TW_STANDARD;
        } else if (ArrayUtils.contains(VI_VN,language)) {
            return VI_VN_STANDARD;
        } else if (ArrayUtils.contains(TH_TH,language)) {
            return TH_TH_STANDARD;
        } else if (ArrayUtils.contains(EN_US,language)) {
            return EN_US_STANDARD;
        } else if (ArrayUtils.contains(MS_MY,language)) {
            return MS_MY_STANDARD;
        } else {
            return ZH_TW_STANDARD;
        }
    }

    public String get(String msgKey,String language) {
        Locale locale;
        if (ArrayUtils.contains(ZH_CN,language)) {
            locale = Locale.SIMPLIFIED_CHINESE;
        } else if (ArrayUtils.contains(ZH_TW,language)) {
            locale = Locale.TRADITIONAL_CHINESE;
        } else if (ArrayUtils.contains(VI_VN,language)) {
            locale = new Locale("vi","VN");
        } else if (ArrayUtils.contains(TH_TH,language)) {
            locale = new Locale("th","TH");
        } else if (ArrayUtils.contains(MS_MY,language)) {
            locale = new Locale("ms","MY");
        } else if (ArrayUtils.contains(EN_US,language)) {
            locale = Locale.US;
        } else {
            locale = Locale.TRADITIONAL_CHINESE;
        }
        try {
            return messageSource.getMessage(msgKey, null, locale);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("获取国际化信息失败-----");
            return msgKey;
        }
    }

    public String noExceptionLogGet(String msgKey,String language) {
        Locale locale;
        if (ArrayUtils.contains(ZH_CN,language)) {
            locale = Locale.SIMPLIFIED_CHINESE;
        } else if (ArrayUtils.contains(ZH_TW,language)) {
            locale = Locale.TRADITIONAL_CHINESE;
        } else if (ArrayUtils.contains(VI_VN,language)) {
            locale = new Locale("vi","VN");
        } else if (ArrayUtils.contains(TH_TH,language)) {
            locale = new Locale("th","TH");
        } else if (ArrayUtils.contains(MS_MY,language)) {
            locale = new Locale("ms","MY");
        } else if (ArrayUtils.contains(EN_US,language)) {
            locale = Locale.US;
        } else {
            locale = Locale.TRADITIONAL_CHINESE;
        }
        try {
            return messageSource.getMessage(msgKey, null, locale);
        } catch (Exception ex) {
            return msgKey;
        }
    }

    private static Properties prop = null;

    private static Properties loadI18nProp() {
        if (prop != null) {
            return prop;
        }
        try {
            String i18nFile = "i18n/messages.properties";
            Resource resource = new ClassPathResource(i18nFile);
            EncodedResource encodedResource = new EncodedResource(resource, "UTF-8");
            prop = PropertiesLoaderUtils.loadProperties(encodedResource);
        } catch (IOException ex) {
            log.error("loadI18nProp exception:", ex);
        }
        return prop;
    }

    public String getMultiString(String... languages) throws JsonProcessingException {
        String language = "";
        if (!ArrayUtils.isEmpty(languages)) {
            language = languages[0];
        }
        Map<String, String> map = new HashMap<>();
        Properties prop = loadI18nProp();
        for (String key : prop.stringPropertyNames()) {
            if (StringUtils.isEmpty(language)) {
                map.put(key, prop.getProperty(key));
            } else {
                map.put(key, get(key, language));
            }
        }
        String json = objectMapper.writeValueAsString(map);
        return json;
    }
}
