<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationExecutionPlanMapper">

    <!-- 插入执行计划 -->
    <insert id="insert" parameterType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationExecutionPlan">
        INSERT INTO asset_change_application_execution_plan (
            id, applicationId,planDescription, planStartDate, planEndDate, planStatus, createTime, updateTime
        ) VALUES (
            #{id}, #{applicationId}, #{planDescription},#{planStartDate}, #{planEndDate}, #{planStatus}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入执行计划 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO asset_change_application_execution_plan (
            id, applicationId,planDescription, planStartDate, planEndDate, planStatus, createTime, updateTime
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.applicationId},#{planDescription}, #{item.planStartDate}, #{item.planEndDate},
             #{item.planStatus}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 根据申请单ID删除执行计划 -->
    <delete id="deleteByApplicationId" parameterType="long">
        DELETE FROM asset_change_application_execution_plan WHERE applicationId = #{applicationId}
    </delete>

    <!-- 根据申请单ID查询执行计划 -->
    <select id="selectByApplicationId" parameterType="long" resultType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationExecutionPlan">
        SELECT * FROM asset_change_application_execution_plan WHERE applicationId = #{applicationId}
    </select>

</mapper>
