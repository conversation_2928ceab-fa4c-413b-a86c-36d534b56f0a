package com.digiwin.escloud.aiocmdb.asset.service.impl;

import com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper;
import com.digiwin.escloud.aiocmdb.asset.model.AiopsProcessParam;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategory;
import com.digiwin.escloud.aiocmdb.asset.model.AssetSaveBigDataParam;
import com.digiwin.escloud.aiocmdb.asset.service.AssetService;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailInfo;
import com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailRequest;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aiocmdb.asset.service.impl.AssetServiceImpl.INSTANCE_STATUS_LIST;
import static com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil.SERVICECLOUD_MODEL_DB;
@Slf4j
@Service
public class AssetTaskService {
    @Resource
    private AssetCategoryMapper assetCategoryMapper;
    @Resource
    private AioItmsFeignClient aioItmsFeignClient;
    @Resource
    private AssetService assetService;
    @Resource
    private BigDataUtil bigDataUtil;

//    public ResponseBase dsProcess(AiopsProcessParam param) {
//        try {
//            log.info("[dsProcess] 开始异步执行DS处理，参数: {}", param.getAcId());
//
//            // 异步执行耗时的dsProcess操作
//            CompletableFuture<ResponseBase> future = dsProcessAsync(param);
//
//            // 立即返回成功响应，表示任务已提交
//            log.info("[dsProcess] DS处理任务已提交到异步线程池，acId: {}", param.getAcId());
//            return ResponseBase.ok("DS处理任务已提交，正在后台执行中...");
//
//        } catch (Exception e) {
//            log.error("[dsProcess] 提交异步任务失败", e);
//            return ResponseBase.error(ResponseCode.INTERNAL_ERROR, "提交异步任务失败: " + e.getMessage());
//        }
//    }

    /**
     * 异步执行DS处理的核心方法
     * 使用Spring管理的专用线程池执行耗时操作
     */
    @Async("dsProcessExecutor")
    public void dsProcessAsync(AiopsProcessParam param) {
        try {
            StopWatch start = new StopWatch();
            start.start();
            log.info("[dsProcessAsync] 开始在异步线程中执行DS处理，线程: {}, acId: {}",
                    Thread.currentThread().getName(), param.getAcId());

            ResponseBase result = dsProcessSync(param);

            start.stop();
            log.info("[dsProcessAsync] 异步DS处理完成，线程: {}, acId: {}, 结果: {} ,时间：{}s",
                    Thread.currentThread().getName(), param.getAcId(), result.checkIsSuccess() ? "成功" : "失败",start.getTotalTimeSeconds());


        } catch (Exception e) {
            log.error("[dsProcessAsync] 异步DS处理异常，线程: {}, acId: {}",
                    Thread.currentThread().getName(), param.getAcId(), e);

        }
    }

    /**
     * 同步执行DS处理的核心逻辑（原dsProcess方法）
     */
    private ResponseBase dsProcessSync(AiopsProcessParam param) {
        try {
            // 1. 根据acId查询asset_category表获取aiopsItem和modelCode
            AssetCategory assetCategory = assetCategoryMapper.selectAssetCategoryById(param.getAcId());
            if (assetCategory == null) {
                return ResponseBase.error(ResponseCode.QUERY_VERIFY);
            }

            Long conditionEid = param.getEid();
//            Long conditionAssetId = param.getAssetId();

            String aiopsItem = assetCategory.getAiopsItem();
            String modelCode = assetCategory.getModelCode();

            // 1. 分页调用getCommonAiopsInstanceAssetListNoPage获取资产实例
            // 这个数据是给注释说明4.使用的

            int instancePageSize = param.getPageSize(); // 设置分页大小
            int instancePageNum = 1;
            boolean hasMoreInstanceData = true;

            while (hasMoreInstanceData) {
                Map<Long, List<Long>> eidToAiIdsMap = new HashMap<>();
                MergeStatisticsDetailRequest request = new MergeStatisticsDetailRequest();
                request.setAiopsItemList(Stream.of(aiopsItem).collect(Collectors.toList()));
                request.setAiopsAuthStatusList(INSTANCE_STATUS_LIST);
                request.setNeedPaging(true);
                request.setPageNum(instancePageNum);
                request.setPageSize(instancePageSize);
                request.setNeedExcludeAsset(true);
                request.setModelCode(modelCode);

                // 根据条件设置eid - 在极端情况下可能会需要注释说明2.的数据
//                if (conditionAssetId != null) {
//                    // 如果conditionAssetId不为空，使用StarRocks查询结果中的eid（注释说明2.产生的数据一定只会有一条）
//                    StringBuilder sqlBuilder = new StringBuilder();
//                    sqlBuilder.append(String.format(
//                            "SELECT eid, aiId FROM %s%s WHERE aiId IS NOT NULL AND useStatus != 'Scrapped'",
//                            SERVICECLOUD_MODEL_DB, modelCode));
//
//                    // 根据优先级添加过滤条件：conditionAssetId优先级最高，其次是conditionEid
//                    sqlBuilder.append(" AND assetId = ").append(conditionAssetId);
//                    String sql = sqlBuilder.toString();
//                    List<Map<String, Object>> starRocksResult = bigDataUtil.starrocksQuery(sql);
//                }

                if (conditionEid != null) {
                    // 如果conditionAssetId为空，使用conditionEid
                    request.setEid(conditionEid);
                }

                log.info("[dsProcess] Calling getCommonAiopsInstanceAssetListNoPage page {}", instancePageNum);
                BaseResponse<List<MergeStatisticsDetailInfo>> response =
                        aioItmsFeignClient.getCommonAiopsInstanceAssetListNoPage(request);

                if (!response.checkIsSuccess() || response.getData() == null) {
                    log.error("[dsProcess] getCommonAiopsInstanceAssetListNoPage failed or returned null data: {}", response);
                    hasMoreInstanceData = false;
                } else {
                    List<MergeStatisticsDetailInfo> detailInfoList = response.getData();

                    if (detailInfoList.isEmpty()) {
                        hasMoreInstanceData = false;
                    } else {
                        // 按租户分组，聚合实例id为aiIdList
                        Map<Long, List<Long>> pageEidToAiIdsMap = detailInfoList.stream()
                                .collect(Collectors.groupingBy(
                                        MergeStatisticsDetailInfo::getEid,
                                        Collectors.mapping(MergeStatisticsDetailInfo::getId, Collectors.toList())
                                ));

                        // 合并到总的Map中
                        pageEidToAiIdsMap.forEach((eid, aiIdList) ->
                                eidToAiIdsMap.computeIfAbsent(eid, k -> new ArrayList<>()).addAll(aiIdList)
                        );

                        // 如果返回的数据少于分页大小，说明没有更多数据了
                        if (detailInfoList.size() < instancePageSize) {
                            hasMoreInstanceData = false;
                        } else {
                            instancePageNum++;
                        }

                        log.info("[dsProcess] Instance page {} processed, got {} records", instancePageNum - 1, detailInfoList.size());
                    }
                }
                // 4. 保存自动上报得资产 upsert=false
                for (Map.Entry<Long, List<Long>> e : eidToAiIdsMap.entrySet()) {
                    Long key = e.getKey();
                    List<Long> idListForEid = e.getValue();
                    if (idListForEid == null || idListForEid.isEmpty()) {
                        continue;
                    }

                    log.info("[dsProcess] Processing batch save for EID: {}, with {} instances.", key, idListForEid.size());

                    AssetSaveBigDataParam saveParam = new AssetSaveBigDataParam();
                    saveParam.setModelCode(modelCode);
                    saveParam.setUpsert(false);
                    saveParam.setAiIdList(idListForEid);
                    saveParam.setAiopsItem(aiopsItem);
                    saveParam.setEid(key);

                    // 如果conditionAssetId不为空，设置assetId
//                    if (conditionAssetId != null) {
//                        saveParam.setAssetId(conditionAssetId);
//                    }

                    BaseResponse saveResult = assetService.batchSaveInstanceToStarRocksAndHBase(saveParam);

                    if (!saveResult.checkIsSuccess()) {
                        log.error("[dsProcess] batchSaveInstanceToStarRocksAndHBase failed for EID: {}. Result: {}", key, saveResult);
                    }
                }
            }


            // 2. 分页查询StarRocks中的modelCode数据，过滤条件是aiId不等于空，useStatus != ('Scrapped')
            int starRocksPageSize = param.getPageSize(); // 设置分页大小
            int starRocksPageNum = 1;
            boolean hasMoreStarRocksData = true;

            while (hasMoreStarRocksData) {
                Map<Long, List<Long>> eidToAiIdListMap = new HashMap<>();
                StringBuilder sqlBuilder = new StringBuilder();
                sqlBuilder.append(String.format(
                        "SELECT eid, aiId FROM %s%s WHERE aiId IS NOT NULL AND useStatus != 'Scrapped'",
                        SERVICECLOUD_MODEL_DB, modelCode));

                // 根据优先级添加过滤条件：conditionAssetId优先级最高，其次是conditionEid
//                if (conditionAssetId != null) {
//                    sqlBuilder.append(" AND assetId = ").append(conditionAssetId);
//                } else
                if (conditionEid != null) {
                    sqlBuilder.append(" AND eid = ").append(conditionEid);
                }

                // 添加分页条件
                int offset = (starRocksPageNum - 1) * starRocksPageSize;
                sqlBuilder.append(" LIMIT ").append(starRocksPageSize).append(" OFFSET ").append(offset);

                String sql = sqlBuilder.toString();
                log.info("[dsProcess] Executing StarRocks query page {}: {}", starRocksPageNum, sql);
                List<Map<String, Object>> starRocksResult = bigDataUtil.starrocksQuery(sql);

                if (starRocksResult == null || starRocksResult.isEmpty()) {
                    hasMoreStarRocksData = false;
                } else {
                    // 按租户分组，聚合实例id为aiIdList
                    Map<Long, List<Long>> pageEidToAiIdListMap = starRocksResult.stream()
                            .filter(row -> row != null && row.get("eid") != null && row.get("aiId") != null)
                            .collect(Collectors.groupingBy(
                                    row -> Long.valueOf(row.get("eid").toString()),
                                    Collectors.mapping(
                                            row -> Long.valueOf(row.get("aiId").toString()),
                                            Collectors.toList()
                                    )
                            ));

                    // 合并到总的Map中
                    pageEidToAiIdListMap.forEach((eid, aiIdList) ->
                            eidToAiIdListMap.computeIfAbsent(eid, k -> new ArrayList<>()).addAll(aiIdList)
                    );

                    // 如果返回的数据少于分页大小，说明没有更多数据了
                    if (starRocksResult.size() < starRocksPageSize) {
                        hasMoreStarRocksData = false;
                    } else {
                        starRocksPageNum++;
                    }

                    log.info("[dsProcess] StarRocks page {} processed, got {} records", starRocksPageNum - 1, starRocksResult.size());
                }
                // 5. 按租户循环调用batchSaveInstanceToStarRocksAndHBase，upsert=true
                for (Map.Entry<Long, List<Long>> entry : eidToAiIdListMap.entrySet()) {
                    Long eid = entry.getKey();
                    List<Long> aiIdList = entry.getValue();

                    if (!aiIdList.isEmpty()) {
                        AssetSaveBigDataParam saveParam2 = new AssetSaveBigDataParam();
                        saveParam2.setModelCode(modelCode);
                        saveParam2.setUpsert(true);
                        saveParam2.setAiIdList(aiIdList);
                        saveParam2.setEid(eid);
                        saveParam2.setAiopsItem(aiopsItem);

                        // 如果conditionAssetId不为空，设置assetId
//                        if (conditionAssetId != null) {
//                            saveParam2.setAssetId(conditionAssetId);
//                        }

                        BaseResponse saveResult2 = assetService.batchSaveInstanceToStarRocksAndHBase(saveParam2);
                        if (!saveResult2.checkIsSuccess()) {
                            log.error("[dsProcess]租户{}保存失败: {}", eid, saveResult2.getErrMsg());
                        }
                    }
                }

            }
            return ResponseBase.ok();

        } catch (Exception e) {
            log.error("[dsProcess] dsProcess处理失败", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR);
        }
    }
}
