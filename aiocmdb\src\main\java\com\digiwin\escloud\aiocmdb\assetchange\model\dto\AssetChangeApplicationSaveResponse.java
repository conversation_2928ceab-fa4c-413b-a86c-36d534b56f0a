package com.digiwin.escloud.aiocmdb.assetchange.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 资产变更申请单保存响应DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@ApiModel(value = "AssetChangeApplicationSaveResponse", description = "资产变更申请单保存响应")
public class AssetChangeApplicationSaveResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("申请单ID")
    private Long applicationId;

    @ApiModelProperty("申请单编号")
    private String applicationNumber;

    @ApiModelProperty("保存状态消息")
    private String message;

    public AssetChangeApplicationSaveResponse() {
    }

    public AssetChangeApplicationSaveResponse(Long applicationId, String applicationNumber, String message) {
        this.applicationId = applicationId;
        this.applicationNumber = applicationNumber;
        this.message = message;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationNumber() {
        return applicationNumber;
    }

    public void setApplicationNumber(String applicationNumber) {
        this.applicationNumber = applicationNumber;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "AssetChangeApplicationSaveResponse{" +
                "applicationId=" + applicationId +
                ", applicationNumber='" + applicationNumber + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
