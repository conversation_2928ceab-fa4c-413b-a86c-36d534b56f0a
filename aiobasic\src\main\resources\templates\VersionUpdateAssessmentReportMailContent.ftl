<tr>
    <#import "./common/common.macro.ftl" as common>
    <@common.commonScript />
    <td width=960 style="padding-left: 10px;">
        <div style="max-width: 900px;margin: 0 auto;">
            <div style="border-radius: 20px; padding: 10px; text-align: center; position: relative;">
                <img src="cid:imageTitleBackground"
                     style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; border-radius: 20px; z-index: -1;">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 25px;">
                    <img src="cid:imageTitleWhale"
                         style="width: 50px; height: 50px; margin-right: 15px;">
                    <h1 style="margin: 0;">${I18n.versionUpdateReport_assessmentReportTitle}</h1>
                </div>
                <div
                        style="margin-bottom: 10px; background: linear-gradient(to right, transparent, #d9efff 20%, #d9efff 80%, transparent);">
                    <span style="color: #666; font-size: 18px; margin-right: 20px;">${I18n.versionUpdateReport_customerWelcomeMessage}</span>
                    <span style="font-weight: 600; font-size: 18px;">${I18n.versionUpdateReport_assessmentReportSubtitle}</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="15" viewBox="0 0 14 15" fill="none">
                        <ellipse cx="6.64" cy="7.4987" rx="6.64" ry="6.91667" fill="#28E9C2"/>
                        <path
                                d="M5.98663 10.5283L5.97378 10.5411L2.76562 7.34685L3.81026 6.30674L5.98667 8.47371L9.46765 5.00781L10.5123 6.04792L5.99952 10.5411L5.98663 10.5283Z"
                                fill="white"/>
                    </svg>
                </div>
            </div>
            <p style="text-align: center; margin-bottom: 20px; color: #666;">
                ${I18n.versionUpdateReport_assessmentContent?replace('{0}', '<span style="color: #000;font-weight: bold;">' + digiwinSiteName + '</span>')}
            </p>
            <div style="display: flex; gap: 40px;justify-content: space-around;">
                <div style="text-align: center; align-items: center;">
                    <h2 style="margin: 0;">${I18n.versionUpdateReport_assessmentIsRegistered} <span
                                style="font-weight: normal;">${I18n.versionUpdateReport_assessmentDigiwinCloudAccount}</span>
                    </h2>
                    <a href="${loginUrl}"
                       style="display:flex; justify-content: center; align-items: center; width: 300px; height: 30px; color: #FFF; text-decoration: none; padding: 10px 32px 10px 48px; border-radius: 100px; border: 2px solid #488FFF; font-size: 18px; font-weight: 600; margin-top: 10px; background: linear-gradient(0deg, #001A95 38.13%, #153EFF 72.15%, #4865FF 128.76%);">
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="22" viewBox="0 0 21 22" fill="none">
                            <g clip-path="url(#clip0_21635_4447)">
                                <g filter="url(#filter0_d_21635_4447)">
                                    <path
                                            d="M15.4126 14.078C16.6834 12.3438 17.2527 10.1936 17.0063 8.05773C16.76 5.92185 15.7163 3.95773 14.084 2.55833C12.4517 1.15893 10.3512 0.427454 8.20278 0.510238C6.05433 0.593021 4.01636 1.48396 2.49659 3.00482C0.976827 4.52567 0.0873447 6.56427 0.00609857 8.71278C-0.0751476 10.8613 0.657834 12.9612 2.0584 14.5925C3.45897 16.2238 5.42383 17.2661 7.55989 17.5109C9.69595 17.7557 11.8457 17.185 13.579 15.9128H13.5777C13.6162 15.9653 13.6591 16.0157 13.7063 16.0638L18.7594 21.1169C19.0055 21.3632 19.3394 21.5016 19.6876 21.5017C20.0357 21.5019 20.3697 21.3637 20.616 21.1176C20.8622 20.8715 21.0007 20.5376 21.0008 20.1894C21.0009 19.8413 20.8627 19.5073 20.6166 19.261L15.5635 14.2079C15.5166 14.1604 15.4661 14.1178 15.4126 14.078ZM15.7512 9.03272C15.7512 9.9807 15.5645 10.9194 15.2017 11.7952C14.8389 12.671 14.3072 13.4668 13.6369 14.1372C12.9665 14.8075 12.1707 15.3392 11.2949 15.702C10.4191 16.0648 9.48041 16.2515 8.53243 16.2515C7.58445 16.2515 6.64575 16.0648 5.76993 15.702C4.89411 15.3392 4.09832 14.8075 3.428 14.1372C2.75768 13.4668 2.22595 12.671 1.86317 11.7952C1.5004 10.9194 1.31368 9.9807 1.31368 9.03272C1.31368 7.11819 2.07422 5.28208 3.428 3.9283C4.78178 2.57452 6.6179 1.81397 8.53243 1.81397C10.447 1.81397 12.2831 2.57452 13.6369 3.9283C14.9906 5.28208 15.7512 7.11819 15.7512 9.03272Z"
                                            fill="#FFFCB4"/>
                                </g>
                            </g>
                            <defs>
                                <filter id="filter0_d_21635_4447" x="-2" y="-1.49609" width="29" height="28.9961"
                                        filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                    <feColorMatrix in="SourceAlpha" type="matrix"
                                                   values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                                   result="hardAlpha"/>
                                    <feOffset dx="2" dy="2"/>
                                    <feGaussianBlur stdDeviation="2"/>
                                    <feComposite in2="hardAlpha" operator="out"/>
                                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
                                    <feBlend mode="normal" in2="BackgroundImageFix"
                                             result="effect1_dropShadow_21635_4447"/>
                                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_21635_4447"
                                             result="shape"/>
                                </filter>
                                <clipPath id="clip0_21635_4447">
                                    <rect width="21" height="21" fill="white" transform="translate(0 0.5)"/>
                                </clipPath>
                            </defs>
                        </svg>
                        ${I18n.versionUpdateReport_assessmentLink?replace('{0}', '<span style="color: #FFFB00;">')?replace('{1}', '</span>&nbsp;')}
                    </a>
                </div>
                <div style="text-align: center; align-items: center;">
                    <h2 style="margin: 0;">${I18n.versionUpdateReport_assessmentIsNotRegistered} <span
                                style="font-weight: normal;">${I18n.versionUpdateReport_assessmentDigiwinCloudAccount}</span>
                    </h2>
                    <a href="${registerUrl}"
                       style="display:flex; justify-content: center; align-items: center; width: 300px; height: 30px; color: #FFF; text-decoration: none; padding: 10px 32px 10px 48px; border-radius: 100px; border: 2px solid #D0B9FF; font-size: 18px; font-weight: 600; margin-top: 10px; background: linear-gradient(180deg, #8972FF 9.28%, #892DFF 37.8%, #50009F 117.34%);">
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="30" viewBox="0 0 21 30" fill="none">
                            <g clip-path="url(#clip0_21635_4451)">
                                <g filter="url(#filter0_d_21635_4451)">
                                    <path
                                            d="M11.375 8.81348C11.549 8.81348 11.7158 8.88279 11.8389 9.00586C11.9619 9.12893 12.0312 9.29568 12.0312 9.46973C12.0312 9.64377 11.9619 9.81052 11.8389 9.93359C11.7158 10.0567 11.549 10.126 11.375 10.126H2.84375C2.6697 10.126 2.50295 10.1953 2.37988 10.3184C2.25681 10.4414 2.1875 10.6082 2.1875 10.7822V25.2197C2.1875 25.3938 2.25681 25.5605 2.37988 25.6836C2.50295 25.8067 2.6697 25.876 2.84375 25.876H17.2812C17.4553 25.876 17.622 25.8067 17.7451 25.6836C17.8682 25.5605 17.9375 25.3938 17.9375 25.2197V17.3447C17.9375 17.1707 18.0068 17.0039 18.1299 16.8809C18.253 16.7578 18.4197 16.6885 18.5938 16.6885C18.7678 16.6885 18.9345 16.7578 19.0576 16.8809C19.1807 17.0039 19.25 17.1707 19.25 17.3447V25.2197C19.25 25.7419 19.043 26.2431 18.6738 26.6123C18.3046 26.9815 17.8034 27.1885 17.2812 27.1885H2.84375C2.3216 27.1885 1.82038 26.9815 1.45117 26.6123C1.08196 26.2431 0.875 25.7419 0.875 25.2197V10.7822C0.875 10.2601 1.08196 9.75886 1.45117 9.38965C1.82038 9.02044 2.3216 8.81348 2.84375 8.81348H11.375ZM17.6113 13.2705L8.66992 22.2158C8.59757 22.2876 8.50892 22.3412 8.41211 22.373L5.24414 23.4297C5.18652 23.4488 5.12452 23.4514 5.06543 23.4375C5.00621 23.4235 4.95124 23.3926 4.9082 23.3496C4.86541 23.3066 4.83527 23.2524 4.82129 23.1934C4.80735 23.1342 4.80997 23.0723 4.8291 23.0146L5.88574 19.8467C5.91794 19.7497 5.97268 19.6611 6.04492 19.5889L14.9863 10.6455L17.6113 13.2705ZM17.7471 8.15625C17.9211 8.15625 18.0888 8.2256 18.2119 8.34863L19.9082 10.0459V10.0469C20.0308 10.1699 20.0996 10.337 20.0996 10.5107C20.0996 10.6844 20.0308 10.8507 19.9082 10.9736L18.54 12.3438L15.915 9.71875L17.2832 8.34863C17.4062 8.2257 17.5732 8.15636 17.7471 8.15625Z"
                                            fill="white"/>
                                </g>
                            </g>
                            <defs>
                                <filter id="filter0_d_21635_4451" x="-1.125" y="6.15625" width="27.2266"
                                        height="27.0312"
                                        filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                    <feColorMatrix in="SourceAlpha" type="matrix"
                                                   values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                                   result="hardAlpha"/>
                                    <feOffset dx="2" dy="2"/>
                                    <feGaussianBlur stdDeviation="2"/>
                                    <feComposite in2="hardAlpha" operator="out"/>
                                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
                                    <feBlend mode="normal" in2="BackgroundImageFix"
                                             result="effect1_dropShadow_21635_4451"/>
                                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_21635_4451"
                                             result="shape"/>
                                </filter>
                                <clipPath id="clip0_21635_4451">
                                    <rect width="21" height="29" fill="white" transform="translate(0 0.5)"/>
                                </clipPath>
                            </defs>
                        </svg>
                        ${I18n.versionUpdateReport_assessmentLink1?replace('{0}', '<span style="color: #FFFB00;">')?replace('{1}', '</span>&nbsp;')}
                    </a>
                </div>
            </div>
        </div>
        <div style="font-size: 16px; font-weight: bold;">
            <p>${description}</p>
            <#if serviceArea == "TW">
                <div style="height:30px"></div>
                <p>
                    <span style="line-height: 20px;">${I18n.versionUpdateReport_assessmentPlatFormRouteTitle?replace('{0}', digiwinSiteName)}：</span><br>
                    <span style="line-height: 24px;">&#x1F449;${I18n.versionUpdateReport_assessmentPlatFormRouteTitle1?replace('{0}', digiwinCompanyName)}：</span><br>
                    <a href="${downloadAddress}"
                       style="color: #488fff; text-decoration: none">${I18n.versionUpdateReport_assessmentPlatFormRoute1?replace('{0}', digiwinCompanyName)?replace('{1}', digiwinSiteName)}</a>
                    <br><br>
                    <span style="line-height: 24px;">&#x1F449;${I18n.versionUpdateReport_assessmentPlatFormRouteTitle2?replace('{0}', digiwinSiteName)}：</span><br>
                    <a href="${misAddress}"
                       style="color: #488fff; text-decoration: none">${I18n.versionUpdateReport_assessmentPlatFormRoute2?replace('{0}', digiwinCompanyName)}</a>
                </p>
                <div style="height:10px"></div>
                <p style="font-weight: normal;">
                    ${I18n.versionUpdateReport_assessmentSupportContactInfo?replace('{0}', digiwinCompanyName)}
                </p>
            </#if>
            <table>
                <tr>
                    <td height=20px></td>
                </tr>
            </table>
        </div>
    </td>
</tr>
<tr>
    <td width=960 style="padding-left: 10px;font-size: 12px;color: #99979a">
        <p>內部自動發送郵件請勿回覆</p>
    </td>
</tr>
<tr>
    <td width=960>
        <img src="cid:imageMailFooter"></img>
    </td>
</tr>
<tr>
    <td width=960 style="padding-left: 10px;font-size: 14px">
        <#if language == "TW">
            <p>
                鼎捷(鼎新數智股份有限公司、鼎華智能股份有限公司、鼎捷數智股份有限公司及鼎捷軟件越南有限公司)將妥善保管您的個人資料，並於合法取得之前提下善意使用，據此本公司僅在營運範圍內之目的與您聯繫，包含鼎捷主辦或協辦之行銷活動、客戶服務、供應商聯繫等，非經由本公司上開目的下之合法授權，所寄發之資訊並不代表本公司。本電子郵件及附件所載訊息均為保密資訊，受合約保護或依法不得洩漏。其內容僅供指定收件人按限定範圍或特殊目的使用。未經授權者收到此資訊者均無權閱讀、使用、
                複製、洩漏或散佈。若您因為誤傳而收到本郵件或者非本郵件之指定收件人，煩請即刻回覆郵件或並永久刪除此郵件及其附件和銷毀所有複印件。倘若有前述情形或信件誤遞至您的信箱或有相關問題，請透過下列方式聯繫更正；mail：<EMAIL>。謝謝您的合作！</p>
        <#elseif language == "CN">
            <p>
                鼎捷(鼎捷数智股份有限公司、鼎新数智股份有限公司、鼎华智能系统有限公司及鼎捷软件越南有限公司)将妥善保管您的个人资料，并于合法取得之前提下善意使用，据此本公司仅在营运范围内之目的与您联系，包含鼎捷主办或协办之营销活动、客户服务、供应商联系等，非经由本公司上开目的下之合法授权，所寄发之信息并不代表本公司
                。本电子邮件及附件所载讯息均为保密信息，受合约保护或依法不得泄漏。其内容仅供指定收件人按限定范围或特殊目的使用。未经授权者收到此信息者均无权阅读、
                使用、
                复制、泄漏或散布。若您因为误传而收到本邮件或者非本邮件之指定收件人，烦请即刻回复邮件或并永久删除此邮件及其附件和销毁所有复印件。倘若有前述情形或邮件误递至您的邮箱或有相关问题，请透过下列方式联系更正；mail：<EMAIL>。谢谢您的合作！</p>
        </#if>
    </td>
</tr>