package com.digiwin.escloud.aioitms.report.service.db.impl;

import com.digiwin.escloud.aioitms.report.annotation.DbTypeCode;
import com.digiwin.escloud.aioitms.report.model.db.DbReportRecord;
import com.digiwin.escloud.aioitms.report.model.db.SqlServerErpReport;
import com.digiwin.escloud.aioitms.report.service.db.impl.product.SqlServerErpBase;
import com.digiwin.escloud.common.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

@DbTypeCode("MSSQL_ERP")
@Service
public class SqlServerErpData extends SqlServerErpBase<SqlServerErpReport> {

    @Override
    protected List<Callable<Object>> getReportItems(DbReportRecord dbReportRecord) {
        List<Callable<Object>> reportItems = super.getReportItems(dbReportRecord);

        // 取潛客代號
        dbReportRecord.setCustomerCode(getCustomerCode(dbReportRecord.getEid()));
        if (dbReportRecord.getCustomerCode() == null) {
            return reportItems;
        }

        reportItems.add(buildProductInfo(dbReportRecord));
        reportItems.add(buildProductCheck(dbReportRecord));
        reportItems.add(buildDeviceCheck(dbReportRecord));
        reportItems.add(buildErp2Check(dbReportRecord.getCustomerCode()));
        reportItems.add(buildOtherCheck(getEidListByCustomerCode(dbReportRecord.getCustomerCode())));

        return reportItems;
    }

    private String getCustomerCode(Long eid) {
        // 以 eid 取潛客代號
        StringBuilder sb = new StringBuilder();
        sb.append("select coalesce(aci.customerCode, at.customerCode) as customerCode ");
        sb.append("from servicecloud.ACP_Customer_Info aci ");
        sb.append("full outer join servicecloud.AiopsTenant at ");
        sb.append("on aci.eid = at.eid ");
        sb.append("where (aci.eid = '").append(eid).append("' or at.eid = '").append(eid).append("')");
        String sql = StringUtil.toString(sb);
        if (bigDataUtil.srQuery(sql).isEmpty()) {
            return null;
        }

        return bigDataUtil.srQuery(sql).get(0).get("customerCode").toString();
    }

    private List<String> getEidListByCustomerCode(String customerCode) {
        // 以潛客代號取 eid
        StringBuilder sb = new StringBuilder();
        sb.append("select distinct eid ");
        sb.append("from ( ");
        sb.append("select eid from servicecloud.ACP_Customer_Info where customerCode = '").append(customerCode).append("' ");
        sb.append("union ");
        sb.append("select eid from servicecloud.AiopsTenant where customerCode = '").append(customerCode).append("' ");
        sb.append(") as eidList ");
        sb.append("where eid is not null and eid != ''");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);

        return data.stream().map(m -> m.get("eid").toString()).collect(Collectors.toList());
    }
}
