package com.digiwin.escloud.issueservice.t.model.cases.dto;

import com.digiwin.escloud.issueservice.model.IssueKbshare;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 前端新增案件页面对应的DTO
 * Created by zx on 2018/3/29 10:54
 */
@Data
@ApiModel("案件提报页面信息实体")
public class CasesFormDTO {
    @ApiModelProperty(value = "服务中心代号")
    private String serviceRegion; //服务中心
    @ApiModelProperty(value = "客服代号")
    private String serviceCode;
    @ApiModelProperty(value = "产品编号")
    private String productCode;
    @ApiModelProperty(value = "产品名称, T100/TOPGP")
    private String productCategory;
    @ApiModelProperty(value = "是否是紧急案件")
    private boolean emergency;
    @ApiModelProperty(value = "是否现场处理过")
    private boolean liveProcess;
    @ApiModelProperty(value = "问题大分类编号")
    private String issueCatelogCode;
    @ApiModelProperty(value = "问题大分类名称")
    private String issueCatelogName;
    @ApiModelProperty(value = "问题分类编号")
    private String issueClassification;
    @ApiModelProperty(value = "问题分类名称")
    private String issueClassificationDesc;
    @ApiModelProperty(value = "不处理问题分类编号")
    private String issueNotDealCode;
    @ApiModelProperty(value = "不处理问题分类名称")
    private String issueNotDealDesc;
    @ApiModelProperty(value = "问题作业编号")
    private String programCode;
    @ApiModelProperty(value = "问题作业所属模组编号")
    private String erpSystemCode;
    @ApiModelProperty(value = "问题发生环境，正式/测试")
    private String environment;
    @ApiModelProperty(value = "问题发生营运据点")
    private String site;//营运据点
    @ApiModelProperty(value = "问题发生企业编号")
    private String ent; // 企业编号
    @ApiModelProperty(value = "预估工时")
    private double estimatedWorkHours   ; //预估时数
    @ApiModelProperty(value = "期望完成时间")
    private String expectedCompletionDate; //预计完成日期
    private String requExpectCompletionDate;//个案期望完成时间
    private String requPlanCompletionDate;//个案计划完成时间
    @ApiModelProperty(value = "问题标题")
    private String questionTitle;//案件标题
    @ApiModelProperty(value = "问题描述")
    private String issueDescription;
    @ApiModelProperty(value = "联系方式id")
    private long contactId;
    @ApiModelProperty(value = "问题反馈人姓名")
    private String username;
    @ApiModelProperty(value = "问题反馈人电话")
    private String phone;//反馈人电话
    @ApiModelProperty(value = "问题反馈人用户userid，没有传空null")
    private String userId;
    @ApiModelProperty(value = "问题反馈人邮箱")
    private String email;//反馈人邮箱
    @ApiModelProperty(value = "问题填写人userId")
    private String submitedId;
    @ApiModelProperty(value = "问题填写人姓名")
    private String submitedName;
    @ApiModelProperty(value = "内部提报的时候，传案件处理人员userId列表，外部提报不传")
    private List<String> account;
    private String isChange;//是否与窗口人员匹配
    //客户家管理员email
    private String clientAdminEmail;
    @ApiModelProperty(value = "外部客户提报的时候，给反馈人发送邮件需要抄送的邮件列表")
    private List<String> customerCcMails;

    private String remark;//备注
    private String contractNo;//合同号
    private String projectNo;//项目号
    private String issueStatus;//案件状态
    private Integer issueLevel; //问题难度等级，取值1，2，3 45
    private Long coefficientId; // 系数id
    private String submitWay;//来自哪个系统azzi932orT100服务云comes->SubmitWay
    private IssueKbshare issueKbshare; //案件提交最佳知识库解答
//    private String username;//反映人员
//    private boolean followed;
//    private List<String> accountList;
//    private String jiaofuType;
//    private String createTime;




}
