@startuml
title 网安稽查报告生成时序图 (方案A：数据库报告方案)

participant "后台服务(网安稽查服务)" as NetworkSecurity
participant "后台服务(一键体检服务)" as ExamService  
participant "Mysql" as MySQL
participant "后台服务(数据库报告服务)" as DbReportService
participant "大数据平台" as BigData
participant "ES" as ES
participant "后台服务(AI服务)" as AIService

activate NetworkSecurity

Note over NetworkSecurity: saveNetworkSecurityExamReport2Es方法开始执行

== 查询体检记录分数 ==
NetworkSecurity -> ExamService: getReportScore(aerId, scale)
activate ExamService
ExamService -> MySQL: 查询体检记录数据
activate MySQL
MySQL --> ExamService: 返回分数数据
deactivate MySQL
ExamService --> NetworkSecurity: 返回分数数据
deactivate ExamService

== 查询指标检测值数据 ==
NetworkSecurity -> DbReportService: 查询指标检测值数据
activate DbReportService
DbReportService -> BigData: 查询指标检测值数据
activate BigData
BigData --> DbReportService: 返回指标检测值数据
deactivate BigData
DbReportService --> NetworkSecurity: 返回指标检测值数据
deactivate DbReportService

== 查询指标参考值数据 ==
NetworkSecurity -> DbReportService: 查询指标参考值数据
activate DbReportService
DbReportService -> MySQL: 查询指标参考值数据
activate MySQL
MySQL --> DbReportService: 返回指标参考值数据
deactivate MySQL
DbReportService --> NetworkSecurity: 返回指标参考值数据
deactivate DbReportService

Note over NetworkSecurity: 整理参考值、检测值数据\n按照一定规则拼接检测值

== 查询预警数据 ==
NetworkSecurity -> BigData: warningService.getDailyWarningInfo()
activate BigData
BigData --> NetworkSecurity: 返回预警数据
deactivate BigData

Note over NetworkSecurity: 组合报告数据和分数数据、预警数据

== 生成报告结论 ==
NetworkSecurity -> AIService: 发起报告结论生成请求
activate AIService
Note over AIService: 调用IndepthAI服务生成报告结论
AIService --> NetworkSecurity: 返回报告结论
deactivate AIService

== 存储报告详情 ==
NetworkSecurity -> ES: networkSecurityExamEsReport.generateReport(record)
activate ES
Note over ES: 存储报告详情\n现在报告指标分数数据实时查询\n因检测值的不同查询逻辑需存储ES\n原来的组织类数据不会再次存储
ES --> NetworkSecurity: 存储完成
deactivate ES

== 更新报告记录 ==
NetworkSecurity -> MySQL: aiopsExamRecordMapper.updateReportStatus()
activate MySQL
MySQL --> NetworkSecurity: 更新完成
deactivate MySQL

Note over NetworkSecurity: 报告生成完成

deactivate NetworkSecurity

@enduml