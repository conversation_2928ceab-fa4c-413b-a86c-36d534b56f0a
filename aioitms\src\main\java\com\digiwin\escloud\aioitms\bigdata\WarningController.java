package com.digiwin.escloud.aioitms.bigdata;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aioitms.authorize.dao.ITenantDao;
import com.digiwin.escloud.aioitms.bigdata.model.WaringQueryDTO;
import com.digiwin.escloud.aioitms.bigdata.service.WarningNoticeService;
import com.digiwin.escloud.aioitms.bigdata.service.WarningService;
import com.digiwin.escloud.aioitms.bigdata.v2.DataServiceV2;
import com.digiwin.escloud.aioitms.collectwarning.dao.CollectWarningMapper;
import com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning;
import com.digiwin.escloud.aioitms.instance.service.IInstanceService;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Date 2021/6/29 10:32
 * @Created yanggld
 * @Description
 */
@Slf4j
@RequestMapping("/warning")
@RestController
public class WarningController {

    @Value("${api.bigdata.url}")
    private String bigDataUrl;
    @Autowired
    private ITenantDao iTenantDao;
    @Autowired
    private BigDataUtil bigDataUtil;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CollectWarningMapper collectWarningMapper;
    @Autowired
    private DataServiceV2 dataServiceV2;
    @Autowired
    private WarningNoticeService warningNoticeService;
    @Autowired
    private WarningService warningService;

    @Autowired
    private IInstanceService instanceService;

    @ApiOperation(value = "预警通知查询")
    @GetMapping("/notice")
    public ResponseBase warningNoticeList(@RequestParam String param, @RequestParam(defaultValue = "1") int pageNum, @RequestParam(defaultValue = "10") int pageSize) {
        return ResponseBase.ok(warningNoticeService.warningNoticeList(param, pageNum, pageSize));
    }


    @ApiOperation(value = "预警通知详情")
    @GetMapping("/notice/info")
    public ResponseBase warningNoticeInfo(@RequestParam String key) {
        String sql = "select * from warningdetail where key ='" + key + "'";
        List<Map<String, Object>> queryResult = bigDataUtil.query(sql);
        if (!CollectionUtils.isEmpty(queryResult)) {
            Map<String, Object> map = queryResult.get(0);
            return ResponseBase.ok(map);
        }
        return ResponseBase.ok();
    }


    @ApiOperation(value = "预警通知读取")
    @PostMapping("/notice/read")
    public ResponseBase updateNoticeReadStatus(@RequestParam String key) throws Exception {
        return ResponseBase.ok(warningNoticeService.updateNoticeReadStatus(key) > 0);
    }

    @ApiOperation(value = "查询服务代号")
    @GetMapping("/service/code")
    public BaseResponse serviceCode(@RequestParam long eid) {
        long sid = RequestUtil.getHeaderSid();
        Map<String, Object> supplierTenantMap = iTenantDao.findSupplierTenantMapBySidAndEid(sid, eid);
        if (!ObjectUtils.isEmpty(supplierTenantMap)) {
            String serviceCode = supplierTenantMap.getOrDefault("serviceCode", "").toString();
            return BaseResponse.ok(serviceCode);
        }
        return BaseResponse.ok("");
    }

    @Deprecated
    @ApiOperation(value = "预警列表")
    @PostMapping("/list")
    public BaseResponse list(@RequestParam(required = false) String serviceCode, @RequestBody ImpalaQuery q) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 数据集
            convertEid(serviceCode, q);
            List<Map<String, Object>> result = bigDataUtil.queryImpalaQuery(q);
            fillServiceCode(result);
            fillData(result);
            // 查询数据
            ImpalaQuery q2 = new ImpalaQuery();
            q2.setCondFields(q.getCondFields());
            List<String> fieldList = new ArrayList<>();
            fieldList.add("count(*) as count");
            q2.setFields(fieldList);
            q2.setTableName(q.getTableName());
            List<Map<String, Object>> result2 = bigDataUtil.queryImpalaQuery(q2);
            if (!CollectionUtils.isEmpty(result2)) {
                resultMap.put("count", result2.get(0).get("count"));
            } else {
                resultMap.put("count", 0);
            }
            resultMap.put("data", result);
            return BaseResponse.ok(resultMap);
        } catch (Exception ex) {
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }

    @Deprecated
    @ApiOperation(value = "预警列表")
    @PostMapping("/list/v2")
    public BaseResponse listV2(@RequestParam(required = false) String serviceCode, @RequestBody ImpalaQuery q) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            bigDataUtil.buildAttentionEid(q);
        } catch (Exception ex) {
            resultMap.put("count", 0);
            resultMap.put("data", new ArrayList<>());
            return BaseResponse.ok(resultMap);
        }
        try {
            // 数据集
            convertEid(serviceCode, q);
            List<Map<String, Object>> result = bigDataUtil.queryImpalaQuery(q);
            fillServiceCode(result);
            fillData(result);
            // 查询数据
            ImpalaQuery q2 = new ImpalaQuery();
            q2.setCondFields(q.getCondFields());
            List<String> fieldList = new ArrayList<>();
            fieldList.add("count(*) as count");
            q2.setFields(fieldList);
            q2.setTableName(q.getTableName());
            List<Map<String, Object>> result2 = bigDataUtil.queryImpalaQuery(q2);
            if (!CollectionUtils.isEmpty(result2)) {
                resultMap.put("count", result2.get(0).get("count"));
            } else {
                resultMap.put("count", 0);
            }
            resultMap.put("data", result);
            return BaseResponse.ok(resultMap);
        } catch (Exception ex) {
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }

    @ApiOperation(value = "预警列表")
    @PostMapping("/list/v3")
    public BaseResponse listV3(@RequestBody WaringQueryDTO dto) {
        try {
            return BaseResponse.ok(warningService.warningPageList(dto));
        } catch (Exception ex) {
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }

    @ApiOperation(value = "预警列表V4 给AI智能体使用")
    @PostMapping("/list/v4")
    public BaseResponse listV4(@RequestBody WaringQueryDTO dto) {
        try {
            return BaseResponse.ok(warningService.listV4(dto));
        } catch (Exception ex) {
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }

    @ApiOperation(value = "预警level統計")
    @PostMapping("/level/count/v3")
    public BaseResponse levelCountV3(@RequestBody WaringQueryDTO dto) {
        try {
            // 一般的統計
            Map<String, Object> totalMap = warningService.warningLevelCount(dto);
            List<Object> tatalList = (List)totalMap.get("data");
            
            // 是否紧急：urgency
            dto.setUrgency(1);
            Map<String, Object> urgentMap = warningService.warningLevelCount(dto);
            List<Object> urgentList = (List)urgentMap.get("data");
            int urgentSum = urgentList.stream().filter(item -> item instanceof Map)
                    .map(item -> (Map) item)
                    .filter(item -> Objects.nonNull(item))
                    .flatMap(item -> item.values().stream())
                    .filter(value -> value instanceof Integer)
                    .mapToInt(value -> (Integer) value)
                    .sum();
            tatalList.add(new HashMap<String, Object>(){{
                put("warninglevel", "URGENT");
                put("count", urgentSum);
            }});
            
            // 是否时智能预测：intelligentPrediction
            dto.setUrgency(null);
            dto.setIntelligentPrediction(true);
            Map<String, Object> intelligentMap = warningService.warningLevelCount(dto);
            List<Object> intelligentList = (List)intelligentMap.get("data");

            int intelligentSum = intelligentList.stream().filter(item -> item instanceof Map)
                    .map(item -> (Map) item)
                    .filter(item -> Objects.nonNull(item))
                    .flatMap(item -> item.values().stream())
                    .filter(value -> value instanceof Integer)
                    .mapToInt(value -> (Integer) value)
                    .sum();
            tatalList.add(new HashMap<String, Object>(){{
                    put("warninglevel", "INTELLIGENT");
                    put("count", intelligentSum);
            }});

            return BaseResponse.ok(tatalList);
        } catch (Exception ex) {
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }

//    @ApiOperation(value = "取得维实例放置點")
//    @GetMapping(value = "/instance/common/info")
//    public BaseResponse aiopsInstancePlacePoint(
//            @ApiParam(value = "运维实例Id", required = true) @RequestParam("aiId") Long aiId) {
//        try {
//            return instanceService.getAiTrustCommonInfoByAiId(aiId);
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            return BaseResponse.error(ex);
//        }
//    }

    @ApiOperation(value = "（不）紧急预警项统计")
    @GetMapping("/warningitem/count")
    public BaseResponse warningItemCount(
            @RequestParam(required = false) String eid,
            @RequestParam(required = false) Boolean urgency,
            @RequestParam(required = false) boolean filterUserAttention) {
        try {
            return BaseResponse.ok(warningService.warningItemCount(eid,urgency,filterUserAttention));
        } catch (Exception ex) {
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }

    @ApiOperation(value = "（不）紧急客户预警统计")
    @GetMapping("/tenant/count")
    public BaseResponse tenantCount(@RequestParam(required = false) String eid,@RequestParam(required = false) Boolean urgency,@RequestParam(required = false)boolean filterUserAttention,@RequestParam(required = false) Integer tenantModuleContractStatus) {
        try {
            return BaseResponse.ok(warningService.tenantCount(eid,urgency,filterUserAttention,tenantModuleContractStatus));
        } catch (Exception ex) {
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }


    @Deprecated
    private void convertEid(String serviceCode, ImpalaQuery q) {
        if (StringUtils.isNotEmpty(serviceCode)) {
            long sid = RequestUtil.getHeaderSid();
            List<Map<String, Object>> list = iTenantDao.findSupplierTenantMapBySidAndServiceCode(sid, serviceCode);
            if (!CollectionUtils.isEmpty(list)) {
                Map<String, Object> map = list.get(0);
                Object eidObj = map.get("eid");
                if (eidObj != null && StringUtils.isNotEmpty(eidObj.toString())) {
                    List<String> condFields = q.getCondFields();
                    if (CollectionUtils.isEmpty(condFields)) {
                        condFields = new ArrayList<>();
                    }
                    condFields.add("eid='" + eidObj + "'");
                    q.setCondFields(condFields);
                    return;
                }
            }
            List<String> condFields = q.getCondFields();
            if (CollectionUtils.isEmpty(condFields)) {
                condFields = new ArrayList<>();
            }
            condFields.add("eid='" + serviceCode + "'");
            q.setCondFields(condFields);
            return;
        }
    }

    private void fillData(List<Map<String, Object>> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        List<Long> warningItemIds = new ArrayList<>(result.size());
        for (Map<String, Object> map : result) {
            Object warningItemIdObj = map.get("warningitemid");
            try {
                long warningItemId = Long.parseLong(warningItemIdObj.toString());
                if (!warningItemIds.contains(warningItemId)) {
                    warningItemIds.add(warningItemId);
                }
            } catch (Exception ex) {
            }
        }
        if (CollectionUtils.isEmpty(warningItemIds)) {
            return;
        }
        List<CollectWarning> collectWarnings = collectWarningMapper.selectCollectWarningByIds(warningItemIds);
        for (Map<String, Object> map : result) {
            Object warningItemIdObj = map.get("warningitemid");
            try {
                long warningItemId = Long.parseLong(warningItemIdObj.toString());
                for (CollectWarning collectWarning : collectWarnings) {
                    long collectWarningId = collectWarning.getId();
                    if (collectWarningId == warningItemId) {
                        map.put("accId", collectWarning.getAccId());
                        map.put("acwId", collectWarningId);
                    }
                }
            } catch (Exception ex) {
            }
        }
    }

    private void fillServiceCode(List<Map<String, Object>> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        List<Long> eids = new ArrayList<>(result.size());
        for (Map<String, Object> map : result) {
            Object eidObj = map.get("eid");
            try {
                long eid = Long.parseLong(eidObj.toString());
                if (!eids.contains(eid)) {
                    eids.add(eid);
                }
            } catch (Exception ex) {
            }
        }
        if (CollectionUtils.isEmpty(eids)) {
            return;
        }
        long headerSid = RequestUtil.getHeaderSid();
        List<Map<String, Object>> supplierTenantMapList = iTenantDao.findSupplierTenantMap(headerSid, eids);
        for (Map<String, Object> map : result) {
            Object eidObj = map.get("eid");
            for (Map<String, Object> stm : supplierTenantMapList) {
                Object stmEidObj = stm.get("eid");
                if (stmEidObj != null && eidObj != null && stmEidObj.toString().equals(eidObj.toString())) {
                    map.put("serviceCode", stm.get("serviceCode"));
                }
            }
        }
    }

    @ApiOperation(value = "预警详情")
    @GetMapping("/info/{key}")
    public ResponseBase infoV3(@PathVariable String key) {
        try {
            return ResponseBase.ok(warningService.warningInfoByKey(key));
        } catch (Exception ex) {
            ex.printStackTrace();
            return ResponseBase.error(ex);
        }
    }


    @ApiOperation(value = "预警详情")
    @GetMapping("/detail/info/{key}")
    public ResponseBase detailInfoV2(@PathVariable String key) {
        String sql = "select * from " + bigDataUtil.getSrDbName() +".warning where key = '" + key + "';";
        List<Map<String, Object>> mainData = bigDataUtil.srQuery(sql);
        if (!CollectionUtils.isEmpty(mainData)) {
            Map<String, Object> mainDatum = mainData.get(0);
            String detailKey = MapUtil.getStr(mainDatum, "latestdetailrowkey", "");
            if (StringUtils.isNotBlank(detailKey)) {
                String detailSql = "select * from " + bigDataUtil.getSrDbName() +".warningdetail where key = '" + detailKey + "';";
                List<Map<String, Object>> detailData = bigDataUtil.srQuery(detailSql);
                if (!CollectionUtils.isEmpty(detailData)) {
                    buildExtendDataMap(detailData.get(0));
                    return ResponseBase.ok(detailData.get(0));
                }
            }
        }
        sql = "select * from (select rank() over(partition by warningRowkey order by warningLevelNum desc,warningTime desc) rk,* from " + bigDataUtil.getSrDbName() +".warningdetail where warningRowkey = '" + key + "')t where t.rk=1;";
        mainData = bigDataUtil.srQuery(sql);
        for (Map<String, Object> mainDatum : mainData) {
            buildExtendDataMap(mainDatum);
        }
        Map<String, Object> data = new HashMap<>();
        if (!CollectionUtils.isEmpty(mainData)) {
            data = mainData.get(0);
        }
        return ResponseBase.ok(data);
    }

    @ApiOperation(value = "预警单头和单身详情")
    @PostMapping("/info")
    public ResponseBase info(@RequestBody List<String> rowKeys) {
        List<Map<String, Object>> data = warningService.warningAndDetailInfoByKey(rowKeys);
        return ResponseBase.ok(data);
    }

    @ApiOperation(value = "预警明细列表")
    @GetMapping("/detail/list")
    public ResponseBase detailList(@RequestParam String warningRowKey, @RequestParam(defaultValue = "10") int pageSize, @RequestParam(defaultValue = "1") int pageIndex) {
        List<QueryCondition> list = new ArrayList<>();
        list.add(QueryCondition.builder().field("warningRowkey").op("=").value(warningRowKey).build());
        String countSql = ImpalaHelper.countSql("warningdetail", list);
        List<Map<String, Object>> countDataList = bigDataUtil.query(countSql);
        if (CollectionUtils.isEmpty(countDataList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return ResponseBase.ok(map);
        }
        Map<String, Object> map = countDataList.get(0);
        List<String> fieldNameList = new ArrayList<>();
        fieldNameList.add("*");
        List<QueryCondition> queryConditionList = new ArrayList<>();
        queryConditionList.add(QueryCondition.builder().field("warningRowkey").op("=").value(warningRowKey).build());
        List<OrderCondition> orderConditionList = new ArrayList<>();
        orderConditionList.add(OrderCondition.builder().field("warningTime").order("desc").build());
        String sql = ImpalaHelper.commonSql("warningdetail", fieldNameList, queryConditionList, orderConditionList, pageSize, pageIndex);
        List<Map<String, Object>> detailData = bigDataUtil.query(sql);
        if (!CollectionUtils.isEmpty(detailData)) {
            for (Map<String, Object> detailDatum : detailData) {
                buildExtendDataMap(detailDatum);
            }
        }
        map.put("list", detailData);
        return ResponseBase.ok(map);
    }


    @ApiOperation(value = "预警明细列表(sr)")
    @GetMapping("/v2/detail/list")
    public ResponseBase detailListV2(
            @RequestParam String warningRowKey,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(defaultValue = "1") int pageIndex,
            @RequestParam(required = false, defaultValue = "") String warningLevel,
            @RequestParam(required = false, defaultValue = "") String eventTimeStart,
            @RequestParam(required = false, defaultValue = "") String eventTimeEnd
    ) {
        try {
            Map<String, String> condMap = new HashMap<>();
            condMap.put("warningLevel", warningLevel);
            condMap.put("eventTimeStart", eventTimeStart);
            condMap.put("eventTimeEnd", eventTimeEnd);

            Map<String, Object> map = warningService.detailListSr4WaringCount(warningRowKey, condMap);
            List<Map<String, Object>> detailData = warningService.detailListSr4Waring(
                    warningRowKey, condMap, pageIndex, pageSize
            );
            map.put("list", detailData);
            return ResponseBase.ok(map);
        } catch (Exception ex) {
            ex.printStackTrace();
            return ResponseBase.error(ex);
        }
    }


    @ApiOperation(value = "磁盘数据")
    @PostMapping("/disk/collected")
    public ResponseBase diskCollected(@RequestBody List<QueryCondition> queryConditionList) {
        Object deviceValue = "";
        for (QueryCondition queryCondition : queryConditionList) {
            deviceValue = queryCondition.getValue();
            if (ObjectUtils.isEmpty(deviceValue)) {
                return ResponseBase.ok(new ArrayList<>());
            }
        }
//        String sql = "select distinct \n" +
//                "get_json_object(model,'$.DataContent.disk__path') as disk__path,get_json_object(model,'$.DataContent.disk__total') as disk__total,\n" +
//                "get_json_object(model,'$.DataContent.disk__used') as disk__used,\n" +
//                "cast(get_json_object(model,'$.DataContent.disk__used_percent') as double) as disk__used_percent,a.collectedTime\n" +
//                "from diskcollected  a\n" +
//                "inner join \n" +
//                "(\n" +
//                "  select max(collectedTime) collectedTime\n" +
//                "  from diskcollected\n" +
//                "  where deviceId = '" +deviceValue+ "' "+
//                "  group by deviceId " +
//                ")b\n" +
//                " where a.collectedTime = b.collectedTime and deviceId = '" +deviceValue+ "'";
//        List<Map<String, Object>> data = bigDataUtil.query(sql);
//        data = data.stream().sorted(Comparator.comparing((Function<Map<String, Object>, Double>) map -> (Double)map.get("disk__used_percent")).reversed()).collect(Collectors.toList());
        List<Map<String, Object>> data = dataServiceV2.diskV2(null, null, null, deviceValue.toString());
        return ResponseBase.ok(data);
    }

    /**
     * 变更预警状态 解决/不解决
     *
     * @param paramList
     * @return
     */
    @PostMapping("/status/update")
    public BaseResponse updateStatus(@RequestBody List<Map<String, String>> paramList) {
        String url = bigDataUrl + "/hbase/update/list";
        List<HbasePut> warningPutList = new ArrayList<>();
        for (Map<String, String> map : paramList) {
            String processStatus = map.get("processStatus");
            String remark = map.get("remark");
            String warningNotificationId = map.get("warningNotificationId");
            HbasePut hbasePut = new HbasePut();
            hbasePut.setRowKey(warningNotificationId);
            hbasePut.setCf("info");
            JSONObject data = new JSONObject();
            data.put("status", processStatus);
            if (StringUtils.isNotEmpty(remark)) {
                data.put("remark", remark);
            }
            hbasePut.setData(data);
            warningPutList.add(hbasePut);
        }
        HbasePutList warningHbasePutList = new HbasePutList();
        warningHbasePutList.setTableName("warning");
        warningHbasePutList.setPutList(warningPutList);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        try {
            HttpEntity<HbasePutList> warningRequest = new HttpEntity<>(warningHbasePutList, headers);
            restTemplate.postForObject(url, warningRequest, List.class);
            return BaseResponse.ok("success");
        } catch (Exception ex) {
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }

    private List<Map<String, Object>> query(String sql) {
        String url = bigDataUrl + "/impala/sql/query";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(sql, headers);
            List<Map<String, Object>> result = restTemplate.postForObject(url, request, List.class);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ArrayList<>();
        }
    }

    private void buildExtendData(List<Map<String, Object>> result) {
        for (Map<String, Object> map : result) {
            buildExtendDataMap(map);
        }
    }

    private void buildExtendDataMap(Map<String, Object> map) {
        List<String> detailList = new ArrayList<>();
        String titleList = getVal(map, "titlelist");
        String contentList = getVal(map, "contentlist");
        if (StringUtils.isNotBlank(contentList)) { //huly: 修复漏洞/bug contentList != null && contentList != null 重复 改成 StringUtils.isNotBlank(contentList)
            JSONArray titleJA = JSONObject.parseArray(titleList);
            JSONArray contentJA = JSONObject.parseArray(contentList);
            for (int i = 0; i < contentJA.size(); i++) {
                JSONArray jsonArray = contentJA.getJSONArray(i);
                if (jsonArray != null) {
                    String detail = "";
                    for (int j = 0; j < jsonArray.size(); j++) {
                        String title = titleJA.getString(j);
                        String content = jsonArray.getString(j);

                        //FIXME 暫時解法.
                        //FIXME 之後要從源頭提供, 針對原始數據, 在預警時, 要能二次調整
                        if ("溫濕度設備採集時間".equals(title) || "温湿度设备采集时间".equals(title)) {
                            content = transferUnixTime(content);
                        }
                        detail = detail + title + ":" + content + ";";
                    }
                    if (StringUtils.isNotEmpty(detail)) {
                        detailList.add(detail);
                    }
                }
            }
        }
        map.put("extendData", detailList);
    }

    private String transferUnixTime(String unixTimeStr) {
        try {
            long unix_seconds = Long.parseLong(unixTimeStr);
            //convert seconds to milliseconds
            Date date = new Date(unix_seconds * 1000L);
            // format of the date
            SimpleDateFormat jdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            jdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
            return jdf.format(date);
        } catch (Exception e) {
            e.printStackTrace();
            return unixTimeStr;
        }
    }

    private String getVal(Map<String, Object> map, String key) {
        Object obj = map.get(key);
        if (obj != null) {
            return obj.toString();
        }
        return null;
    }

    /**
     * 预警数量折线图
     *
     * @return
     */
    @GetMapping("/count/cart")
    public BaseResponse warningCountCart(@RequestParam String startTime, @RequestParam String endTime) {
        String sql = "select concat(substr(warningTime,1,13),':00') AS t,sum(CASE  WHEN warningLevel = 'ERROR' THEN 1 ELSE 0 END) AS ERROR_sum," +
                "sum(CASE  WHEN warningLevel = 'WARNING' THEN 1 ELSE 0 END) AS WARNING_sum,sum(CASE  WHEN warningLevel = 'INFO' THEN 1 ELSE 0 END) AS INFO_sum," +
                "sum(CASE  WHEN warningLevel = 'FATAL' THEN 1 ELSE 0 END) AS FATAL_sum from hive_warningdetail " +
                "where warningTime>'" + startTime + "' and warningTime<'" + endTime + "' and status='unsolved' " +
                "group by substr(warningTime,1, 13) order by t";
        List<Map<String, Object>> dataList = bigDataUtil.query(sql);
        Map<String, Object> cartDataMap = new HashMap<>();
        cartDataMap.put("error_sum", "0");
        cartDataMap.put("warning_sum", "0");
        cartDataMap.put("info_sum", "0");
        cartDataMap.put("fatal_sum", "0");
        LocalDateTime startDateTime = bigDataUtil.str2LocalDateTimedf2(startTime);
        LocalDateTime endTimeDateTime = bigDataUtil.str2LocalDateTimedf2(endTime);
        List<Map<String, Object>> data = bigDataUtil.fillChartData(dataList, "t", cartDataMap, startDateTime, endTimeDateTime, null, TimeUnit.HOURS);
        return BaseResponse.ok(data);
    }

    /**
     * 预警分布
     *
     * @return
     */
    @GetMapping("/count/top")
    public BaseResponse warningCountTop(@RequestParam String startTime, @RequestParam String endTime, @RequestParam String deviceId, @RequestParam(defaultValue = "5") int size) {
        String sql = "select warninglevel,warningLevelNum,title,count(*) as count from hive_warning " +
                "where warningTime<'" + endTime + "' and warningTime>'" + startTime + "' and deviceId='" + deviceId + "' " +
                "group by warninglevel,warningLevelNum,title order by warningLevelNum desc,count desc limit " + size;
        List<Map<String, Object>> dataList = bigDataUtil.query(sql);
        return BaseResponse.ok(dataList);
    }

    @ApiOperation(value = "云管家查询预警数据")
    @GetMapping("/scp")
    public BaseResponse warning4scp(@RequestParam(required = false) String deviceId) {
        long headerEid = RequestUtil.getHeaderEid();
        if (headerEid == 0) {
            return BaseResponse.error(new RuntimeException("eid获取失败"));
        }
        String eid = Long.toString(headerEid);
        String sql1 = "select sum(CASE  WHEN warningLevel = 'ERROR' THEN 1 ELSE 0 END) AS errorsum ,sum(CASE  WHEN warningLevel = 'FATAL' THEN 1 ELSE 0 END) AS fatalsum from hive_warning where status='unsolved' and eid='" + eid + "' ";
        if (StringUtils.isNotEmpty(deviceId)) {
            sql1 += "and deviceid='" + deviceId + "'";
        }
        String sql2 = "select count(*) as deviceSum from hive_warning where status='unsolved' and eid='" + eid + "' ";
        if (StringUtils.isNotEmpty(deviceId)) {
            sql2 += "and deviceid='" + deviceId + "'";
        }
        sql2 += " group by deviceid";
        String sql3 = "select count(*) as total from hive_warning where status='unsolved' and eid='" + eid + "' ";
        if (StringUtils.isNotEmpty(deviceId)) {
            sql3 += "and deviceid='" + deviceId + "'";
        }
        List<Map<String, Object>> dataList1 = bigDataUtil.query(sql1);
        List<Map<String, Object>> dataList2 = bigDataUtil.query(sql2);
        List<Map<String, Object>> dataList3 = bigDataUtil.query(sql3);
        Map<String, Object> result = new HashMap<>();
        for (Map<String, Object> map : dataList1) {
            result.put("errorSum", map.get("errorsum") == null ? 0 : map.get("errorsum"));
            result.put("fatalSum", map.get("fatalsum") == null ? 0 : map.get("fatalsum"));
        }
        result.put("deviceSum", dataList2.size());
        for (Map<String, Object> map : dataList3) {
            result.put("total", map.get("total"));
        }
        return BaseResponse.ok(result);
    }

    @ApiOperation(value = "实例最大级别的预警查询")
    @GetMapping("/aiId/maxLevel/query")
    public BaseResponse queryWarningAiIdMaxLevel(@RequestParam Map<String, String> params) {
        String eid = params.get("eid");
        String aiId = params.get("aiId");
        String aiopsItem = params.get("aiopsItem");
        String warningItemCode = params.get("warningItemCode");
        String sql = "select warningLevel warningLevel,count(*) count from (" +
                "    select rank() over(partition by eid,aiId order by warninglevelnum desc) as rk,warninglevel,eid,aiId" +
                "    from warning where eid='" + eid + "' and aiId = '" + aiId + "' and aiopsItem='" + aiopsItem + "' and warningItemCode ='" + warningItemCode + "'" +
                ")tmp where rk = 1 group by warninglevel;";
        return BaseResponse.ok(bigDataUtil.query(sql));
    }
}
