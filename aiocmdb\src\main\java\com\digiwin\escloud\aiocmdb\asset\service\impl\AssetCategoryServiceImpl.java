package com.digiwin.escloud.aiocmdb.asset.service.impl;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper;
import com.digiwin.escloud.aiocmdb.asset.dao.CmdbModelShowFieldMapper;
import com.digiwin.escloud.aiocmdb.asset.dao.CmdbModelShowFieldUserMapper;
import com.digiwin.escloud.aiocmdb.asset.model.*;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetCategoryService;
import com.digiwin.escloud.aiocmdb.asset.service.AssetService;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.service.AssetRelatedMapService;
import com.digiwin.escloud.aiocmdb.etl.dao.EtlMapper;
import com.digiwin.escloud.aiocmdb.model.dao.ModelMapper;
import com.digiwin.escloud.aiocmdb.model.model.Model;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping;
import com.digiwin.escloud.aiocmdb.model.model.ModelGroup;
import com.digiwin.escloud.aiocmdb.model.model.ModelGroupField;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.model.service.IModelService;
import com.digiwin.escloud.aiocmdb.model.vo.ModelVo;
import com.digiwin.escloud.aiocmdb.util.DsUtil;
import com.digiwin.escloud.etl.model.EtlEngine;
import com.digiwin.escloud.aioitms.model.instance.AiopsBaseInstance;
import com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailInfo;
import com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailRequest;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.util.concurrent.CompletableFuture;

import java.util.*;
import java.util.Arrays;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aiocmdb.asset.model.AssetCategory.CreationMode.AUTOMATICALLY_ESTABLISHED;
import static com.digiwin.escloud.aiocmdb.asset.service.impl.AssetServiceImpl.INSTANCE_STATUS_LIST;
import static com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil.SERVICECLOUD_MODEL_DB;

/**
 * <p>
 * 资产类别分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Service
@Slf4j
public class AssetCategoryServiceImpl  implements IAssetCategoryService ,ParamCheckHelp  {

    public static final String CATEGORY_DEFAULT_SCOPE_ID = "CATEGORY_DEFAULT_SCOPE_ID";
    private static final String ASSET_PROJECT_NAME = "aieom_asset";

    @Autowired
    private AssetCategoryMapper assetCategoryMapper;

    @Autowired
    private CmdbModelShowFieldMapper cmdbModelShowFieldMapper;

    @Autowired
    private CmdbModelShowFieldUserMapper cmdbModelShowFieldUserMapper;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private BigDataUtil bigDataUtil;

    @Autowired
    private DsUtil dsUtil;

    @Value("${api.automatically.established.url:http://221.6.15.182:30010/aiogateway/aiocmdb/asset/assetCategory/ds/process}")
    private String automaticallyEstablishedUrl;

    @Value("${api.automatically.established.crontab:0 0 */3 * * ? *}")
    private String automaticallyEstablishedCrontab;

    @Autowired
    private IModelService modelService;
    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;
    @Autowired
    private AssetService assetService;
    @Autowired
    private EtlMapper etlMapper;
    @Autowired
    private AssetRelatedMapService assetRelatedMapService;


    @Override
    public BaseResponse saveAssetCategoryClassification(AssetCategoryClassification classification) {
        // 校验唯一性
        Optional<BaseResponse> optResponse = checkParamIsEmpty(classification.getCategoryType(), "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        int count = assetCategoryMapper.countByCategoryName(classification.getCategoryName(),classification.getCategoryType().name() , null);
        if (count > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NAME_EXISTS);
        }
        classification.setId(SnowFlake.getInstance().newId());
        int result = assetCategoryMapper.insertAssetCategoryClassification(classification);
        if (result > 0) {
            return BaseResponse.ok(classification);
        }
        return BaseResponse.error(ResponseCode.INSERT_FAILD);
    }

    @Override
    public BaseResponse updateAssetCategoryClassification(AssetCategoryClassification classification) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(classification.getCategoryType(), "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        AssetCategoryClassification existCategoryClassification = assetCategoryMapper.selectAssetCategoryClassificationById(classification.getId());

        if (BooleanUtil.isFalse(existCategoryClassification.getCanEdit())) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NOT_EDIT_DELETE);
        }

        if (Objects.nonNull(existCategoryClassification.getParentId()) && !existCategoryClassification.getParentId().equals(classification.getParentId())) {
            int count = assetCategoryMapper.countAssetCategoryByClassificationId(classification.getId());
            if (count > 0) {
                return BaseResponse.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_HAS_CATEGORIES);
            }
        }


        // 校验categoryName唯一性（排除自己）
        int count = assetCategoryMapper.countByCategoryName(classification.getCategoryName(),classification.getCategoryType().name() , classification.getId());
        if (count > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NAME_EXISTS);
        }

        int result = assetCategoryMapper.updateAssetCategoryClassification(classification);
        if (result > 0) {
            return BaseResponse.ok(classification);
        }
        return BaseResponse.error(ResponseCode.UPDATE_FAILD);
    }

    @Override
    public ResponseBase deleteAssetCategoryClassification(Long id) {

        AssetCategoryClassification categoryClassification = assetCategoryMapper.selectAssetCategoryClassificationById(id);

        if (BooleanUtil.isFalse(categoryClassification.getCanDelete())) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NOT_EDIT_DELETE);
        }

        // 校验是否有关联的AssetCategory数据
        int count = assetCategoryMapper.countAssetCategoryByClassificationId(id);
        if (count > 0) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_HAS_CATEGORIES);
        }

        int result = assetCategoryMapper.deleteAssetCategoryClassification(id);
        if (result > 0) {
            return ResponseBase.ok();
        }
        return ResponseBase.error(ResponseCode.DELETE_FAILD);
    }

    @Override
    public BaseResponse getAssetCategoryClassificationList(String categoryType) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(categoryType, "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        List<AssetCategoryClassification> list = assetCategoryMapper.selectAssetCategoryClassificationList(categoryType);

        // 为每个分类统计AssetCategory数量
        for (AssetCategoryClassification classification : list) {
            int count = assetCategoryMapper.countAssetCategoryByClassificationId(classification.getId());
            classification.setCount(count);
        }

        return BaseResponse.ok(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse saveAssetCategory(AssetCategory category) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(category.getCategoryType(), "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        category.setId(SnowFlake.getInstance().newId());
        category.setSid(RequestUtil.getHeaderSid());
        category.setScopeId(CATEGORY_DEFAULT_SCOPE_ID);
        category.setModelCode(category.getCategoryNumber());
        category.setSinkName(category.getCategoryNumber());
        category.setStatus(AssetCategory.Status.ENABLED);
        // 校验sid、scopeId、categoryNumber三字段联合唯一性
        int count = assetCategoryMapper.countBySidScopeIdCategoryNumber(
                category.getSid(), category.getScopeId(), category.getCategoryNumber(), null,category.getCategoryType().name());
        if (count > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_UNIQUE_CONSTRAINT_VIOLATION);
        }

        int nameCount = assetCategoryMapper.countBySidScopeIdCategoryName(
                category.getSid(), category.getScopeId(), category.getCategoryName(), null,category.getCategoryType().name());
        if (nameCount > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_UNIQUE_NAME_CONSTRAINT_VIOLATION);
        }


        int result = assetCategoryMapper.insertAssetCategory(category);

        if (result > 0) {
            // 保存资产类别编号规则
            for (AssetCategoryCodingRuleSettingResult rule : category.getAccrsrList()) {
                rule.setId(SnowFlake.getInstance().newId());
                rule.setObjId(category.getId());
                rule.setObjType(category.getCategoryType().name());
                rule.setCategoryClassificationCode(category.getCategoryClassificationCode());
                assetCategoryMapper.insertAssetCategoryCodingRuleSettingResult(rule);
            }

            if (AUTOMATICALLY_ESTABLISHED.equals(category.getCreationMode())) {
                //更新自动映射规则
                saveCmdbModelDataFieldRelationMapping(category.getCmdfrmList(),category.getModelCode());
            }
            
            // 根据modelCode查询cmdb_model_group_field并填充到cmdb_model_show_field表
            populateModelShowFields(category);

            // 创建中台任务
            createDsProcess(category);
            return BaseResponse.ok(category);
        }

        return BaseResponse.error(ResponseCode.INSERT_FAILD);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse updateAssetCategory(AssetCategory category) {
        // categoryNumber不可编辑，这里不更新categoryNumber字段
        AssetCategory existingCategory = assetCategoryMapper.selectAssetCategoryById(category.getId());
        if (existingCategory == null) {
            return BaseResponse.error(ResponseCode.QUERY_VERIFY);
        }
        category.setSid(RequestUtil.getHeaderSid());
        int nameCount = assetCategoryMapper.countBySidScopeIdCategoryName(
                category.getSid(), category.getScopeId(), category.getCategoryName(), category.getId(),category.getCategoryType().name());
        if (nameCount > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_UNIQUE_NAME_CONSTRAINT_VIOLATION);
        }
        BaseResponse editNumberRb = validateCategoryNumberEditRule(category, existingCategory);
        if (!editNumberRb.checkIsSuccess()) {
            return editNumberRb;
        }
        category.setModelCode(category.getCategoryNumber());
        category.setSinkName(category.getCategoryNumber());
        int result = assetCategoryMapper.updateAssetCategory(category);
        if (result > 0) {

            clearCategoryNumberSettingRule(category, existingCategory);

            assetCategoryMapper.deleteAssetCategoryCodingRuleSettingResult(category.getId());

            // 保存资产类别编号规则
            for (AssetCategoryCodingRuleSettingResult rule : category.getAccrsrList()) {
                rule.setId(SnowFlake.getInstance().newId());
                rule.setObjId(category.getId());
                rule.setObjType(category.getCategoryType().name());
                rule.setCategoryClassificationCode(category.getCategoryClassificationCode());
                assetCategoryMapper.insertAssetCategoryCodingRuleSettingResult(rule);
            }

            //更新自动映射规则
            BaseResponse baseResponse = saveCmdbModelDataFieldRelationMapping(category.getCmdfrmList(),category.getModelCode());
            if (!baseResponse.checkIsSuccess()){
                // 回滚
                throw new RuntimeException("保存CMDB模型字段关系映射失败: " + baseResponse.getErrMsg());
            }
            if (AUTOMATICALLY_ESTABLISHED.equals(category.getCreationMode())) {
                if (LongUtil.isEmpty(existingCategory.getProcessId())) {
                    // 创建中台任务
                    createDsProcess(category);
                }
            } else {
                deleteDsProcess(existingCategory);
            }
            return BaseResponse.ok(category);
        }

        return BaseResponse.error(ResponseCode.UPDATE_FAILD);
    }

    private BaseResponse validateCategoryNumberEditRule(AssetCategory newCategory, AssetCategory existingCategory) {
        if (existingCategory.getCategoryNumber().equals(newCategory.getCategoryNumber())
                && existingCategory.getClassificationId().equals(newCategory.getClassificationId())) {
            return BaseResponse.ok();
        }

        Map<String, Long> starRocksCountMap = getStarRocksCountMap(Collections.singletonList(existingCategory.getCategoryNumber()),null,false);
        Long usageCount = starRocksCountMap.getOrDefault(existingCategory.getCategoryNumber(), 0L);

        if (usageCount > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_NUMBER_CANT_EDIT);
        }
        return BaseResponse.ok();
    }

    private BaseResponse clearCategoryNumberSettingRule(AssetCategory newCategory, AssetCategory existingCategory) {
        // 从数据库获取已存在的规则列表
        List<AssetCategoryCodingRuleSettingResult> existingList =
                assetCategoryMapper.selectAssetCategoryCodingRuleSettingResultByObjId(existingCategory.getId());

        // 从新对象中获取要比较的规则列表
        List<AssetCategoryCodingRuleSettingResult> newList = newCategory.getAccrsrList();

        // 调用专门的比较方法
        if (areRuleIdListsIdentical(existingList, newList)) {
            // 如果列表完全一致，则什么都不做，直接返回成功
            return BaseResponse.ok();
        }

        // 如果列表不一致，执行缓存清除操作
        assetRelatedMapService.removeRangeAssetCodeCache(existingCategory.getModelCode(), existingCategory.getSinkName());
        log.info("清除资产类别编号缓存: {}:{}" , existingCategory.getModelCode() , existingCategory.getSinkName());
        return BaseResponse.ok();
    }

    /**
     * 比较两个规则列表的ruleId是否在顺序和值上都完全一致。
     * @param listA 第一个列表
     * @param listB 第二个列表
     * @return 如果完全一致返回 true，否则返回 false
     */
    private boolean areRuleIdListsIdentical(List<AssetCategoryCodingRuleSettingResult> listA, List<AssetCategoryCodingRuleSettingResult> listB) {
        // 1. 处理 null 的情况
        // 如果两个引用指向同一个对象（包括两个都为null），则它们相等。
        if (listA == listB) {
            return true;
        }
        // 如果执行到这里，说明两者引用不同。如果其中一个是null，那么它们必然不相等。
        if (listA == null || listB == null) {
            return false;
        }

        // 2. 至此，可以确定 listA 和 listB 都不是 null，可以安全调用 .size()
        if (listA.size() != listB.size()) {
            return false;
        }

        // 3. 逐个比较相同下标元素的 ruleId
        for (int i = 0; i < listA.size(); i++) {
            AssetCategoryCodingRuleSettingResult itemA = listA.get(i);
            AssetCategoryCodingRuleSettingResult itemB = listB.get(i);

            // 额外的健壮性检查：防止列表中的元素为 null
            if (itemA == null || itemB == null) {
                // 如果要求列表中的元素也不能为null，或者一个为null一个不为null，则认为不一致
                if (itemA != itemB) return false;
                    // 如果 itemA 和 itemB 都为 null，则跳过本次循环，继续比较下一个
                else continue;
            }

            Long ruleIdA = itemA.getRuleId();
            Long ruleIdB = itemB.getRuleId();

            // 使用 Objects.equals 安全地处理 ruleId 可能为 null 的情况
            if (!Objects.equals(ruleIdA, ruleIdB)) {
                return false;
            }

            // 判断 ruleSettingValue 是否相等
            String ruleSettingValueA = itemA.getRuleSettingValue();
            String ruleSettingValueB = itemB.getRuleSettingValue();
            if (!Objects.equals(ruleSettingValueA, ruleSettingValueB)) {
                return false;
            }
        }

        // 4. 如果循环正常结束，说明所有元素的 ruleId 都一致
        return true;
    }

    @Override
    public ResponseBase updateAssetCategoryStatus(Long id, String status) {

        AssetCategory.Status newStatus = AssetCategory.Status.valueOf(status);
        // 1. 查询对象
        AssetCategory category = assetCategoryMapper.selectAssetCategoryById(id);
        if (category == null) {
            return ResponseBase.error(ResponseCode.QUERY_VERIFY);
        }

        // 标记是否为“停用”操作，提高可读性，避免重复判断
        boolean isDisabling = newStatus == AssetCategory.Status.DISABLED;

        // 2. 停用前的业务校验
        if (isDisabling) {
            int count = countAssetBySinkName(category.getSinkName());
            if (count > 0) {
                return ResponseBase.error(ResponseCode.ASSET_CATEGORY_HAS_ASSET);
            }
        }

        // 3. 更新数据库状态
        category.setStatus(newStatus);
        int result = assetCategoryMapper.updateAssetCategory(category);

        // 如果数据库更新失败，直接返回错误，不执行后续操作
        if (result <= 0) {
            return ResponseBase.error(ResponseCode.UPDATE_FAILD);
        }

        // 4. 数据库更新成功后，再执行与下游系统的交互（关键逻辑修正）
        updateDsSystem(category, isDisabling);

        // 5. 所有操作成功
        return ResponseBase.ok(category);
    }

    /**
     * 私有辅助方法，封装与下游系统交互的逻辑，使主流程更清晰
     * @param category 资产分类对象
     * @param isDisabling 是否是停用操作
     */
    private void updateDsSystem(AssetCategory category, boolean isDisabling) {
        if (isDisabling) {
            offLineDsProcess(category);
        } else {
            onLineDsProcess(category);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBase deleteAssetCategory(Long id) {
        AssetCategory category = assetCategoryMapper.selectAssetCategoryById(id);
        if (category == null) {
            return ResponseBase.error(ResponseCode.QUERY_VERIFY);
        }
//        if (category.getStatus().equals(AssetCategory.Status.ENABLED)) {
//            return ResponseBase.error(ResponseCode.DELETE_FAILD);
//        }

        // 删除前需要进行额外的校验，预留位置
        int count = countAssetBySinkName(category.getSinkName());

        if (count > 0) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_HAS_ASSET);
        }
        int result = assetCategoryMapper.deleteAssetCategory(id);

        if (result > 0) {
            assetCategoryMapper.deleteAssetCategoryCodingRuleSettingResult(id);
            clearCategoryNumberSettingRule(new AssetCategory(), category);
            // 删除中台定时任务
            deleteDsProcess(category);
            // 删除cmdb_model_show_field表
            cmdbModelShowFieldMapper.deleteByModelCode(category.getModelCode(), category.getSid());
            cmdbModelShowFieldUserMapper.deleteByModelCode(category.getModelCode(), category.getSid());
            // 清空模型数据
            bigDataUtil.srSave(" truncate table " + SERVICECLOUD_MODEL_DB + category.getModelCode());
            return ResponseBase.ok();
        }

        return ResponseBase.error(ResponseCode.DELETE_FAILD);
    }

    @Override
    public BaseResponse getAssetCategoryList(AssetCategoryQueryParam queryParam) {

        if (queryParam.getNeedPaging()) {
            // 分页查询
            int pageNum = queryParam.getPageNum() == null || queryParam.getPageNum() <= 0 ? 1 : queryParam.getPageNum();
            int pageSize = queryParam.getPageSize() == null || queryParam.getPageSize() <= 0 ? 10 : queryParam.getPageSize();

            PageHelper.startPage(pageNum, pageSize);
            List<AssetCategory> list = assetCategoryMapper.selectAssetCategoryListWithPaging(queryParam);
            PageInfo<AssetCategory> pageInfo = new PageInfo<>(list);

            if (list.isEmpty()) {
                return BaseResponse.ok(pageInfo);
            }

            // 填充相关数据
            populateAssetCategoryData(list,queryParam.getEid());

            return BaseResponse.ok(pageInfo);
        } else {
            // 不分页查询，只支持根据classificationId搜索
            List<AssetCategory> list = assetCategoryMapper.selectAssetCategoryListWithoutPaging(queryParam.getClassificationId(),
                    queryParam.getCategoryType(), queryParam.getCategoryNumber());

            // 填充相关数据
            populateAssetCategoryData(list,queryParam.getEid());

            return BaseResponse.ok(list);
        }
    }

    /**
     * 填充AssetCategory相关数据：Count、ModelData、CmdfrmList、AccrsrList
     *
     * @param list AssetCategory列表
     */
    private void populateAssetCategoryData(List<AssetCategory> list,Long eid) {
        if (list.isEmpty()) {
            return;
        }

        // 提取modelCode列表
        List<String> modelCodeList = list.stream()
                .map(AssetCategory::getModelCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 获取StarRocks中的数量统计
        Map<String, Long> starrocksCountMap = getStarRocksCountMap(modelCodeList, eid,false);
        Map<String, Long> useStatusStarrocksCountMap = getStarRocksCountMap(modelCodeList, eid,true);

        // 获取Model数据映射
        Map<String, Model> modelMap = getModelMap(modelCodeList);

        // 为每个category填充数据
        for (AssetCategory category : list) {
            // 设置编码规则数据
            List<AssetCategoryCodingRuleSettingResult> ruleResults =
                    assetCategoryMapper.selectAssetCategoryCodingRuleSettingResultByObjId(category.getId());
            category.setAccrsrList(ruleResults);

            // 设置映射规则数据
            List<CmdbModelDataFieldRelationMapping> cmdfrmList =
                    assetCategoryMapper.selectCmdbModelDataFieldRelationMappingByTargetModelCode(category.getModelCode());
            category.setCmdfrmList(cmdfrmList);

            // 设置模型数据
            category.setModelData(modelMap.get(category.getModelCode()));

            // 设置数量
            if (category.getModelCode() != null) {
                category.setCount(starrocksCountMap.getOrDefault(category.getModelCode(), 0L));
                category.setUseCount(useStatusStarrocksCountMap.getOrDefault(category.getModelCode(), 0L));
            }
        }
    }

    /**
     * 获取StarRocks中各modelCode对应的数量统计
     *
     * @param modelCodeList modelCode列表
     * @return modelCode到数量的映射
     */
    private Map<String, Long> getStarRocksCountMap(List<String> modelCodeList, Long eid, boolean useStatus) {

        Map<String, Long> starrocksCountMap = new HashMap<>();

        if (!modelCodeList.isEmpty()) {
            // 先查询etl_engine中是否有sinkType='starrocks'的数据
            List<String> validModelCodes = new ArrayList<>();
            for (String modelCode : modelCodeList) {
                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("modelCode", modelCode);
                queryMap.put("sinkType", "starrocks");
                EtlEngine etlEngine = etlMapper.getMainEtlEngine(queryMap);
                if (etlEngine != null) {
                    validModelCodes.add(modelCode);
                }
            }

            // 只有存在starrocks配置的modelCode才进行查询
            if (!validModelCodes.isEmpty()) {
                StringJoiner sqlBuilder = new StringJoiner(" UNION ALL ");
                for (String modelCode : validModelCodes) {
                    StringBuilder queryBuilder = new StringBuilder();
                    if (LongUtil.isNotEmpty(eid)) {
                        queryBuilder.append(String.format("SELECT '%s' as model_code, COUNT(*) as count FROM %s%s WHERE eid = '%s'",
                                modelCode, SERVICECLOUD_MODEL_DB, modelCode, eid));
                        if (useStatus) {
                            queryBuilder.append(" AND useStatus !='Scrapped'");
                        }
                    } else {
                        queryBuilder.append(String.format("SELECT '%s' as model_code, COUNT(*) as count FROM %s%s",
                                modelCode, SERVICECLOUD_MODEL_DB, modelCode));
                        if (useStatus) {
                            queryBuilder.append(" WHERE useStatus !='Scrapped'");
                        }
                    }
                    sqlBuilder.add(queryBuilder.toString());
                }


                String finalSql = sqlBuilder.toString();

                List<Map<String, Object>> countResults = bigDataUtil.starrocksQuery(finalSql);

                starrocksCountMap = countResults.stream()
                        .collect(Collectors.toMap(
                                map -> (String) map.get("model_code"),
                                map -> {
                                    Object countValue = map.get("count");
                                    return (countValue instanceof Number) ? ((Number) countValue).longValue() : 0L;
                                }
                        ));
            }
        }

        return starrocksCountMap;
    }

    /**
     * 支持传入查询列和过滤条件，因为这是union all所以查询列和过滤条件列每个表都需要有
     *
     * @param request 查询请求参数
     * @return 查询结果
     */
    @Override
    public ResponseBase<List<Map<String, Object>>> queryStarRocksData(StarRocksQueryRequest request) {
        try {
            if (request == null || request.getModelCodeList() == null || request.getModelCodeList().isEmpty()) {
                return ResponseBase.error(ResponseCode.PARAM_VERIFY, "modelCodeList不能为空");
            }

            if (request.getSelectColumns() == null || request.getSelectColumns().isEmpty()) {
                return ResponseBase.error(ResponseCode.PARAM_VERIFY, "selectColumns不能为空");
            }

            // 先查询etl_engine中是否有sinkType='starrocks'的数据
            List<String> validModelCodes = new ArrayList<>();
            for (String modelCode : request.getModelCodeList()) {
                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("modelCode", modelCode);
                queryMap.put("sinkType", "starrocks");
                EtlEngine etlEngine = etlMapper.getMainEtlEngine(queryMap);
                if (etlEngine != null) {
                    validModelCodes.add(modelCode);
                }
            }

            if (validModelCodes.isEmpty()) {
                log.warn("[queryStarRocksData] No valid modelCodes found with starrocks configuration");
                return ResponseBase.ok(new ArrayList<>());
            }

            // 构建查询列字符串
            String selectClause = String.join(", ", request.getSelectColumns());
            if (BooleanUtil.isTrue(request.getDistinct())) {
                selectClause = "DISTINCT " + selectClause;
            }

            // 构建UNION ALL查询
            StringJoiner sqlBuilder = new StringJoiner(" UNION ALL ");
            for (String modelCode : validModelCodes) {
                StringBuilder singleQuery = new StringBuilder();
                singleQuery.append("SELECT ").append(selectClause)
                        .append(" ,'").append(modelCode).append("'").append(" AS modelCode").append(" FROM ").append(SERVICECLOUD_MODEL_DB).append(modelCode);

                // 添加过滤条件
                if (request.getWhereCondition() != null && !request.getWhereCondition().trim().isEmpty()) {
                    singleQuery.append(" WHERE ").append(request.getWhereCondition());
                }

                sqlBuilder.add(singleQuery.toString());
            }

            String finalSql = sqlBuilder.toString();
            log.info("[queryStarRocksData] Executing SQL: {}", finalSql);

            List<Map<String, Object>> results = bigDataUtil.starrocksQuery(finalSql);
            return ResponseBase.ok(results);

        } catch (Exception e) {
            log.error("[queryStarRocksData] Error executing StarRocks query", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据aiopsItemList查询StarRocks数据的包装方法
     * 先根据aiopsItemList查询modelCodeList，然后调用queryStarRocksData方法
     *
     * @param request 查询请求参数，包含aiopsItemList
     * @return 查询结果
     */
    @Override
    public ResponseBase<List<Map<String, Object>>> queryStarRocksDataByAiopsItems(StarRocksQueryRequest request) {
        try {
            if (request == null || request.getAiopsItemList() == null || request.getAiopsItemList().isEmpty()) {
                return ResponseBase.error(ResponseCode.PARAM_VERIFY, "aiopsItemList不能为空");
            }

            // 根据aiopsItemList查询modelCodeList
            List<String> modelCodeList = assetCategoryMapper.selectModelCodeListByAiopsItemList(request.getAiopsItemList());
            
            if (modelCodeList == null || modelCodeList.isEmpty()) {
                log.warn("[queryStarRocksDataByAiopsItems] No modelCodes found for aiopsItems: {}", request.getAiopsItemList());
                return ResponseBase.ok(new ArrayList<>());
            }

            // 创建新的请求对象，设置固定的查询列和modelCodeList
            StarRocksQueryRequest newRequest = new StarRocksQueryRequest();
            newRequest.setModelCodeList(modelCodeList);
            
            // 设置固定的查询列：aiId, assetId, aiopsItem, aiopsItemId
            List<String> selectColumns = Arrays.asList("aiId", "assetId", "aiopsItem", "aiopsItemId","assetCode","assetName");
            newRequest.setSelectColumns(selectColumns);

            newRequest.setWhereCondition(request.getWhereCondition());
            newRequest.setDistinct(request.getDistinct());

            // 调用原有的queryStarRocksData方法
            return queryStarRocksData(newRequest);

        } catch (Exception e) {
            log.error("[queryStarRocksDataByAiopsItems] Error executing StarRocks query by aiops items", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取Model数据映射
     *
     * @param modelCodeList modelCode列表
     * @return modelCode到Model对象的映射
     */
    private Map<String, Model> getModelMap(List<String> modelCodeList) {
        if (modelCodeList.isEmpty()) {
            return new HashMap<>();
        }

        List<Model> modelList = assetCategoryMapper.selectByModelCodeList(modelCodeList);
        return modelList.stream()
                .collect(Collectors.toMap(Model::getModelCode, model -> model));
    }

    @Override
    @Transactional
    public BaseResponse saveCmdbModelDataFieldRelationMapping(List<CmdbModelDataFieldRelationMapping> mappingList,String targetModelCode) {
        if (mappingList == null || mappingList.isEmpty()) {
            // 根据targetModelCode删除表里所有的数据
            assetCategoryMapper.deleteCmdbModelDataFieldRelationMappingByTargetModelCode(targetModelCode);
            return BaseResponse.ok();
        }

        // 根据targetModelCode删除表里所有的数据
        assetCategoryMapper.deleteCmdbModelDataFieldRelationMappingByTargetModelCode(targetModelCode);

        // 新增数据，同时校验targetModelCode和targetModelFieldName的联合唯一性
        for (CmdbModelDataFieldRelationMapping mapping : mappingList) {
            int count = assetCategoryMapper.countByTargetModelCodeAndFieldName(
                    mapping.getTargetModelCode(), mapping.getTargetModelFieldName(),mapping.getTargetModelFieldJsonPath() , null);
            if (count > 0) {
                return BaseResponse.error(ResponseCode.SOMTHING_ALREADY_EXISTS,
                        "targetModelCode and targetModelFieldName combination");
            }
            mapping.setId(SnowFlake.getInstance().newId());
            assetCategoryMapper.insertCmdbModelDataFieldRelationMapping(mapping);
        }

        return BaseResponse.ok(mappingList);
    }

    @Override
    public ResponseBase<List<CmdbModelDataFieldRelationMapping>> getCmdbModelDataFieldRelationMappingList(String targetModelCode) {
        if (!StringUtils.hasText(targetModelCode)) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY);
        }

        List<CmdbModelDataFieldRelationMapping> list =
                assetCategoryMapper.selectCmdbModelDataFieldRelationMappingByTargetModelCode(targetModelCode);
        return ResponseBase.okT(list);
    }

    @Override
    public ResponseBase getAllAssetCategoryCodingRule() {
        List<AssetCategoryCodingRule> list =
                assetCategoryMapper.selectAllAssetCategoryCodingRule();
        return ResponseBase.ok(list);
    }

    @Override
    @Transactional
    public ResponseBase batchUpsertAiopsCollectSink(List<AiopsCollectSink> sinkList) {
        if (sinkList == null || sinkList.isEmpty()) {
            return ResponseBase.ok();
        }
        for (AiopsCollectSink aiopsCollectSink : sinkList) {
            aiopsCollectSink.setId(SnowFlake.getInstance().newId());
        }
        assetCategoryMapper.batchUpsertAiopsCollectSink(sinkList);
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase getAllModel(String modelGroupCode, String modelCode) {
        List<ModelGroup> allModelGroups = modelService.getAllModel(null, modelGroupCode, null, true, null);

        if (allModelGroups == null || allModelGroups.isEmpty()) {
            return ResponseBase.ok(Collections.emptyList());
        }

        long headerSid = RequestUtil.getHeaderSid();
        List<String> modelCodeList = assetCategoryMapper.selectCategoryNumberBySidScopeIdCategoryNumber(headerSid, CATEGORY_DEFAULT_SCOPE_ID);

        allModelGroups.forEach(group -> {
            if (group != null && group.getModellist() != null) {
                List<Model> filteredModels = group.getModellist().stream()
                        .peek(model -> model.setRelatedAsset(modelCodeList.contains(model.getModelCode())))
                        .sorted(Comparator.comparing(Model::isRelatedAsset))
                        .collect(Collectors.toList());
                group.setModellist(filteredModels);
            }
        });

        return ResponseBase.ok(allModelGroups);
    }

    /**
     * 根据sinkName（表名）从StarRocks中计算资产数量。
     *
     * @param sinkName 要查询的表名。
     * @return 资产的数量。如果查询无结果或发生错误，则返回0。
     */
    private int countAssetBySinkName(String sinkName) {
        // --- 安全性检查 ---
        if (sinkName == null || sinkName.trim().isEmpty()) {
            return 0;
        }

        // 拼接SQL语句
        String sql = "select count(1) as cnt from " + SERVICECLOUD_MODEL_DB  + sinkName;

        try {
            List<Map<String, Object>> maps = bigDataUtil.starrocksQuery(sql);

            if (maps == null || maps.isEmpty()) {
                log.warn("StarRocks query returned no results for SQL: {}", sql);
                return 0;
            }

            // 2. 获取第一行数据（也应该是唯一一行）
            Map<String, Object> row = maps.get(0);
            if (row == null || !row.containsKey("cnt")) {
                log.error("Expected column 'cnt' not found in StarRocks query result for SQL: {}", sql);
                return 0;
            }

            // 3. 安全地将结果转换为int
            Object countValue = row.get("cnt");
            if (countValue instanceof Number) {
                return ((Number) countValue).intValue();
            } else if (countValue != null) {
                return Integer.parseInt(countValue.toString());
            } else {
                return 0;
            }

        } catch (NumberFormatException e) {
            log.error("Failed to parse count value from StarRocks. SQL: {}. Error: {}", sql, e.getMessage());
            return 0;
        } catch (Exception e) {
            log.error("An error occurred while counting assets from StarRocks. SQL: {}. Error: {}", sql, e.getMessage(), e);
            return 0;
        }
    }

    private void createDsProcess(AssetCategory assetCategory) {
        if (AUTOMATICALLY_ESTABLISHED.equals(assetCategory.getCreationMode())) {
            HashMap<String, Object> dsParamMap = buildProcessParams(assetCategory);
            dsUtil.createProcessAsync(dsParamMap, processId -> {
                assetCategoryMapper.updateAssetCategoryProcessId(assetCategory.getId(), processId);
                dsUtil.onLineProcessAsync(processId, result -> {
                    log.info("[createDsProcess] create process result: {}", result);
                });
            }, ASSET_PROJECT_NAME);
        }

    }

    private void updateDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        if (AUTOMATICALLY_ESTABLISHED.equals(assetCategory.getCreationMode())) {
            HashMap<String, Object> dsParamMap = buildProcessParams(assetCategory);
            dsUtil.updateProcessAsync(dsParamMap, result -> {
                log.info("[updateDsProcess] update process result: {}", result);
            }, ASSET_PROJECT_NAME);
        } else {
            deleteDsProcess(assetCategory);
        }

    }

    private static boolean extracted(AssetCategory assetCategory) {
        return assetCategory.getProcessId() == null || assetCategory.getProcessId() == 0;
    }

    private void offLineDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        dsUtil.offLineProcessAsync(assetCategory.getProcessId(), aBoolean -> {
            log.info("[offLineDsProcess] offLineProcessAsync result: {} , processId:{}", aBoolean, assetCategory.getProcessId());
        });
    }

    private void onLineDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        dsUtil.onLineProcessAsync(assetCategory.getProcessId(), aBoolean -> {
            log.info("[onLineDsProcess] onLineProcessAsync result: {} , processId:{}", aBoolean, assetCategory.getProcessId());
        });
    }

    @Override
    public ResponseBase deleteDsProcessByCategoryId(Long processId) {
        AssetCategory assetCategory = new AssetCategory();
        assetCategory.setProcessId(processId);
        deleteDsProcess(assetCategory);
        return ResponseBase.ok();
    }

    private void deleteDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        dsUtil.offLineProcessAsync(assetCategory.getProcessId(), aBoolean -> {
           if (aBoolean){
               dsUtil.deleteProcessAsync(assetCategory.getProcessId(), bBoolean -> {
                   log.info("[deleteDsProcess] deleteProcessAsync result: {} , processId:{}", bBoolean, assetCategory.getProcessId());
                   assetCategoryMapper.updateAssetCategoryProcessId(assetCategory.getId(), null);
               },  ASSET_PROJECT_NAME);
           }
            log.info("[offLineDsProcess] offLineProcessAsync result: {} , processId:{}", aBoolean, assetCategory.getProcessId());
        });

    }

    private HashMap<String, Object> buildProcessParams(AssetCategory assetCategory) {
        HashMap<String, Object> params = new HashMap<>(8);
        params.put("id", assetCategory.getId());
        params.put("name", assetCategory.getCategoryNumber());
        params.put("crontab", automaticallyEstablishedCrontab);
        params.put("type", ASSET_PROJECT_NAME);
        params.put("appCode",ASSET_PROJECT_NAME);
        params.put("automaticallyEstablishedUrl", automaticallyEstablishedUrl);
        Map<String, Long> map = new HashMap<>();
        map.put("acId", assetCategory.getId());
        params.put("requestParamsBody", JSON.toJSONString(map));
        params.put("processId", assetCategory.getProcessId());
        return params;
    }

    /**
     * 根据category.modelCode查询cmdb_model_group_field并填充到cmdb_model_show_field表
     *
     * @param category 资产类别
     */
    private void populateModelShowFields(AssetCategory category) {
        try {
            String modelCode = category.getModelCode();
            if (StringUtils.isEmpty(modelCode)) {
                log.warn("[populateModelShowFields] modelCode is empty for category: {}", category.getId());
                return;
            }

            // 0. 先删除已存在的数据
            cmdbModelShowFieldMapper.deleteByModelCode(modelCode, category.getSid());
            log.info("[populateModelShowFields] Deleted existing model show fields for modelCode: {}", modelCode);

            // 1. 根据modelCode查询模型信息获取modelGroupCode
            Model model = modelMapper.getByCode(modelCode);
            if (model == null) {
                log.warn("[populateModelShowFields] Model not found for modelCode: {}", modelCode);
                return;
            }

            String modelGroupCode = model.getModelGroupCode();
            if (StringUtils.isEmpty(modelGroupCode)) {
                log.warn("[populateModelShowFields] modelGroupCode is empty for modelCode: {}", modelCode);
                return;
            }

            // 2. 根据modelGroupCode查询cmdb_model_group_field获取字段信息，设置默认customHide为false
            List<ModelGroupField> modelGroupFields = modelMapper.selectModelGroupField(modelGroupCode,modelCode);
            List<CmdbModelShowField> showFields = new ArrayList<>();

            if (modelGroupFields != null && !modelGroupFields.isEmpty()) {
                for (ModelGroupField groupField : modelGroupFields) {
                    CmdbModelShowField showField = new CmdbModelShowField();
                    showField.setId(SnowFlake.getInstance().newId());
                    showField.setSid(category.getSid());
                    showField.setModelCode(modelCode);
                    showField.setFieldCode(groupField.getFieldCode());
                    showField.setSort(groupField.getSort());
                    showField.setModelFieldGroupCode(groupField.getField().getModelFieldGroupCode());
                    showField.setCustomHide(false); // 默认customHide为false
                    showFields.add(showField);
                }
            }

            // 3. 根据modelCode查询cmdb_model_field_mapping表中的字段映射信息
            List<ModelFieldMapping> fieldMappings = modelMapper.selectFieldMappingsByModelCode(modelCode, category.getSid());
            if (fieldMappings != null && !fieldMappings.isEmpty()) {

                // 过滤掉已存在的fieldCode
                Set<String> existingFieldCodes = showFields.stream()
                        .map(CmdbModelShowField::getFieldCode)
                        .collect(Collectors.toSet());

                int maxSort = showFields.stream()
                        .mapToInt(CmdbModelShowField::getSort)
                        .max()
                        .orElse(0);

                int addedCount = 0;
                for (ModelFieldMapping mapping : fieldMappings) {
                    if (!existingFieldCodes.contains(mapping.getTargetCode())) {
                        CmdbModelShowField showField = new CmdbModelShowField();
                        showField.setId(SnowFlake.getInstance().newId());
                        showField.setSid(category.getSid());
                        showField.setModelCode(modelCode);
                        showField.setFieldCode(mapping.getTargetCode());
                        showField.setSort(++maxSort);
                        showField.setModelFieldGroupCode(mapping.getModelFieldGroupCode());

                        if (!"BasicInfo".equals(mapping.getModelFieldGroupCode())) {
                            showField.setCustomHide(addedCount >= 2);
                            addedCount++;
                        } else {
                            showField.setCustomHide(true);
                        }
                        showFields.add(showField);

                    }
                }
            }

            // 4. 在保存到数据库之前，对字段顺序进行调整
            if (!showFields.isEmpty()) {
                reorderShowFields(showFields);
                int insertCount = cmdbModelShowFieldMapper.batchInsert(showFields);
                log.info("[populateModelShowFields] Successfully inserted {} model show fields for modelCode: {}",
                        insertCount, modelCode);
            } else {
                log.info("[populateModelShowFields] No model show fields to insert for modelCode: {}", modelCode);
            }

            populateUserShowFields(modelCode, category.getSid(), showFields);

        } catch (Exception e) {
            log.error("[populateModelShowFields] Error populating model show fields for category: {}",
                    category.getId(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据modelCode和sid查询cmdb_model_show_field_user数据并为用户增加缺失列
     *
     * @param modelCode 模型编码
     * @param sid 运维商ID
     * @param showFields 显示字段列表
     */
    private void populateUserShowFields(String modelCode, Long sid, List<CmdbModelShowField> showFields) {
        try {
            // 1. 查询cmdb_model_show_field_user表中所有不同的userId
            List<String> userIds = cmdbModelShowFieldUserMapper.selectDistinctUserIdsByModelCodeAndSid(modelCode, sid);
            if (userIds == null || userIds.isEmpty()) {
                log.info("[populateUserShowFields] No users found for modelCode: {}, sid: {}", modelCode, sid);
                return;
            }

            // 2. 为每个用户检查并添加缺失的字段
            List<CmdbModelShowFieldUser> userFieldsToInsert = new ArrayList<>();

            for (String userId : userIds) {
                // 查询该用户已有的字段配置
                List<CmdbModelShowFieldUser> existingUserFields = cmdbModelShowFieldUserMapper.selectByModelCodeAndUserId(modelCode, userId, sid);
                Set<String> existingFieldCodes = existingUserFields.stream()
                        .map(CmdbModelShowFieldUser::getFieldCode)
                        .collect(Collectors.toSet());

                // 检查showFields中哪些字段该用户没有
                for (CmdbModelShowField showField : showFields) {
                    if (!existingFieldCodes.contains(showField.getFieldCode())) {
                        // 为该用户创建缺失的字段配置
                        CmdbModelShowFieldUser userField = new CmdbModelShowFieldUser();
                        userField.setId(SnowFlake.getInstance().newId());
                        userField.setSid(sid);
                        userField.setModelCode(modelCode);
                        userField.setUserId(userId);
                        userField.setFieldCode(showField.getFieldCode());
                        userField.setSort(showField.getSort());
                        userField.setModelFieldGroupCode(showField.getModelFieldGroupCode());
                        userField.setCustomHide(showField.getCustomHide());
                        userFieldsToInsert.add(userField);
                    }
                }
            }

            // 3. 批量插入缺失的用户字段配置
            if (!userFieldsToInsert.isEmpty()) {
                int insertCount = cmdbModelShowFieldUserMapper.batchInsert(userFieldsToInsert);
                log.info("[populateUserShowFields] Successfully inserted {} user field configurations for modelCode: {}",
                        insertCount, modelCode);
            } else {
                log.info("[populateUserShowFields] No missing user field configurations to insert for modelCode: {}", modelCode);
            }

        } catch (Exception e) {
            log.error("[populateUserShowFields] Error populating user show fields for modelCode: {}, sid: {}",
                    modelCode, sid, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase populateModelShowFields(String modelCode, Long sid) {
        if (StringUtils.isEmpty(modelCode)) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY);
        }

        try {
            // 创建一个临时的AssetCategory对象来调用私有方法
            AssetCategory tempCategory = new AssetCategory();
            tempCategory.setModelCode(modelCode);
            tempCategory.setSid(sid);

            populateModelShowFields(tempCategory);

            return ResponseBase.ok();
        } catch (Exception e) {
            log.error("[populateModelShowFields] Error populating model show fields for modelCode: {}", modelCode, e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase deleteCmdbModelShowFieldUser(String userId,String modelCode) {
        if (StringUtils.isEmpty(userId)) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY);
        }

        try {
            int deleteCount = cmdbModelShowFieldUserMapper.deleteByUserId(userId,modelCode);
            log.info("[deleteCmdbModelShowFieldUserByUserId] Successfully deleted {} user field configurations for userId: {}",
                    deleteCount, userId);
            return ResponseBase.ok();
        } catch (Exception e) {
            log.error("[deleteCmdbModelShowFieldUserByUserId] Error deleting user field configurations for userId: {}", userId, e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR);
        }
    }

    /**
     * 对显示字段进行重新排序
     * 将特定字段按要求重新排列：
     * 1. 将通过addedCount >= 2设置customHide的两个字段移到assetName字段之后
     * 2. 将useStatus字段移到上述两个字段之后
     * 3. 其余字段保持原有顺序
     *
     * @param showFields 显示字段列表
     */
    private void reorderShowFields(List<CmdbModelShowField> showFields) {
        if (showFields == null || showFields.isEmpty()) {
            return;
        }

        // 找到特殊字段（非BasicInfo且customHide=false的字段，取前两个）
        List<CmdbModelShowField> specialFields = showFields.stream()
                .filter(field -> !"BasicInfo".equals(field.getModelFieldGroupCode()) && !field.getCustomHide())
                .limit(2)
                .collect(Collectors.toList());

        // 找到useStatus字段
        CmdbModelShowField useStatusField = showFields.stream()
                .filter(field -> "useStatus".equals(field.getFieldCode()))
                .findFirst()
                .orElse(null);

        // 创建新的排序列表，保持原有顺序，但在assetName后插入特殊字段和useStatus
        List<CmdbModelShowField> reorderedFields = new ArrayList<>();
        
        for (CmdbModelShowField field : showFields) {
            String fieldCode = field.getFieldCode();
            
            // 跳过特殊字段和useStatus，它们会在assetName后面插入
            if (specialFields.contains(field) || "useStatus".equals(fieldCode)) {
                continue;
            }
            
            // 添加当前字段
            reorderedFields.add(field);
            
            // 如果当前字段是assetName，则在其后插入特殊字段和useStatus
            if ("assetName".equals(fieldCode)) {
                // 插入特殊字段（前两个）
                reorderedFields.addAll(specialFields);
                
                // 插入useStatus字段
                if (useStatusField != null) {
                    reorderedFields.add(useStatusField);
                }
            }
        }
        if (showFields.stream().map(CmdbModelShowField::getFieldCode).collect(Collectors.toList()).contains("assetName")) {
            // 更新原列表
            showFields.clear();
            showFields.addAll(reorderedFields);
        }



        // 重新设置sort值
        for (int i = 0; i < showFields.size(); i++) {
            showFields.get(i).setSort(i + 1);
        }

        log.info("[reorderShowFields] Reordered {} fields, special fields inserted: {}, useStatus inserted: {}", 
                showFields.size(), specialFields.size(), useStatusField != null ? 1 : 0);
    }

    /**
     * 修复asset_category_coding_rule_setting_result表中categoryClassificationCode为空的数据
     * 通过关联查询找到最顶级的分类代码并更新
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase fixEmptyCategoryClassificationCode() {
        try {
            // 1. 查询asset_category_coding_rule_setting_result表中categoryClassificationCode为空串的数据
            List<AssetCategoryCodingRuleSettingResult> emptyCodeRules = 
                assetCategoryMapper.selectEmptyCategoryClassificationCodeRules();
            
            if (emptyCodeRules.isEmpty()) {
                log.info("没有找到categoryClassificationCode为空的数据");
                return ResponseBase.ok("没有需要修复的数据");
            }
            
            log.info("找到{}条categoryClassificationCode为空的数据需要修复", emptyCodeRules.size());
            
            int successCount = 0;
            int failCount = 0;
            
            for (AssetCategoryCodingRuleSettingResult rule : emptyCodeRules) {
                try {
                    // 2. 根据objId去asset_category中找数据
                    AssetCategory assetCategory = assetCategoryMapper.selectAssetCategoryById(rule.getObjId());
                    if (assetCategory == null) {
                        log.warn("未找到objId={}对应的asset_category数据", rule.getObjId());
                        failCount++;
                        continue;
                    }
                    
                    // 3. 根据classificationId去asset_category_classification表中找数据
                    AssetCategoryClassification classification = 
                        assetCategoryMapper.selectAssetCategoryClassificationById(assetCategory.getClassificationId());
                    if (classification == null) {
                        log.warn("未找到classificationId={}对应的asset_category_classification数据", 
                                assetCategory.getClassificationId());
                        failCount++;
                        continue;
                    }
                    
                    // 4. 根据parentId找到最顶级的asset_category_classification数据
                    String topLevelCode = findTopLevelClassificationCode(classification);
                    if (StringUtils.isEmpty(topLevelCode)) {
                        log.warn("未找到classificationId={}对应的最顶级分类代码", assetCategory.getClassificationId());
                        failCount++;
                        continue;
                    }
                    
                    // 5. 更新asset_category_coding_rule_setting_result表中的categoryClassificationCode
                    int updateResult = assetCategoryMapper.updateCategoryClassificationCode(rule.getId(), topLevelCode);
                    if (updateResult > 0) {
                        log.info("成功更新规则ID={}的categoryClassificationCode为{}", rule.getId(), topLevelCode);
                        successCount++;
                    } else {
                        log.warn("更新规则ID={}的categoryClassificationCode失败", rule.getId());
                        failCount++;
                    }
                    
                } catch (Exception e) {
                    log.error("处理规则ID={}时发生异常", rule.getId(), e);
                    failCount++;
                }
            }
            
            String resultMessage = String.format("修复完成：成功%d条，失败%d条", successCount, failCount);
            log.info(resultMessage);
            
            return ResponseBase.ok(resultMessage);
            
        } catch (Exception e) {
            log.error("修复categoryClassificationCode时发生异常", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR, "修复失败：" + e.getMessage());
        }
    }
    
    /**
     * 递归查找最顶级的分类代码
     * 最顶级的数据特征：parentId为null且code有值
     * 非最顶级的数据特征：parentId不为null且code为空
     */
    private String findTopLevelClassificationCode(AssetCategoryClassification classification) {
        if (classification == null) {
            return null;
        }
        
        // 如果parentId为null，说明是最顶级数据，返回其code
        if (classification.getParentId() == null) {
            return classification.getCode();
        }
        
        // 如果parentId不为null，继续向上查找
        AssetCategoryClassification parentClassification = 
            assetCategoryMapper.selectAssetCategoryClassificationById(classification.getParentId());
        
        return findTopLevelClassificationCode(parentClassification);
    }

    @Override
    public ResponseBase updateAssetCategoryProcessId(Long id, Long processId) {
        try {
            // 参数校验
            if (id == null) {
                return ResponseBase.error(ResponseCode.PARAM_VERIFY, "资产类别ID不能为空");
            }
            
            // 检查资产类别是否存在
            AssetCategory existingCategory = assetCategoryMapper.selectAssetCategoryById(id);
            if (existingCategory == null) {
                return ResponseBase.error(ResponseCode.QUERY_VERIFY, "资产类别不存在");
            }
            
            // 更新流程ID
            int result = assetCategoryMapper.updateAssetCategoryProcessId(id, processId);
            if (result > 0) {
                log.info("成功更新资产类别ID={}的流程ID为{}", id, processId);
                return ResponseBase.ok("更新成功");
            } else {
                log.warn("更新资产类别ID={}的流程ID失败", id);
                return ResponseBase.error(ResponseCode.UPDATE_FAILD, "更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新资产类别流程ID时发生异常，资产类别ID={}, 流程ID={}", id, processId, e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR, "更新失败：" + e.getMessage());
        }
    }

}
