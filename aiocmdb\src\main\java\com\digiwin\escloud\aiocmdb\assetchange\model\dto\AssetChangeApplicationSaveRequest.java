package com.digiwin.escloud.aiocmdb.assetchange.model.dto;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationAssetList;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationExecutionPlan;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeExecutionPlanShutdownStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 资产变更申请单保存请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@ApiModel(value = "AssetChangeApplicationSaveRequest", description = "资产变更申请单保存请求")
@Data
public class AssetChangeApplicationSaveRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("资产变更申请单主表信息")
    private AssetChangeApplication application;

    @ApiModelProperty("变更资产清单")
    private List<AssetChangeApplicationAssetList> assetList;

    @ApiModelProperty("执行计划列表")
    private List<ExecutionPlanWithShutdownStatus> executionPlanList;



    /**
     * 执行计划及其停机状况的组合类
     */
    @ApiModel(value = "ExecutionPlanWithShutdownStatus", description = "执行计划及停机状况")
    @Data
    public static class ExecutionPlanWithShutdownStatus implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty("执行计划信息")
        private AssetChangeApplicationExecutionPlan executionPlan;

        @ApiModelProperty("停机状况信息")
        private AssetChangeExecutionPlanShutdownStatus shutdownStatus;


        @Override
        public String toString() {
            return "ExecutionPlanWithShutdownStatus{" +
                    "executionPlan=" + executionPlan +
                    ", shutdownStatus=" + shutdownStatus +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "AssetChangeApplicationSaveRequest{" +
                "application=" + application +
                ", assetList=" + assetList +
                ", executionPlanList=" + executionPlanList +
                '}';
    }
}
