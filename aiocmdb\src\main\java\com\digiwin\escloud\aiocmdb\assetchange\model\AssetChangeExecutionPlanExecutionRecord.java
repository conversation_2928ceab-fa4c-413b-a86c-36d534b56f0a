package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 执行记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@ApiModel(value = "AssetChangeExecutionPlanExecutionRecord对象", description = "执行记录")
public class AssetChangeExecutionPlanExecutionRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("计划ID")
    private Long planId;

    @ApiModelProperty("执行人用户ID")
    private String executorUserId;

    @ApiModelProperty("执行人名称")
    private String executorName;

    @ApiModelProperty("计划状态")
    private String planStatus;

    @ApiModelProperty("实际计划开始日期")
    private LocalDateTime actualPlanStartDate;

    @ApiModelProperty("实际计划结束日期")
    private LocalDateTime actualPlanEndDate;

    @ApiModelProperty("系统变更前版本")
    private String systemPreChangeVersion;

    @ApiModelProperty("系统变更前描述")
    private String systemPreChangeDescription;

    @ApiModelProperty("系统变更后版本")
    private String systemPostChangeVersion;

    @ApiModelProperty("系统变更后描述")
    private String systemPostChangeDescription;

    @ApiModelProperty("执行说明")
    private String executionExplanation;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public String getExecutorUserId() {
        return executorUserId;
    }

    public void setExecutorUserId(String executorUserId) {
        this.executorUserId = executorUserId;
    }

    public String getExecutorName() {
        return executorName;
    }

    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }

    public String getPlanStatus() {
        return planStatus;
    }

    public void setPlanStatus(String planStatus) {
        this.planStatus = planStatus;
    }

    public LocalDateTime getActualPlanStartDate() {
        return actualPlanStartDate;
    }

    public void setActualPlanStartDate(LocalDateTime actualPlanStartDate) {
        this.actualPlanStartDate = actualPlanStartDate;
    }

    public LocalDateTime getActualPlanEndDate() {
        return actualPlanEndDate;
    }

    public void setActualPlanEndDate(LocalDateTime actualPlanEndDate) {
        this.actualPlanEndDate = actualPlanEndDate;
    }

    public String getSystemPreChangeVersion() {
        return systemPreChangeVersion;
    }

    public void setSystemPreChangeVersion(String systemPreChangeVersion) {
        this.systemPreChangeVersion = systemPreChangeVersion;
    }

    public String getSystemPreChangeDescription() {
        return systemPreChangeDescription;
    }

    public void setSystemPreChangeDescription(String systemPreChangeDescription) {
        this.systemPreChangeDescription = systemPreChangeDescription;
    }

    public String getSystemPostChangeVersion() {
        return systemPostChangeVersion;
    }

    public void setSystemPostChangeVersion(String systemPostChangeVersion) {
        this.systemPostChangeVersion = systemPostChangeVersion;
    }

    public String getSystemPostChangeDescription() {
        return systemPostChangeDescription;
    }

    public void setSystemPostChangeDescription(String systemPostChangeDescription) {
        this.systemPostChangeDescription = systemPostChangeDescription;
    }

    public String getExecutionExplanation() {
        return executionExplanation;
    }

    public void setExecutionExplanation(String executionExplanation) {
        this.executionExplanation = executionExplanation;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AssetChangeExecutionPlanExecutionRecord{" +
            "id = " + id +
            ", planId = " + planId +
            ", executorUserId = " + executorUserId +
            ", executorName = " + executorName +
            ", planStatus = " + planStatus +
            ", actualPlanStartDate = " + actualPlanStartDate +
            ", actualPlanEndDate = " + actualPlanEndDate +
            ", systemPreChangeVersion = " + systemPreChangeVersion +
            ", systemPreChangeDescription = " + systemPreChangeDescription +
            ", systemPostChangeVersion = " + systemPostChangeVersion +
            ", systemPostChangeDescription = " + systemPostChangeDescription +
            ", executionExplanation = " + executionExplanation +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
        "}";
    }
}
