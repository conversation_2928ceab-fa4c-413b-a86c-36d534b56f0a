package com.digiwin.escloud.userv2.chatheader.service.impl;

import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.userv2.chatheader.dao.ISettingDao;
import com.digiwin.escloud.userv2.chatheader.model.ChatHeaderSetting;
import com.digiwin.escloud.userv2.chatheader.service.ISettingService;
import com.digiwin.escloud.userv2.robot.dao.ICommonDao;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;


@Service
@Slf4j
public class SettingSettingService implements ISettingService {
    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    private Long defaultSid;
    @Resource
    private ISettingDao dao;
    @Resource
    private ICommonDao commonDao;
    @Override
    public BaseResponse getChatHeaderSettings(String serviceRegion, String productCode, String lang, int pageNo, int pageSize) {
        Long sid = commonDao.getSidByProductCode(productCode);
        sid = Optional.ofNullable(sid).orElse(RequestUtil.getHeaderSidOrDefault(defaultSid));

        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", sid);
        map.put("serviceRegion", serviceRegion);
        map.put("productCode", productCode);
        map.put("lang", lang);

        Page page = PageHelper.startPage(pageNo, pageSize);
        dao.getChatHeaderSettings(map);
        PageInfo<ChatHeaderSetting> pageInfo = new PageInfo<ChatHeaderSetting>(page);
        return BaseResponse.ok(pageInfo);
    }

    @Override
    public BaseResponse getChatHeaderSetting(String serviceRegion, String productCode, String lang) {
        Long sid = commonDao.getSidByProductCode(productCode);
        sid = Optional.ofNullable(sid).orElse(RequestUtil.getHeaderSidOrDefault(defaultSid));

        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", sid);
        map.put("serviceRegion", serviceRegion);
        map.put("productCode", productCode);
        map.put("lang", lang);

        return BaseResponse.ok(dao.getChatHeaderSetting(map));
    }

    @Override
    public BaseResponse getChatHeaderSettingDetail(long id) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("id", id);
        return BaseResponse.ok(dao.getChatHeaderSettingDetail(map));
    }

    @Override
    public BaseResponse saveChatHeaderSetting(ChatHeaderSetting chatHeader) {
        //兼容A1,现根据产品线code 查mars_product表，如果查到对应的sid，用查到的值，如果没有查到，用默认的值
        Long sid = commonDao.getSidByProductCode(chatHeader.getProductCode());
        sid = Optional.ofNullable(sid).orElse(RequestUtil.getHeaderSidOrDefault(defaultSid));

        chatHeader.setSid(sid);

        BaseResponse responseBase = checkParam(chatHeader);
        if (!responseBase.getCode().equals(ResponseCode.SUCCESS.toString())) {
            return responseBase;
        }

        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", chatHeader.getSid());
        map.put("serviceRegion", chatHeader.getServiceRegion());
        map.put("productCode", chatHeader.getProductCode());

        ChatHeaderSetting setting = dao.getChatHeaderSetting(map);
        if(Objects.nonNull(setting)){
            responseBase.setCode(ResponseCode.EXIST_CHAT_MESSAGE_SETTING.toString());
            responseBase.setErrMsg(ResponseCode.EXIST_CHAT_MESSAGE_SETTING.getMsg());
            return responseBase;
        }

        dao.saveChatHeaderSetting(chatHeader);

        responseBase.setCode(ResponseCode.SUCCESS.toString());
        responseBase.setErrMsg(ResponseCode.SUCCESS.toString());
        return responseBase;
    }

    public BaseResponse checkParam(ChatHeaderSetting chatHeader) {
        BaseResponse responseBase = new BaseResponse();
        if ( StringUtils.isEmpty(chatHeader.getServiceRegion()) || StringUtils.isEmpty(chatHeader.getProductCode())) {
            responseBase.setCode(ResponseCode.PARAM_VERIFY.toString());
            responseBase.setErrMsg(ResponseCode.PARAM_VERIFY.getMsg());
            return responseBase;
        }
        if (StringUtils.isEmpty(chatHeader.getHeaderContentCN())
                && StringUtils.isEmpty(chatHeader.getHeaderContentTH())
                && StringUtils.isEmpty(chatHeader.getHeaderContentVN())
                && StringUtils.isEmpty(chatHeader.getHeaderContentTW())
                && StringUtils.isEmpty(chatHeader.getHeaderContentUS())
        ) {
            responseBase.setCode(ResponseCode.PARAM_VERIFY.toString());
            responseBase.setErrMsg(ResponseCode.PARAM_VERIFY.getMsg());
            return responseBase;
        }
        responseBase.setCode(ResponseCode.SUCCESS.toString());
        responseBase.setErrMsg(ResponseCode.SUCCESS.toString());
        return responseBase;
    }

    @Override
    public BaseResponse updateChatHeaderSetting(ChatHeaderSetting chatHeader) {
        //兼容A1,现根据产品线code 查mars_product表，如果查到对应的sid，用查到的值，如果没有查到，用默认的值
        Long sid = commonDao.getSidByProductCode(chatHeader.getProductCode());
        sid = Optional.ofNullable(sid).orElse(RequestUtil.getHeaderSidOrDefault(defaultSid));
        chatHeader.setSid(sid);
        BaseResponse responseBase = checkParam(chatHeader);
        if (!responseBase.getCode().equals(ResponseCode.SUCCESS.toString())) {
            return responseBase;
        }

        dao.updateChatHeaderSetting(chatHeader);

        responseBase.setCode(ResponseCode.SUCCESS.toString());
        responseBase.setErrMsg(ResponseCode.SUCCESS.toString());
        return responseBase;
    }

    @Override
    public BaseResponse deleteChatHeaderSetting(long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        return BaseResponse.ok(dao.deleteChatHeaderSetting(param));
    }

}
