<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="escloud.customermapper">
    <select id="SelectCustomerContractState" resultType="java.lang.String">
        SELECT ifnull(ContractState,'') as ContractState
        from mars_customerservice
        where CustomerServiceCode = #{serviceCode} and ProductCode = #{productCode}
        limit 0,1
    </select>
    <select id="SelectCustomerServiceInfo" resultType="com.digiwin.escloud.issueservice.model.CustomerServiceInfo">
        SELECT ifnull(ContractState,'') as ContractState,CustomerServiceCode,ProductCode
        from mars_customerservice
        where CustomerServiceCode = #{serviceCode} and ProductCode = #{productCode}
            limit 0,1
    </select>

    <select id="GetCustomerIssueConfirmer" resultType="java.lang.String">
        SELECT a.confirmIds
        FROM mars_servicesetting a
        WHERE a.serviceCode = #{serviceCode} AND a.productCode= #{productCode} AND a.confirm = '1'
    </select>

    <select id="SelectCustomerIssueConfirmSetting" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM mars_servicesetting a
        WHERE a.serviceCode = #{serviceCode} AND a.productCode= #{productCode} AND a.confirm = '1'
    </select>
</mapper>