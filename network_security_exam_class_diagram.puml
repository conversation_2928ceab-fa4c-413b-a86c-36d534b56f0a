@startuml
!define LIGHTBLUE #E0FFFF
!define LIGHTGREEN #F0FFF0

' 布局设置，将主要类放在中心，其他类分散四周
!define CENTER "aiops_exam_record\n体检记录"

class "aiops_exam_record\n体检记录" {
    + id: long
    + 体检id: long
    + sid: long
    + eid: long
    + serviceCode: string
    + customerName: string
    + customerFullName: string
    + examBeginTime: datetime
    + examEndTime: datetime
    + examStatus: string
    + examScore: decimal
    + userId: string
    + userName: string
    + 标题: string
    + 体检环境: string
    --
    + getExamRecord(体检id): List<ExamRecord>
    - getExamInstance(aerId): List<ExamInstance>
    - getExamReportRecord(aerId): List<ExamReportRecord>
    + getNetworkSecurityExaminationModel(aerId): List<NetworkSecurityExaminationModel>
    - getNetworkSecurityProject(modelCode,sid,eid): List<NetworkSecurityProject>
    - buildResultData(ExamReportRecord,ExamInstance,NetworkSecurityProject,NetworkSecurityExaminationModel): List<ExamRecord>
    - computeProcess(ExamReportRecord,ExamInstance,NetworkSecurityProject): List<ExamInstance>
    + getExamEnvList(sid,eid): List<String>
    + saveAiopsExamRecord(ExamRecord): ResponseBase
    + saveSecurityExaminationModel(List modelCode,体检记录Id): ResponseBase
    + saveAiopsExamItemInstanceScore(List<NetworkSecurityProject>,体检记录Id)
    + computeScore(Long aeId, Long aerId, String aiopsItemId,其他资产Id)
    + getAerReport(Long aerId): ResponseBase
    + getModelData(modelCode): ResponseBase
    + getWarningData(eid,startDate ,endDate): ResponseBase
    + saveNetworkSecurityExamReport(AiopsExamRecordsReportRecord reportRecord): BaseResponse
}

class "aiops_exam_item_instance_score\n体检实例" {
    + id: long
    + 体检记录ID: long
    + aiopsItem: string
    + aiopsItemId: string
    + 是否体检完成: boolean
    + 设备得分: decimal
    + 设备得分: decimal
    + 分类生成时间: datetime
    + 体检实例状态: datetime
    + 网安实例类别code: datetime
    + 网安体检实例Id: datetime
    + 体检等级编号: string
}

class "aiops_exam_instance_index_score\n体检设备指标得分" {
    + id: long
    + 体检实例id: long
    + 指标id: long
    + 得分: decimal
    + 标签值: string
    + 分数生成时间: datetime
    + 体检等级编号: string
}

class "aiops_exam_index_score\n体检指标得分" LIGHTBLUE {
    + id: long
    + 体检记录id: long
    + 指标id: long
    + 得分: decimal
    + 分数生成时间: datetime
    + 分数等级编号: string
    + 创建时间: datetime
    + 更新时间: datetime
    --
    + handleScore(ScoreContext context)
}

class "aiops_exam_records_report_records\n网安稽查记录报告记录" LIGHTBLUE {
    + id: long
    + sid: long
    + eid: long
    + 体检记录Id: long
    + serviceCode: string
    + customerName: string
    + customerFullName: string
    + 状态(生成中 未发送 已发送): string
    + 报告生成人: string
    + 报告时间: date
    + 生成时间: datetime
    + 创建时间: datetime
    + 更新时间: datetime
}

class aiops_instance {
    + id: long
    + samcd: long
    + aiopsItemId: string
    --
    + getInstanceDetail(eid): List<AiopsInstance>
}

class cmdb_field {
    + id: long
    + moduleId: long
    + fieldCode: string
    + fieldName: string
    --
    + getModelField(Long moduleId): List<ModelField>
}

class "network_security_examination_project_type\n网安稽查项目类别" LIGHTBLUE {
    + id: long
    + parentCode: string
    + 类别名称: string
    + 类别Code: string
    + modelCode: string
    + modelName: string
    + 创建时间: datetime
    + 更新时间: datetime
    --
    + getProjectType(类别Code,类别名称): List<ProjectType>
}

class "network_security_examination_info_system\nSR 网安稽查信息系统模型" LIGHTGREEN {
    + id: long
    + sid: long
    + eid: long
    + 稽查项目类别code: string
    + 运维项目aiopsItem: string
    + 运维项目id aiopsItemId: string
    + 运维项目实例id aiId: long
    + 系统名称: string
    + 安全保护等级: string
    + 是否备案: boolean
    + 备案编号: string
    + 备注: string
    + 创建时间: datetime
    + 更新时间: datetime
    --
    + getProject(modelCode,pageNum, pageSize): Page<project>
    - getProjectInstanceDetailData(List instanceIdList, eid): List<ProjectModel>
    - getProjectModelData(List projectId): List<ProjectModel>
    + saveProject(modelData, instanceId): ResponseBase
    - saveProjectModelData(modelData, projectId): ResponseBase
    + deleteProject(projectId): ResponseBase
    - deleteProjectModelData(projectId): ResponseBase
    + updateProject(modelData, instanceId): ResponseBase
}

' 布局控制 - 将其他类分散到中心类四周
' 上方
"aiops_exam_item_instance_score\n体检实例" -[hidden]up-> CENTER
"network_security_examination_project_type\n网安稽查项目类别" -[hidden]up-> CENTER

' 下方
"aiops_exam_instance_index_score\n体检设备指标得分" -[hidden]down-> CENTER
"network_security_examination_info_system\nSR 网安稽查信息系统模型" -[hidden]down-> CENTER

' 左侧
"aiops_exam_records_report_records\n网安稽查记录报告记录" -[hidden]left-> CENTER
aiops_instance -[hidden]left-> CENTER

' 右侧
"aiops_exam_index_score\n体检指标得分" -[hidden]right-> CENTER
cmdb_field -[hidden]right-> CENTER

' 使用layout指令强制布局
"aiops_exam_item_instance_score\n体检实例" -[hidden]- "network_security_examination_project_type\n网安稽查项目类别"
"aiops_exam_instance_index_score\n体检设备指标得分" -[hidden]- "network_security_examination_info_system\nSR 网安稽查信息系统模型"
"aiops_exam_records_report_records\n网安稽查记录报告记录" -[hidden]- aiops_instance
"aiops_exam_index_score\n体检指标得分" -[hidden]- cmdb_field

' 定义关联关系
"aiops_exam_record\n体检记录" ||--o{ "aiops_exam_item_instance_score\n体检实例"
"aiops_exam_item_instance_score\n体检实例" ||--o{ "aiops_exam_instance_index_score\n体检设备指标得分"
"aiops_exam_record\n体检记录" ||--o{ "aiops_exam_records_report_records\n网安稽查记录报告记录"
"aiops_exam_record\n体检记录" ||--o{ "aiops_exam_index_score\n体检指标得分"

"network_security_examination_project_type\n网安稽查项目类别" ||--o{ "network_security_examination_info_system\nSR 网安稽查信息系统模型"
"network_security_examination_info_system\nSR 网安稽查信息系统模型" ..> aiops_instance
"network_security_examination_info_system\nSR 网安稽查信息系统模型" ||--|| cmdb_field

"aiops_exam_item_instance_score\n体检实例" ||--|| "network_security_examination_info_system\nSR 网安稽查信息系统模型"

@enduml