package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 执行计划
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Data
@ApiModel(value = "AssetChangeApplicationExecutionPlan对象", description = "执行计划")
public class AssetChangeApplicationExecutionPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("申请单ID")
    private Long applicationId;
    @ApiModelProperty("计划描述")
    private String planDescription;

    @ApiModelProperty("计划开始日期")
    private LocalDate planStartDate;

    @ApiModelProperty("计划结束日期")
    private LocalDate planEndDate;

    @ApiModelProperty("计划状态")
    private String planStatus;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;



    @Override
    public String toString() {
        return "AssetChangeApplicationExecutionPlan{" +
            "id = " + id +
            ", applicationId = " + applicationId +
            ", planStartDate = " + planStartDate +
            ", planEndDate = " + planEndDate +
            ", planStatus = " + planStatus +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
        "}";
    }
}
