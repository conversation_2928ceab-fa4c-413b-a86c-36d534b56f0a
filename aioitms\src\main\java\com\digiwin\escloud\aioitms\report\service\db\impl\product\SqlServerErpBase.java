package com.digiwin.escloud.aioitms.report.service.db.impl.product;

import static com.alibaba.fastjson.JSON.parseArray;

import com.alibaba.fastjson.JSONArray;
import com.digiwin.escloud.aioitms.device.dao.DeviceV2Mapper;
import com.digiwin.escloud.aioitms.device.model.AiopsKitDeviceDataSource;
import com.digiwin.escloud.aioitms.device.model.DeviceInfo;
import com.digiwin.escloud.aioitms.report.dao.SqlServerErpReportMapper;
import com.digiwin.escloud.aioitms.report.model.db.DbReportRecord;
import com.digiwin.escloud.aioitms.report.model.db.SqlServerErpReport;
import com.digiwin.escloud.aioitms.report.service.db.AdvBaseDbData;
import com.digiwin.escloud.aiouser.model.supplier.SupplierProduct;
import com.digiwin.escloud.common.util.IntegerUtil;
import com.digiwin.escloud.common.util.StringUtil;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.digiwin.escloud.aioitms.report.constant.SqlServerEdrReportConst.*;

public class SqlServerErpBase<T extends SqlServerErpReport> extends AdvBaseDbData<T> {
    @Value("${escloud.db.name}")
    private String escloudDb;
    @Autowired
    private DeviceV2Mapper deviceV2Mapper;
    @Autowired
    private SqlServerErpReportMapper sqlServerErpReportMapper;

    protected Callable<Object> buildProductInfo(DbReportRecord dbReportRecord) {
        String customerCode = dbReportRecord.getCustomerCode();

        SqlServerErpReport.ProductInfo productInfo = new SqlServerErpReport.ProductInfo();

        SqlServerErpReport.Erp erp = new SqlServerErpReport.Erp();
        SqlServerErpReport.Erp2 erp2 = new SqlServerErpReport.Erp2();

        // 該潛客代號下的 erp 跟 erp2 模組
        Map<String, List<String>> purchasedProducts = getPurchasedProducts(customerCode);
        erp.setAllPurchased(purchasedProducts.get("erp"));
        erp.setAllProduct(ERP_PRODUCT_LIST);
        erp2.setAllPurchased(purchasedProducts.get("erp2"));
        erp2.setAllProduct(ERP2_PRODUCT_LIST);

        productInfo.setErp(erp);
        productInfo.setErp2(erp2);

        return () -> productInfo;
    }

    protected Callable<Object> buildProductCheck(DbReportRecord dbReportRecord) {
        List<SqlServerErpReport.ProductCheck> productCheckList = new ArrayList<>();
        // 參數帶入設備清單， 透過 deviceId 取得設備資訊 ( Products 包含 erp 與 erp2 )
        dbReportRecord.getDeviceIdList().forEach(deviceId -> {


            DeviceInfo deviceInfo = getDeviceInfo(StringUtil.toString(dbReportRecord.getEid()), deviceId);
            if (deviceInfo != null) {
                SqlServerErpReport.ProductCheck productCheck = new SqlServerErpReport.ProductCheck();

                SqlServerErpReport.ProductPurchased productPurchased =
                        getProductCodeForDeviceId(dbReportRecord.getCustomerCode(), deviceInfo);
                String productValues = String.join("、", getProductValues(productPurchased));

                productCheck.setDeviceName(deviceInfo.getDeviceName());
                productCheck.setProducts(productValues);
                productCheck.setRole("");
                productCheck.setIpAddress(deviceInfo.getIpAddress());

                productCheckList.add(productCheck);
            }
        });

        return () -> productCheckList;
    }

    protected Callable<Object> buildDeviceCheck(DbReportRecord dbReportRecord) {
        String eid = StringUtil.toString(dbReportRecord.getEid());
        String customerCode = dbReportRecord.getCustomerCode();
        // 取得設備 deviceId, 以及相對應 MSSQL dbId 清單
        List<Map<String, Object>> deviceIdAndDbIdList = getDeviceIdAndDbId(dbReportRecord.getDeviceIdList());

        List<SqlServerErpReport.DeviceCheck> deviceCheckList = new ArrayList<>();
        deviceIdAndDbIdList.forEach(deviceIdAndDbId -> {
            String deviceId = ObjectUtils.toString(deviceIdAndDbId.get("deviceId"), "");
            List<String> dbIdList = new ArrayList<>();
            Object dbIdObj = deviceIdAndDbId.get("scoreDbIdList");
            if (dbIdObj instanceof List) {
                for (Object dbId : (List<?>) dbIdObj) {
                    dbIdList.add(String.valueOf(dbId));
                }
            }

            DeviceInfo deviceInfo = getDeviceInfo(eid, deviceId);
            SqlServerErpReport.ProductPurchased productPurchased = getProductCodeForDeviceId(customerCode, deviceInfo);
            // 設備維度，此設備是否有 erp 或 erp2
            boolean erp = !productPurchased.getErp().isEmpty();
            boolean erp2 = !productPurchased.getErp2().isEmpty();

            SqlServerErpReport.DeviceCheck deviceCheck = new SqlServerErpReport.DeviceCheck();
            deviceCheck.setProductPurchased(productPurchased);
            deviceCheck.setEnvironment(getEnvironment(eid, customerCode, deviceId, productPurchased.getErp()));
            if (erp || erp2) { // 沒有 erp 或 erp2 模組的設備為其他設備，不顯示database
                deviceCheck.setDatabase(getDatabaseName(eid, deviceId, dbIdList));
            }

            deviceCheckList.add(deviceCheck);
        });

        return () -> deviceCheckList;
    }

    protected Callable<Object> buildErp2Check(String customerCode) {
        List<String> erp2ProductCodeList = ERP2_PRODUCT_LIST.stream()
                .map(SqlServerErpReport.Product.ProductModel::getCode)
                .collect(Collectors.toList());
        List<Map<String, String>> productCodeListResult = sqlServerErpReportMapper.getProductCode(escloudDb, customerCode, erp2ProductCodeList);
        List<String> productCodeList = productCodeListResult.stream()
                .flatMap(map -> map.values().stream())
                .collect(Collectors.toList());

        List<SqlServerErpReport.DataCheck.ProductsInstallInfo> productsInfoList = new ArrayList<>();
        List<SqlServerErpReport.Version> versionList = new ArrayList<>();
        // 潛客代號下 erp2 所有產品，並透過 productCode 取得產品資訊
        productCodeList.forEach(productCode -> {
            SqlServerErpReport.DataCheck.ProductsInstallInfo productsInfo = new SqlServerErpReport.DataCheck.ProductsInstallInfo();
            productsInfo.setProductName(productCode);
            productsInfo.setFolderPath("");
            productsInfo.setFolderSize("");
            productsInfo.setFileCount("");
            productsInfoList.add(productsInfo);

            SqlServerErpReport.Version version = new SqlServerErpReport.Version();
            version.setProductName(productCode);
            version.setProductVersion("-");
            version.setLicensesCount("-");
            versionList.add(version);
        });

        SqlServerErpReport.DataCheck.HrmInfo hrmInfo = createInfo(SqlServerErpReport.DataCheck.HrmInfo::new);
        hrmInfo.setIsWith(productCodeList.contains("HRM") ? HAS : NO_HAS);

        SqlServerErpReport.DataCheck dataCheck = new SqlServerErpReport.DataCheck();
        dataCheck.setProductsInstallInfo(productsInfoList);
        dataCheck.setHrmInfo(hrmInfo);
        dataCheck.setIsoModule(createInfo(SqlServerErpReport.DataCheck.IsoModule::new));

        SqlServerErpReport.Erp2Check erp2Check = new SqlServerErpReport.Erp2Check();
        erp2Check.setDataCheck(dataCheck);
        erp2Check.setVersionList(versionList);

        return () -> erp2Check;
    }

    protected Callable<Object> buildOtherCheck(List<String> eidList) {
        SqlServerErpReport.CheckItems checkItems = new SqlServerErpReport.CheckItems();
        checkItems.setOtherConnection(createInfo(SqlServerErpReport.CheckItems.OtherConnection::new));
        checkItems.setHasReplication(createInfo(SqlServerErpReport.CheckItems.HasReplication::new));
        checkItems.setHasDecimal(hasDecimal(eidList));
        checkItems.setCrossConnection(createInfo(SqlServerErpReport.CheckItems.CrossConnection::new));
        checkItems.setErpEtimatedHours("");
        checkItems.setErp2EtimatedHours("");

        SqlServerErpReport.OtherCheck otherCheck = new SqlServerErpReport.OtherCheck();
        otherCheck.setBaseSpecification(ALL_SPECIFICATION);
        otherCheck.setCheckItems(checkItems);

        return () -> otherCheck;
    }

    private Map<String, List<String>> getPurchasedProducts(String customerCode) {
        List<String> erpProductCodeList = ERP_PRODUCT_LIST.stream()
                .map(SqlServerErpReport.Product.ProductModel::getCode)
                .collect(Collectors.toList());
        List<String> erp2ProductCodeList = ERP2_PRODUCT_LIST.stream()
                .map(SqlServerErpReport.Product.ProductModel::getCode)
                .collect(Collectors.toList());
        List<Map<String, String>> erpPurchasedResult = sqlServerErpReportMapper.getProductCode(escloudDb, customerCode, erpProductCodeList);
        List<String> erpPurchased = erpPurchasedResult.stream()
                .flatMap(map -> map.values().stream())
                .collect(Collectors.toList());
        List<Map<String, String>> erp2PurchasedResult = sqlServerErpReportMapper.getProductCode(escloudDb, customerCode, erp2ProductCodeList);
        List<String> erp2Purchased = erp2PurchasedResult.stream()
                .flatMap(map -> map.values().stream())
                .collect(Collectors.toList());

        Map<String, List<String>> result = new HashMap<>();
        result.put("erp", erpPurchased);
        result.put("erp2", erp2Purchased);

        return result;
    }

    private List<Map<String, Object>> getDeviceIdAndDbId(List<String> deviceIdList) {
        List<Map<String, Object>> result = new ArrayList<>();

        deviceIdList.forEach(deviceId -> {
            Map<String, Object> map = new HashMap<>();
            map.put("deviceId", deviceId);
            List<String> dbIdList = deviceV2Mapper.selectDeviceDataSourceByDeviceId(deviceId).stream()
                    .filter(data -> "mssql_v2".equals(data.getDbType()) && !data.getIsDelete())
                    .map(AiopsKitDeviceDataSource::getDbId)
                    .collect(Collectors.toList());
            if (!dbIdList.isEmpty()) {
                map.put("scoreDbIdList", dbIdList);
            }
            result.add(map);
        });

        return result;
    }

    private SqlServerErpReport.Environment getEnvironment(String eid, String customerCode, String deviceId, String erp) {
        SqlServerErpReport.Environment environment = new SqlServerErpReport.Environment();

        // 加入 environment 資料
        environment.setDeviceId(deviceId);
        environment.setSrvSpec(getSrvSpec(eid, deviceId, customerCode));
        environment.setWebSites(getWebSites(eid, deviceId));
        environment.setNetworks(getNetworks(eid, deviceId));
        if (!erp.isEmpty()) {
            environment.setErpSetting(getErpSetting(eid, customerCode, deviceId, erp));
        }

        return environment;
    }

    private List<SqlServerErpReport.Database> getDatabaseName(String eid, String deviceId, List<String> dbIdList) {
        List<SqlServerErpReport.Database> databaseList = new ArrayList<>();

        // 加入 database 資料
        dbIdList.forEach(dbId -> {
            SqlServerErpReport.Database database = new SqlServerErpReport.Database();
            database.setDeviceId(deviceId);
            database.setSourceDbId(dbId);
            database.setServerInfo(getServerInfo(eid, dbId));
            database.setDbNameSize(getDbNameSize(eid, dbId));
            databaseList.add(database);
        });
        return databaseList;
    }

    private Map<String, Object> getSrvSpec(String eid, String deviceId, String customerCode) {
        Map<String, Object> srvSpec = new HashMap<>();
        srvSpec.put("osInfo", getOsInfo(eid, deviceId, customerCode));
        srvSpec.put("hardwareInfo", getHardwareInfo(eid, deviceId));

        return srvSpec;
    }

    private List<Map<String, Object>> getWebSites(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("SiteName as siteName, propertyName, propertyLocation, state, bindings ");
        sb.append("from servicecloud.Windows_IISSitesInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.Windows_IISSitesInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        return bigDataUtil.srQuery(sql);
    }

    private List<Map<String, Object>> getNetworks(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_name') as network_adapter_name, ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_mac') as network_adapter_mac, ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_dhcp_enabled') as network_adapter_dhcp_enabled, ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_ip') as network_adapter_ip, ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_subnet_mask[0]') as network_adapter_subnet_mask, ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_gateway') as network_adapter_default_gateway, ");
        sb.append("array_join(cast(get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_dns') as array<string>), ',') as network_adapter_dns ");
        sb.append("from servicecloud.DeviceAdvancedInfoCollected_sr_primary, ");
        sb.append("json_each(parse_json(network_adapter_info)) as network_adapter_info ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("'");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> result = bigDataUtil.srQuery(sql);
        result.forEach(data ->
                data.put("network_adapter_ip", getFirstValidIPv4(StringUtil.toString(data.get("network_adapter_ip"))))
        );

        return result;
    }

    private Map<String, Object> getErpSetting(String eid, String customerCode, String deviceId, String erp) {
        Map<String, Object> erpSetting = new HashMap<>();
        erpSetting.put("software", getSoftware(eid, customerCode, deviceId, erp));
        erpSetting.put("modules", getModules(eid, deviceId, erp));
        erpSetting.put("dgwModules", getDgwModules(customerCode, erp));
        erpSetting.put("companyNameSize", getCompanyNameSize(eid, deviceId));

        return erpSetting;
    }

    private Map<String, Object> getServerInfo(String eid, String sourceDbId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("server_name, concat(product, ' ', edition) as product_name, platform, product_version, language, ");
        sb.append("physical_memory_total_gb as physical_memory, processor_count as processors, collation, dataPath, ");
        sb.append("logPath, backupPath, '' as sqlBinarySort ");
        sb.append("from servicecloud.MsSQLInstanceInfo msli ");
        sb.append("left join ");
        sb.append("(select * from servicecloud.MsSQL_PresetPathInfo ");
        sb.append("where eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.MsSQL_PresetPathInfo ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(")) msppi ");
        sb.append("on msli.eid = msppi.eid and msli.source_db_id = msppi.source_db_id ");
        sb.append("where ");
        sb.append("msli.eid = '").append(eid).append("' and ");
        sb.append("msli.source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("msli.collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.MsSQLInstanceInfo ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        if (data.isEmpty()) {
            return Collections.emptyMap();
        }

        return data.get(0);
    }

    private Map<String, Object> getDbNameSize(String eid, String sourceDbId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("database_name as database_id, ");
        sb.append("round(size/1024, 4) as database_size, ");
        sb.append("physical_name, ");
        sb.append("case ");
        sb.append("when disk_fstype = 0 then 'ROWS' ");
        sb.append("when disk_fstype = 1 then 'LOG' ");
        sb.append("when disk_fstype = 2 then 'FILESTREAM' ");
        sb.append("when disk_fstype = 3 then 'FULLTEXT' ");
        sb.append("end as type_desc ");
        sb.append("from servicecloud.MsSQLDataBaseFileCollected ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.MsSQLDataBaseFileCollected ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") order by binary(lower(database_name)) ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", data.size());
        result.put("totalSize", data.stream().map(m -> new BigDecimal(ObjectUtils.toString(m.get("database_size")))).reduce(BigDecimal.ZERO, BigDecimal::add));
        result.put("list", data);

        return result;
    }

    private Map<String, Object> getOsInfo(String eid, String deviceId, String customerCode) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("get_json_string(parse_json(domain_info), '$.domain_machine_name') as domain_machine_name, ");
        sb.append("'' as db_or_ap, ");
        sb.append("'' as domain_groups, ");
        sb.append("get_json_string(parse_json(os_info), '$.os_product_name') as os_product_name, ");
        sb.append("case ");
        sb.append("when get_json_string(parse_json(os_info), '$.os_bits') = 'win64' then '64-bit' ");
        sb.append("when get_json_string(parse_json(os_info), '$.os_bits') = 'win32' then '32-bit' ");
        sb.append("else get_json_string(parse_json(os_info), '$.os_bits') end as os_bits, ");
        sb.append("get_json_string(parse_json(processor_info), '$.processor_name') as processor_name, ");
        sb.append("get_json_string(parse_json(physical_memory_info), '$.[0].physical_memory_total_gb') as physical_memory_total_gb, ");
        sb.append("get_json_string(parse_json(network_adapter_info), '$.[0].network_adapter_ip') as ipAddress, ");
        sb.append("concat(msli.product, ");
        sb.append("case ");
        sb.append("when starts_with(msli.product_version, '17') then ' 2025 ' ");
        sb.append("when starts_with(msli.product_version, '16') then ' 2022 ' ");
        sb.append("when starts_with(msli.product_version, '15') then ' 2019 ' ");
        sb.append("when starts_with(msli.product_version, '14') then ' 2017 ' ");
        sb.append("when starts_with(msli.product_version, '13') then ' 2016 ' ");
        sb.append("when starts_with(msli.product_version, '12') then ' 2014 ' ");
        sb.append("when starts_with(msli.product_version, '11') then ' 2012 ' ");
        sb.append("when starts_with(msli.product_version, '10') then ' 2008 ' ");
        sb.append("when starts_with(msli.product_version, '9') then ' 2005 ' ");
        sb.append("when starts_with(msli.product_version, '8') then ' 2000 ' ");
        sb.append("else 'Unknown' end, msli.edition) as sql_version ");
        sb.append("from servicecloud.DeviceAdvancedInfoCollected_sr_primary daicsp ");
        sb.append("left join servicecloud.MsSQLInstanceInfo msli ");
        sb.append("on daicsp.deviceId = msli.deviceId ");
        sb.append("where ");
        sb.append("daicsp.eid = '").append(eid).append("' and ");
        sb.append("daicsp.deviceId = '").append(deviceId).append("'");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        if (data.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, Object> result = new HashMap<>(data.get(0));
        // 預設帶入該設備內模組
        DeviceInfo deviceInfo = getDeviceInfo(eid, deviceId);
        SqlServerErpReport.ProductPurchased productPurchased = getProductCodeForDeviceId(customerCode, deviceInfo);
        result.put("erp2products", getProductValues(productPurchased));
        result.put("ipAddress", getFirstValidIPv4(StringUtil.toString(result.get("ipAddress"))));

        return result;
    }

    private List<Map<String, Object>> getHardwareInfo(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("disk__path, disk__fstype, disk__total, disk__free, disk__used, disk__used_percent ");
        sb.append("from servicecloud.DiskCollected_sr_duplicate ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.DiskCollected_sr_duplicate ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        return bigDataUtil.srQuery(sql);
    }

    private Map<String, Object> getSoftware(String eid, String customerCode, String deviceId, String erp) {
        Map<String, Object> result = new HashMap<>();

        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("productVersion, clientModi, serverModi, ");
        sb.append("case when admnt_id = 'VoucherServer' then 'WebDAV' else rtBuilderDataOption end as rtBuilderDataOption, ");
        sb.append("case when admnt_id = 'VoucherServer' ");
        sb.append("then concat('主機類型:', ServerType, '<br/>網址:', Path, '<br/>通訊埠:', Port, '<br/>WebDAV類型:', WebDAVType, '<br/>站台名稱:', SiteName, '<br/>帳號名稱:', UserName) ");
        sb.append("else rtBuilderDataPath end as rtBuilderDataPath, ");
        sb.append("case when admnt_id = 'DMSServer' then 'WebDAV' else (case when relationDataOption = '存放共用路徑' then '有' when relationDataOption = '分散存放於工作站' then '無' else relationDataOption end) end as relationDataOption, ");
        sb.append("case when admnt_id = 'DMSServer' ");
        sb.append("then concat('主機類型:', ServerType, '<br/>網址:', Path, '<br/>通訊埠:', Port, '<br/>WebDAV類型:', WebDAVType, '<br/>站台名稱:', SiteName, '<br/>帳號名稱:', UserName) ");
        sb.append("else (case when relationDataOption = '分散存放於工作站' then '' else relationDataPath end) end as relationDataPath, ");
        sb.append("case when admnt_id = 'DMSServer' then 'WebDAV' else (case when fileServerUNCOption = '存放共用路徑' then '有' when fileServerUNCOption = '分散存放於工作站' then '無' else fileServerUNCOption end) end as fileServerUNCOption, ");
        sb.append("case when admnt_id = 'DMSServer' ");
        sb.append("then concat('主機類型:', ServerType, '<br/>網址:', Path, '<br/>通訊埠:', Port, '<br/>WebDAV類型:', WebDAVType, '<br/>站台名稱:', SiteName, '<br/>帳號名稱:', UserName) ");
        sb.append("else (case when fileServerUNCOption = '分散存放於工作站' then '' else fileServerUNCPath end) end as fileServerUNCPath, ");
        sb.append("isExistERP ");
        sb.append("from servicecloud.ERP_SoftSetting_sr_primary esssp ");
        sb.append("left join ");
        sb.append("(select * from servicecloud.ERP_WebDAVInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_WebDAVInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(")) ewdisp ");
        sb.append("on esssp.eid = ewdisp.eid and esssp.deviceId = ewdisp.deviceId ");
        sb.append("where ");
        sb.append("esssp.eid = '").append(eid).append("' and ");
        sb.append("esssp.deviceId = '").append(deviceId).append("' and ");
        sb.append("esssp.collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_SoftSetting_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");


        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        if (data.isEmpty()) {
            return result;
        }
        result.putAll(data.get(0));

        // 憑證設定選項(WebDAV)
        result.put("rtBuilderDataWebDAV", getWebDAV(eid, deviceId, "VoucherServer"));

        // 圖檔設定選項(WebDAV)
        result.put("relationDataWebDAV", getWebDAV(eid, deviceId, "DMSServer"));

        // 文檔設定選項(WebDAV)
        result.put("fileServerUNCWebDAV", getWebDAV(eid, deviceId, "DMSServer"));

        // ERP產品線的授權人數
        Integer licensesCount = sqlServerErpReportMapper.getLicensesCount(escloudDb, customerCode, erp);
        result.put("licensesCount", licensesCount == null ? 0 : licensesCount);

        // POS產品線的授權人數
        Integer posLicensesCount = sqlServerErpReportMapper.getLicensesCount(escloudDb, customerCode, "POS");
        result.put("posLicensesCount", posLicensesCount == null ? 0 : posLicensesCount);

        // AUS(自動更新) 欄位
        result.put("aus", getAus(eid, deviceId));

        return result;
    }

    private Map<String, Object> getModules(String eid, String deviceId, String erp) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("moduleCode as code ");
        sb.append("from servicecloud.ERPModuleInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("erpProduct = '").append(erp).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERPModuleInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("erpProduct = '").append(erp).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        // 取得erp模組代碼
        List<String> moduleCodeList = data.stream()
                .map(dataMap -> Objects.toString(dataMap.get("code"), ""))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        // 跟erp模組資料表比對，過濾掉表內無此模組的資料
        if (!moduleCodeList.isEmpty()) {
            data = sqlServerErpReportMapper.matchErpModules(escloudDb, erp, moduleCodeList);
        }

        return getModulesInfo(data);
    }

    private Map<String, Object> getDgwModules(String customerCode, String erp) {
        Map<String, Object> result;

        List<Map<String, Object>> data = sqlServerErpReportMapper.getDgwModules(escloudDb, customerCode, erp);
        result = getModulesInfo(data);

        return result;
    }

    private Map<String, Object> getCompanyNameSize(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("database_erp, database_id, erp_dscmb002, database_size ");
        sb.append("from servicecloud.ERP_CompanyDatabaseInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_CompanyDatabaseInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", data.size());
        result.put("totalSize", data.stream().map(m -> new BigDecimal(ObjectUtils.toString(m.get("database_size")))).reduce(BigDecimal.ZERO, BigDecimal::add));
        result.put("list", data);

        return result;
    }

    private DeviceInfo getDeviceInfo(String eid, String deviceId) {
        Map<String, Object> param = new HashMap<>();
        param.put("eid", eid);
        param.put("deviceNameOrId", deviceId);

        return deviceV2Mapper.getDeviceList(param).isEmpty() ? null : deviceV2Mapper.getDeviceList(param).get(0);
    }

    private List<String> getProductValues(SqlServerErpReport.ProductPurchased productPurchased) {
        String erp = productPurchased.getErp();
        List<String> erp2 = productPurchased.getErp2();

        // !erp.isEmpty() -> erp 設備 -> 只顯示 erp
        // erp.isEmpty() && !erp2.isEmpty() -> erp2設備 -> 只顯示 erp2
        // 其他設備 -> 不顯示
        List<String> productValueList = new ArrayList<>();

        if (!erp.isEmpty()) {
            productValueList.add(erp);
        }
        if (erp.isEmpty() && !erp2.isEmpty()) {
            productValueList.addAll(erp2);
        }

        return productValueList;
    }

    private SqlServerErpReport.ProductPurchased getProductCodeForDeviceId(String customerCode ,DeviceInfo deviceInfo) {
        SqlServerErpReport.ProductPurchased productPurchased = new SqlServerErpReport.ProductPurchased();

        // erp 判定
        if (deviceInfo != null && deviceInfo.getSupplierProductList() != null) {
            deviceInfo.getSupplierProductList().stream()
                    .map(SupplierProduct::getProductCode)
                    .forEach(productCode -> {
                        if (ERP_PRODUCT_LIST.stream().anyMatch(item -> item.getCode().equals(productCode))) {
                            productPurchased.setErp(productCode);
                        }
                    });
        }

        // erp2 判定
        List<String> erp2 = getPurchasedProducts(customerCode).get("erp2");

        productPurchased.setErp(Optional.ofNullable(productPurchased.getErp()).orElse(""));
        productPurchased.setErp2(erp2);

        return productPurchased;
    }

    private String getSevenDayAgo() {
        LocalDate sevenDaysAgo = LocalDate.now().minusDays(7);
        LocalDateTime startOfDay = sevenDaysAgo.atStartOfDay();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        return startOfDay.format(formatter);
    }

    private String getFirstValidIPv4(String ipJson) {
        if (StringUtils.isEmpty(ipJson)) {
            return "";
        }

        JSONArray array = parseArray(ipJson);
        if (array.isEmpty()) {
            return "";
        }

        // 取得清單內第一筆為 IPv4 資料
        return array.stream()
                .filter(String.class::isInstance)
                .map(item -> Objects.toString(item, ""))
                .filter(ip -> IPV4_PATTERN.matcher(ip).matches())
                .findFirst()
                .orElse("");
    }

    private Map<String, Object> getWebDAV(String eid, String deviceId, String admntId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("SiteName as name, UserName as account, '' as password, '' as ip, Port as port, Path as path ");
        sb.append("from servicecloud.ERP_WebDAVInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("admnt_id like '%").append(admntId).append("%' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_WebDAVInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("admnt_id like '%").append(admntId).append("%' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        return bigDataUtil.srQuery(sql).isEmpty() ? new HashMap<>() : bigDataUtil.srQuery(sql).get(0);
    }

    private Map<String, Object> getAus(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("protocol, name, account, password, aus_ip as ip, port, path, enabled, '' as isWith ");
        sb.append("from servicecloud.ERP_AUSInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("protocol not in ('', '-99') and ");
        sb.append("enabled <> 0 and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_AUSInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("' and ");
        sb.append("protocol not in ('', '-99') and ");
        sb.append("enabled <> 0");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        return bigDataUtil.srQuery(sql).isEmpty() ? new HashMap<>() : bigDataUtil.srQuery(sql).get(0);
    }

    private Map<String, Object> getModulesInfo(List<Map<String, Object>> data) {
        Map<String, Object> result = new HashMap<>();

        result.put("purchased", data);
        // 判定是否有 EIN (電子發票系統) 模組
        result.put("eInvoice", getModuleInfo(data, "EIN"));
        result.put("eInvoiceUsed", isModuleUsed(data, "EIN"));
        // 判定是否有 FTS (傳輸系統) 模組
        result.put("transmitSystem", getModuleInfo(data, "FTS"));
        result.put("transmitSystemUsed", isModuleUsed(data, "FTS"));
        // 判定是否有 MTP (多角貿易系統) 模組
        result.put("triangleTrade", getModuleInfo(data, "MTP"));
        result.put("triangleTradeUsed", isModuleUsed(data, "MTP"));

        return result;
    }

    private SqlServerErpReport.Info getModuleInfo(List<Map<String, Object>> data, String moduleCode) {
        SqlServerErpReport.Info info = createInfo(SqlServerErpReport.Info::new);
        info.setIsWith(data.stream().anyMatch(item -> moduleCode.equals(item.get("code"))) ? HAS : NO_HAS);
        return info;
    }

    private SqlServerErpReport.Info isModuleUsed(List<Map<String, Object>> data, String moduleCode) {
        SqlServerErpReport.Info info = createInfo(SqlServerErpReport.Info::new);
        info.setIsWith(data.stream().noneMatch(item -> moduleCode.equals(item.get("code"))) ? NO_HAS : info.getIsWith());
        return info;
    }

    private SqlServerErpReport.CheckItems.HasDecimal hasDecimal(List<String> eid) {
        // 以潛客代號維度查詢 (潛客底下的 eid 作為條件)
        StringBuilder sb = new StringBuilder();
        sb.append("select count(*) as count ");
        sb.append("from servicecloud.ERP_DatabaseDecimalCheck_sr_primary ");
        sb.append("where ");
        sb.append("eid in ('").append(String.join("', '", eid)).append("') and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_DatabaseDecimalCheck_sr_primary ");
        sb.append("where ");
        sb.append("eid in ('").append(String.join("', '", eid)).append("') and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        SqlServerErpReport.CheckItems.HasDecimal hasDecimal = createInfo(SqlServerErpReport.CheckItems.HasDecimal::new);
        Integer count = IntegerUtil.objectToInteger(bigDataUtil.srQuery(sql).get(0).get("count"));
        hasDecimal.setIsWith(count > 0 ? YES : NO);

        return hasDecimal;
    }

    private <U extends SqlServerErpReport.Info> U createInfo(Supplier<U> constructor) {
        U info = constructor.get();
        info.setIsWith("");
        info.setComment("");
        return info;
    }

    @Override
    protected Map<String, Object> getOtherDeviceInfo(String appCode, long eid, String deviceId, String dbId) {
        return Collections.emptyMap();
    }
}
