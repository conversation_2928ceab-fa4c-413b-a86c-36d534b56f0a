package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 资产变更报告记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@ApiModel(value = "AssetChangeApplicationReportRecord对象", description = "资产变更报告记录")
public class AssetChangeApplicationReportRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("申请单ID")
    private Long applicationId;

    @ApiModelProperty("报告状态")
    private String reportStatus;

    @ApiModelProperty("报告生成时间")
    private LocalDateTime reportGenerationTime;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public String getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(String reportStatus) {
        this.reportStatus = reportStatus;
    }

    public LocalDateTime getReportGenerationTime() {
        return reportGenerationTime;
    }

    public void setReportGenerationTime(LocalDateTime reportGenerationTime) {
        this.reportGenerationTime = reportGenerationTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AssetChangeApplicationReportRecord{" +
            "id = " + id +
            ", applicationId = " + applicationId +
            ", reportStatus = " + reportStatus +
            ", reportGenerationTime = " + reportGenerationTime +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
        "}";
    }
}
