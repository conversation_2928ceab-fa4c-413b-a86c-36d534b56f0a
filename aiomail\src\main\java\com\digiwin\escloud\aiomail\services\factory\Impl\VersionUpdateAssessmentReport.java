package com.digiwin.escloud.aiomail.services.factory.Impl;

import com.digiwin.escloud.aiomail.Mail;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.aiomail.services.factory.annotation.BaseMailContent;
import org.springframework.stereotype.Service;

import static com.digiwin.escloud.aiomail.util.MailUtils.ReadMailContent;

@BaseMailContent(MailSourceType.VersionUpdateAssessmentReport)
@Service
public class VersionUpdateAssessmentReport extends ContentProduceBase {
    @Override
    protected String getFileName() {
        return "VersionUpdateAssessmentReport.html";
    }

    @Override
    public String getContent(Mail mail) {
        return String.format(ReadMailContent(getFileName(), mail.getLanguage()), mail.getMessage());
    }
}
