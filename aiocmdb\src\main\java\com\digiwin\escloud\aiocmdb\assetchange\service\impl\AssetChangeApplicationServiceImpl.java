package com.digiwin.escloud.aiocmdb.assetchange.service.impl;

import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationAssetListMapper;
import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationExecutionPlanMapper;
import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationMapper;
import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeExecutionPlanShutdownStatusMapper;
import com.digiwin.escloud.aiocmdb.assetchange.model.*;
import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationSaveRequest;
import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationSaveResponse;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.ApplicationStatus;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.PlanStatus;
import com.digiwin.escloud.aiocmdb.assetchange.service.IAssetChangeApplicationService;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 资产变更申请单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Slf4j
@Service
public class AssetChangeApplicationServiceImpl implements IAssetChangeApplicationService {

    @Autowired
    private AssetChangeApplicationMapper assetChangeApplicationMapper;

    @Autowired
    private AssetChangeApplicationAssetListMapper assetChangeApplicationAssetListMapper;

    @Autowired
    private AssetChangeApplicationExecutionPlanMapper assetChangeApplicationExecutionPlanMapper;

    @Autowired
    private AssetChangeExecutionPlanShutdownStatusMapper assetChangeExecutionPlanShutdownStatusMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase saveAssetChangeApplication(AssetChangeApplicationSaveRequest request) {
        try {
            // 参数校验
            ResponseBase validationResult = validateRequest(request);
            if (!validationResult.checkIsSuccess()) {
                return validationResult;
            }

            AssetChangeApplication application = request.getApplication();
            boolean isUpdate = LongUtil.isNotEmpty(application.getId());

            // 处理申请单编号
            if (!isUpdate && StringUtils.isEmpty(application.getApplicationNumber())) {
                application.setApplicationNumber(generateApplicationNumber());
            }

            // 检查申请单编号唯一性
            if (checkApplicationNumberExists(application.getApplicationNumber(), application.getId())) {
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "申请单编号已存在");
            }

            // 保存主表
            saveMainApplication(application, isUpdate);

            // 保存子表数据
            saveSubTables(request, application.getId());

            log.info("资产变更申请单保存成功，申请单ID: {}, 申请单编号: {}",
                    application.getId(), application.getApplicationNumber());

            AssetChangeApplicationSaveResponse response = new AssetChangeApplicationSaveResponse(
                    application.getId(),
                    application.getApplicationNumber(),
                    isUpdate ? "更新成功" : "新增成功"
            );
            return ResponseBase.ok(response);

        } catch (Exception e) {
            log.error("保存资产变更申请单失败", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "保存失败: " + e.getMessage());
        }
    }

    /**
     * 参数校验
     */
    private ResponseBase validateRequest(AssetChangeApplicationSaveRequest request) {
        if (request == null) {
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "请求参数不能为空");
        }

        AssetChangeApplication application = request.getApplication();
        if (application == null) {
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "申请单信息不能为空");
        }

        // 校验必填字段
        if (application.getEid() == null) {
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "eid不能为空");
        }

        if (StringUtils.isEmpty(application.getApplicantUserId())) {
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "申请人用户ID不能为空");
        }

        if (StringUtils.isEmpty(application.getApplicantName())) {
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "申请人名称不能为空");
        }

        if (StringUtils.isEmpty(application.getApplicationCategory())) {
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "申请类别不能为空");
        }

        if (StringUtils.isEmpty(application.getChangePriority())) {
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "变更优先级不能为空");
        }

        return ResponseBase.ok();
    }

    /**
     * TODO 生成申请单编号
     */
    private String generateApplicationNumber() {
        // 生成格式：AC + yyyyMMdd + 6位序号（基于雪花算法ID的后6位）
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        long snowflakeId = SnowFlake.getInstance().newId();
        String sequence = String.format("%06d", snowflakeId % 1000000);
        return "AC" + dateStr + sequence;
    }

    /**
     * 检查申请单编号是否存在
     */
    private boolean checkApplicationNumberExists(String applicationNumber, Long excludeId) {
        int count = assetChangeApplicationMapper.checkApplicationNumberExists(applicationNumber, excludeId);
        return count > 0;
    }

    /**
     * 保存主表信息
     */
    private void saveMainApplication(AssetChangeApplication application, boolean isUpdate) {
        LocalDateTime now = LocalDateTime.now();

        if (isUpdate) {
            // 更新操作
            application.setUpdateTime(now);
            AssetChangeApplication assetChangeApplication = assetChangeApplicationMapper.selectById(application.getId());
            if (!ApplicationStatus.isEqual(assetChangeApplication.getApplicationStatus(), ApplicationStatus.NOT_SUBMITTED)
                    && !ApplicationStatus.isEqual(assetChangeApplication.getApplicationStatus(), ApplicationStatus.NOT_APPROVED)) {
                throw new RuntimeException("当前状态不允许更新");
            }
            assetChangeApplicationMapper.updateById(application);
        } else {
            // 新增操作
            application.setId(SnowFlake.getInstance().newId());
            application.setCreateTime(now);
            application.setApplicationDate(now);
            application.setApplicationStatus(ApplicationStatus.NOT_SUBMITTED.name());
            application.setUpdateTime(now);
            assetChangeApplicationMapper.insert(application);
        }
    }

    /**
     * 保存子表数据
     */
    private void saveSubTables(AssetChangeApplicationSaveRequest request, Long applicationId) {
        // 如果是更新操作，先删除原有的子表数据
        if (request.getApplication().getId() != null && request.getApplication().getId() > 0) {
            deleteExistingSubTableData(applicationId);
        }

        // 保存变更资产清单
        saveAssetList(request.getAssetList(), applicationId);

        // 保存执行计划及停机状况
        saveExecutionPlansWithShutdownStatus(request.getExecutionPlanList(), applicationId);
    }

    /**
     * 删除已存在的子表数据
     */
    private void deleteExistingSubTableData(Long applicationId) {
        // 删除变更资产清单
        assetChangeApplicationAssetListMapper.deleteByApplicationId(applicationId);

        // 查询执行计划ID列表，用于删除停机状况
        List<AssetChangeApplicationExecutionPlan> existingPlans =
                assetChangeApplicationExecutionPlanMapper.selectByApplicationId(applicationId);

        if (!CollectionUtils.isEmpty(existingPlans)) {
            List<Long> planIdList = existingPlans.stream()
                    .map(AssetChangeApplicationExecutionPlan::getId)
                    .collect(Collectors.toList());

            // 删除停机状况
            assetChangeExecutionPlanShutdownStatusMapper.deleteByPlanIdList(planIdList);
        }

        // 删除执行计划
        assetChangeApplicationExecutionPlanMapper.deleteByApplicationId(applicationId);
    }

    /**
     * 保存变更资产清单
     */
    private void saveAssetList(List<AssetChangeApplicationAssetList> assetList, Long applicationId) {
        if (CollectionUtils.isEmpty(assetList)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        for (AssetChangeApplicationAssetList asset : assetList) {
            asset.setId(SnowFlake.getInstance().newId());
            asset.setApplicationId(applicationId);
            asset.setCreateTime(now);
            asset.setUpdateTime(now);
        }

        assetChangeApplicationAssetListMapper.batchInsert(assetList);
    }

    /**
     * 保存执行计划及停机状况
     */
    private void saveExecutionPlansWithShutdownStatus(
            List<AssetChangeApplicationSaveRequest.ExecutionPlanWithShutdownStatus> executionPlanList,
            Long applicationId) {

        if (CollectionUtils.isEmpty(executionPlanList)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        List<AssetChangeApplicationExecutionPlan> planList = new ArrayList<>();
        List<AssetChangeExecutionPlanShutdownStatus> shutdownStatusList = new ArrayList<>();

        for (AssetChangeApplicationSaveRequest.ExecutionPlanWithShutdownStatus planWithStatus : executionPlanList) {
            AssetChangeApplicationExecutionPlan plan = planWithStatus.getExecutionPlan();
            AssetChangeExecutionPlanShutdownStatus shutdownStatus = planWithStatus.getShutdownStatus();

            if (plan != null) {
                // 设置执行计划信息
                Long planId = SnowFlake.getInstance().newId();
                plan.setId(planId);
                plan.setApplicationId(applicationId);
                plan.setPlanStatus(PlanStatus.NOT_EXECUTED.name());
                plan.setCreateTime(now);
                plan.setUpdateTime(now);
                planList.add(plan);

                // 设置停机状况信息
                if (shutdownStatus != null) {
                    shutdownStatus.setId(SnowFlake.getInstance().newId());
                    shutdownStatus.setPlanId(planId);
                    shutdownStatus.setCreateTime(now);
                    shutdownStatus.setUpdateTime(now);
                    shutdownStatusList.add(shutdownStatus);
                }
            }
        }

        // 批量保存执行计划
        if (!CollectionUtils.isEmpty(planList)) {
            assetChangeApplicationExecutionPlanMapper.batchInsert(planList);
        }

        // 批量保存停机状况
        if (!CollectionUtils.isEmpty(shutdownStatusList)) {
            assetChangeExecutionPlanShutdownStatusMapper.batchInsert(shutdownStatusList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase deleteAssetChangeApplication(String id) {
        try {
            if (StringUtils.isEmpty(id)) {
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "申请单ID不能为空");
            }

            long applicationId = Long.parseLong(id);
            AssetChangeApplication application = assetChangeApplicationMapper.selectById(applicationId);

            if (application == null) {
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "未找到指定的申请单");
            }

            // 检查申请单状态
            if (!ApplicationStatus.isEqual(application.getApplicationStatus(), ApplicationStatus.NOT_SUBMITTED)) {
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "只有“未提交”状态的申请单才能被删除");
            }

            // 删除所有相关的子表数据
            deleteExistingSubTableData(applicationId);

            // 删除主表数据
            assetChangeApplicationMapper.deleteById(applicationId);

            log.info("资产变更申请单删除成功，申请单ID: {}", applicationId);

            return ResponseBase.ok("删除成功");

        } catch (NumberFormatException e) {
            log.error("无效的申请单ID格式", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "无效的ID格式");
        } catch (Exception e) {
            log.error("删除资产变更申请单失败", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "删除失败: " + e.getMessage());
        }
    }
}



