<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.userv2.chatheader.dao.ISettingDao">

    <select id="getChatHeaderSettings" resultType="com.digiwin.escloud.userv2.chatheader.model.ChatHeaderSetting">
        select a.id,a.sid,a.serviceRegion, c.RegionDisplayName serviceRegionName, a.productCode, b.ProductCategory productCategory, a.headerContentCN, a.headerContentTW, a.headerContentUS, a.headerContentVN, a.headerContentTH,
        case when #{lang}='zh-CN' then a.headerContentCN
        when #{lang}='zh-TW' then a.headerContentTW
        when #{lang}='en-US' then a.headerContentUS
        when #{lang}='vi-VN' then a.headerContentVN
        when #{lang}='th-TH' then a.headerContentTH ELSE '' END headerContent,
        GROUP_CONCAT(if(a.headerContentCN IS NOT NULL AND a.headerContentCN !='' ,'简体中文、',''),
        if(a.headerContentTW IS NOT NULL AND a.headerContentTW !='' ,'繁體中文、',''),
        if(a.headerContentUS IS NOT NULL AND a.headerContentUS !='' ,'English、',''),
        if(a.headerContentVN IS NOT NULL AND a.headerContentVN !='' ,'Tiếng việt nam、',''),
        if(a.headerContentTH IS NOT NULL AND a.headerContentTH !='' ,'ไทย、','')
        ) langStr,#{lang} lang

        from chat_header a
        LEFT JOIN mars_product b ON a.productCode = b.ProductCode
        LEFT JOIN serviceregions c ON c.Region = a.serviceRegion
        where 1=1
        <if test="sid>0">
            and a.sid=#{sid}
        </if>
        <if test="serviceRegion != '' and serviceRegion != null">
            and a.serviceRegion=#{serviceRegion}
        </if>
        <if test="productCode != '' and productCode != null">
            and a.productCode=#{productCode}
        </if>
        GROUP BY a.id
    </select>

    <select id="getChatHeaderSetting" resultType="com.digiwin.escloud.userv2.chatheader.model.ChatHeaderSetting">
        select a.id,a.sid,a.serviceRegion, c.RegionDisplayName serviceRegionName, a.productCode, b.ProductCategory productCategory, a.headerContentCN, a.headerContentTW, a.headerContentUS, a.headerContentVN, a.headerContentTH,
        case when #{lang}='zh-CN' then a.headerContentCN
        when #{lang}='zh-TW' then a.headerContentTW
        when #{lang}='en-US' then a.headerContentUS
        when #{lang}='vi-VN' then a.headerContentVN
        when #{lang}='th-TH' then a.headerContentTH ELSE '' END headerContent,
        GROUP_CONCAT(if(a.headerContentCN IS NOT NULL AND a.headerContentCN !='' ,'简体中文、',''),
        if(a.headerContentTW IS NOT NULL AND a.headerContentTW !='' ,'繁體中文、',''),
        if(a.headerContentUS IS NOT NULL AND a.headerContentUS !='' ,'English、',''),
        if(a.headerContentVN IS NOT NULL AND a.headerContentVN !='' ,'Tiếng việt nam、',''),
        if(a.headerContentTH IS NOT NULL AND a.headerContentTH !='' ,'ไทย、','')
        ) langStr,#{lang} lang

        from chat_header a
        LEFT JOIN mars_product b ON a.productCode = b.ProductCode
        LEFT JOIN serviceregions c ON c.Region = a.serviceRegion
        where 1=1
        <if test="sid>0">
            and a.sid=#{sid}
        </if>
        <if test="serviceRegion != '' and serviceRegion != null">
            and a.serviceRegion=#{serviceRegion}
        </if>
        <if test="productCode != '' and productCode != null">
            and a.productCode=#{productCode}
        </if>
        GROUP BY a.id
    </select>

    <select id="getChatHeaderSettingDetail" resultType="com.digiwin.escloud.userv2.chatheader.model.ChatHeaderSetting">
        select a.id,a.sid,a.serviceRegion, c.RegionDisplayName serviceRegionName, a.productCode, b.ProductCategory productCategory, a.headerContentCN, a.headerContentTW, a.headerContentUS, a.headerContentVN, a.headerContentTH
        from chat_header a
        LEFT JOIN mars_product b ON a.productCode = b.ProductCode
        LEFT JOIN serviceregions c ON c.Region = a.serviceRegion
        where 1=1
        <if test="id != '' and id != null">
            and a.id=#{id}
        </if>

       limit 1
    </select>

    <insert id="saveChatHeaderSetting" parameterType="com.digiwin.escloud.userv2.chatheader.model.ChatHeaderSetting">
        insert into chat_header(sid, serviceRegion, productCode, headerContentCN, headerContentTW, headerContentUS, headerContentVN, headerContentTH)
        values(#{sid}, #{serviceRegion}, #{productCode}, #{headerContentCN}, #{headerContentTW}, #{headerContentUS},
             #{headerContentVN}, #{headerContentTH})
    </insert>

    <update id="updateChatHeaderSetting" parameterType="com.digiwin.escloud.userv2.chatheader.model.ChatHeaderSetting">
        update chat_header
        set sid = #{sid}, serviceRegion = #{serviceRegion}, productCode = #{productCode},
            headerContentCN = #{headerContentCN}, headerContentTW = #{headerContentTW}, headerContentUS = #{headerContentUS},
            headerContentVN = #{headerContentVN}, headerContentTH = #{headerContentTH}
        where id=#{id}
    </update>

    <delete id="deleteChatHeaderSetting" >
        delete from chat_header where id =#{id}
    </delete>

</mapper>