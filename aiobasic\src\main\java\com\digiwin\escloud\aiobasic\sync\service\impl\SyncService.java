package com.digiwin.escloud.aiobasic.sync.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiobasic.common.RedisNameHelp;
import com.digiwin.escloud.aiobasic.edr.util.CommonUtils;
import com.digiwin.escloud.aiobasic.sync.constant.SyncConst;
import com.digiwin.escloud.aiobasic.sync.dao.SyncBaseDataMapper;
import com.digiwin.escloud.aiobasic.sync.dao.SyncMapper;
import com.digiwin.escloud.aiobasic.sync.model.baseData.EsArea;
import com.digiwin.escloud.aiobasic.sync.model.baseData.EsIndustry;
import com.digiwin.escloud.aiobasic.sync.model.baseData.SupplierArea;
import com.digiwin.escloud.aiobasic.sync.model.baseData.SupplierIndustry;
import com.digiwin.escloud.aiobasic.sync.model.supplier.SupplierTenantMap;
import com.digiwin.escloud.aiobasic.sync.model.tenant.*;
import com.digiwin.escloud.aiobasic.sync.model.user.EsUser;
import com.digiwin.escloud.aiobasic.sync.model.user.UserPersonalInfo;
import com.digiwin.escloud.aiobasic.sync.model.user.UserTenantMap;
import com.digiwin.escloud.aiobasic.sync.model.user.UserThirdParty;
import com.digiwin.escloud.aiobasic.sync.service.ISyncService;
import com.digiwin.escloud.aiobasic.sync.util.IamUtils;
import com.digiwin.escloud.aioitms.model.instance.AiopsInstanceFixParam;
import com.digiwin.escloud.aiouser.model.tenant.ModuleContractStatus;
import com.digiwin.escloud.aiouser.model.tenant.TenantContract;
import com.digiwin.escloud.aiouser.model.tenant.TenantContractEx;
import com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractDetail;
import com.digiwin.escloud.aiouser.model.user.ProductLine147NoticeMailParams;
import com.digiwin.escloud.aiouser.model.user.User;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.model.tenant.ServiceStaffRequest;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.digiwin.escloud.integration.api.iam.req.tenant.TenantVO;
import com.digiwin.escloud.integration.api.iam.req.user.IamAuthoredUser;
import com.digiwin.escloud.integration.service.IamService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.digiwin.escloud.aiouser.model.tenant.ModuleContractStatus.SUBSCRIBED;
import static com.digiwin.escloud.aiouser.model.tenant.ModuleContractStatus.TRIALING;

@Service
@Slf4j
@RefreshScope
public class SyncService implements ISyncService, RedisNameHelp, AsyncProcessHelp {

    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    private long defaultSid;
    @Value("${digiwin.supplier.defaultstaffId:184293222941248}")
    private long defaultstaffId;
    @Value("${digiwin.supplier.defaultStartDate:1970-01-01}")
    private String defaultStartDate;
    @Value("${digiwin.token.user.verifyuserid}")
    private String verifyUserId;
    @Value("${digiwin.token.tenant.id}")
    private String tenantId;
    @Value("${digiwin.escloud.dbname:escloud-db}")
    private String escloudDBName;
    @Value("${sync.all.tenant.batch.size:10}")
    private Integer syncAllTenantBatchSize;
    @Value("${sync.all.tenant.status.check.limit:10}")
    private Integer syncAllTenantStatusCheckLimit;
    @Value("${sync.all.tenant.status.check.interval:2000}")
    private Long syncAllTenantStatusCheckInterval;
    public final static String SEND_MAIL_TENANT_SID_KEY = "send:mail:tenant:sid:";
    @Autowired
    private IamService iamService;
    @Autowired
    private SyncMapper syncMapper;
    @Autowired
    private SyncBaseDataMapper syncBaseDataMapper;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private IamUtils iamUtils;
    @Autowired
    private SyncTenantService syncTenantService;
    @Autowired
    private SyncTenantContractService syncTenantContractService;
    @Autowired
    private SyncUserService syncUserService;
    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private AioUserFeignClient aioUserFeignClient;

    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    private static final Long IT_CODE = 1L;
    private static final String PRODUCT_147 = "147";

    private boolean redisKeyIsExist(String key) {
        if (redisTemplate.hasKey(SyncConst.TENANT_ID) && redisTemplate.opsForValue().getOperations().getExpire(SyncConst.TENANT_ID) > 0)
            return true;
        else
            return false;
    }

    @Override
    public void syncAllData() {

    }

    private void handleBaseData() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("escloudDBName", escloudDBName);
        List<EsArea> esAreas = syncBaseDataMapper.getEsAreas(map);
        esAreas.stream().forEach(o -> {
            SupplierArea supplierArea = SupplierArea.builder()
                    .areaCode(o.getAreaCode())
                    .sid(defaultSid)
                    .name(o.getAreaName())
                    .build();
            syncBaseDataMapper.insertSupplierArea(supplierArea);
            String key = SyncConst.SUPPLIER_AREA_CODE + o.getAreaCode();
            redisTemplate.opsForValue().set(key, Integer.toString(supplierArea.getId()), 30, TimeUnit.DAYS);
        });

        List<EsIndustry> esIndustrys = syncBaseDataMapper.getEsIndustrys(map);
        esIndustrys.stream().forEach(o -> {
            SupplierIndustry supplierIndustry = SupplierIndustry.builder()
                    .industryCode(o.getIndustryCode())
                    .sid(defaultSid)
                    .name(o.getIndustryName())
                    .build();
            syncBaseDataMapper.insertSupplierIndustry(supplierIndustry);
            String key = SyncConst.SUPPLIER_INDUSTRY_CODE + o.getIndustryCode();
            redisTemplate.opsForValue().set(key, Integer.toString(supplierIndustry.getId()), 30, TimeUnit.DAYS);
        });
    }

    @Override
    public void syncBaseData() {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(() -> {
                handleBaseData();
            });
        } finally {
            executorService.shutdown();
        }
    }

    private User getUser(IamAuthoredUser iamUserInfo, EsUser esUser) {
        String email = iamUserInfo.getEmail();
        String tenantId = iamUserInfo.getTenantId();
        User user = new User();
        user.setSid(iamUserInfo.getSid());
        user.setId(iamUserInfo.getUserId());
        user.setName(iamUserInfo.getUserName());
        user.setEmail(email);
        user.setTelephone(iamUserInfo.getTelephone());
        user.setOpenId(esUser.getOpenId());
        final boolean[] isDigiwinUser = new boolean[1];
        Optional.ofNullable(email).ifPresent(x -> isDigiwinUser[0] = x.endsWith(SyncConst.MAIL_DIGIWIN) ? true : false);
        if (!isDigiwinUser[0])
            Optional.ofNullable(tenantId).ifPresent(x -> isDigiwinUser[0] = x.equals(SyncConst.TENANT_ID_DIGIWIN) ? true : false);
        if (isDigiwinUser[0]) {
            user.setDefaultSid(isDigiwinUser[0] ? defaultSid : null);
            user.setDefaultSidEid(SyncConst.TENANT_SID_DIGIWIN);
        } else {
            user.setDefaultEid(iamUserInfo.getTenantSid());
            user.setDefaultEidSid(defaultSid);
        }
        user.setStatus(1);
        return user;
    }

    private UserPersonalInfo getUserPersonInfo(IamAuthoredUser iamUserInfo, EsUser esUser) {
        UserPersonalInfo userPersonalInfo = new UserPersonalInfo();
        userPersonalInfo.setSid(iamUserInfo.getSid());
        Optional.ofNullable(esUser.getEsUserPersonalInfo()).ifPresent(o -> BeanUtils.copyProperties(o, userPersonalInfo));
        return userPersonalInfo;
    }

    private HashMap<String, Object> getUserTenantMap(IamAuthoredUser iamUserInfo, EsUser esUser) {
        HashMap<String, Object> resMap = new HashMap<>();
        //如果用户Id和租户id相同，说明这个用户是这个租户的创建者
        boolean isEnterprise = iamUserInfo.getUserId().equals(iamUserInfo.getTenantId());
        List<UserTenantMap> userTenantMaps = new ArrayList<>();
        List<UserThirdParty> userThirdParties = new ArrayList<>();
        HashMap<String, Object> map = new HashMap<>();
        map.put("type", "joined");
        List<TenantVO> tenantsByUser = iamService.getTenantsByUser(iamUserInfo.getToken(), map);
        tenantsByUser.stream().forEach(o -> {

            UserTenantMap userTenantMap = new UserTenantMap();
            userTenantMap.setId(SnowFlake.getInstance().newId());
            userTenantMap.setUserSid(iamUserInfo.getSid());
            userTenantMap.setEid(o.getSid());
            userTenantMap.setSid(defaultSid);
            String authorizedProductCodes = esUser.getEsCustomerServices().stream()
                    .filter(x -> x.getCustomerServiceCode().equals(o.getId()))
                    .map(x -> x.getProductCode())
                    .collect(Collectors.joining(","));
            List<String> authorizedProductCodeList = esUser.getEsCustomerServices().stream()
                    .filter(x -> x.getCustomerServiceCode().equals(o.getId()))
                    .map(x -> x.getProductCode())
                    .collect(Collectors.toList());
            String defaultProductCode = !CollectionUtils.isEmpty(authorizedProductCodeList) ? authorizedProductCodeList.get(0) : null;
            userTenantMap.setAuthorizedProductCodes(authorizedProductCodes);
            if (o.getId().equals(iamUserInfo.getTenantId())) {
                userTenantMap.setEnterprise(isEnterprise);
                userTenantMap.setDefaultProductCode(esUser.getDefaultProductCode());
            } else {
                //暂时都给false
                userTenantMap.setEnterprise(false);
                userTenantMap.setDefaultProductCode(defaultProductCode);
            }
            userTenantMaps.add(userTenantMap);

            UserThirdParty userThirdParty = new UserThirdParty();
            userThirdParty.setId(SnowFlake.getInstance().newId());
            userThirdParty.setTpEid(o.getId());
            userThirdParty.setTpProductCode(defaultProductCode);
            userThirdParty.setTpAccountSet("");
            userThirdParty.setTpUserId(esUser.getId());
            userThirdParty.setTpUserName(esUser.getUserName());
            userThirdParty.setTpUserEmail(esUser.getEmail());
            userThirdParty.setTpUserTelephone(esUser.getPhone());
            userThirdParty.setUserId(iamUserInfo.getUserId());
            userThirdParty.setEid(o.getSid());
            userThirdParty.setSid(defaultSid);
            userThirdParties.add(userThirdParty);
        });
        resMap.put("userTenantMaps", userTenantMaps);
        resMap.put("userThirdParties", userThirdParties);
        return resMap;
    }

    @Override
    public void syncUserData(String userId) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(() -> {
                HashMap<String, Object> map = new HashMap<>();
                map.put("userId", userId);
                map.put("escloudDBName", escloudDBName);
                List<EsUser> esUsers = syncMapper.getEsUsers(map);
                if (CollectionUtils.isEmpty(esUsers))
                    return;
                esUsers.stream().forEach(o -> {
                    IamAuthoredUser iamUserInfo = iamUtils.getIamUserInfo(o.getId(), o.getCustomerServiceCode());
                    if (ObjectUtils.isEmpty(iamUserInfo))
                        return;

                    try {
                        User user = getUser(iamUserInfo, o);
                        Integer userExist = syncMapper.getUserExist(user.getSid());
                        if (userExist == null)
                            syncMapper.insertUser(user);
                        else
                            syncMapper.updateUser(user);

                        UserPersonalInfo userPersonInfo = getUserPersonInfo(iamUserInfo, o);
                        syncMapper.insertUserPersonalInfo(userPersonInfo);

                        HashMap<String, Object> userTenantMap = getUserTenantMap(iamUserInfo, o);
                        List<UserTenantMap> userTenantMaps = (List) userTenantMap.get("userTenantMaps");
                        List<UserThirdParty> userThirdParties = (List) userTenantMap.get("userThirdParties");
                        userTenantMaps.stream().forEach(x -> syncMapper.insertUserTenantMap(x));
                        userThirdParties.stream().forEach(x -> syncMapper.insertTpUser(x));
                    } catch (Exception e) {
                        log.error("syncUserData error esUserId:" + o.getId() + " customerServiceCode:" + o.getCustomerServiceCode(), e);
                    }
                });
            });
        } finally {
            executorService.shutdown();
        }
    }

    @Override
    public void syncTenantData(String serviceCode) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(() -> {
                //全量更新
                HashMap<String, Object> map = new HashMap<>();
                map.put("serviceCode", serviceCode);
                map.put("escloudDBName", escloudDBName);
                List<EsCustomer> esCustomers = syncMapper.getEsCustomers(map);
                if (CollectionUtils.isEmpty(esCustomers))
                    return;
                String token = iamService.grantAccessToken(verifyUserId, tenantId);
                List<String> tenantIds = new ArrayList<>();
                esCustomers.stream().forEach(o -> {
                    try {
                        String key = SyncConst.TENANT_ID + o.getCustomerServiceCode();
                        String tenantSid = redisTemplate.opsForValue().get(key);
                        if (StringUtils.isEmpty(tenantSid)) {
                            tenantIds.clear();
                            tenantIds.add(o.getCustomerServiceCode());
                            List<TenantVO> tenantSimpleInfos = iamService.getTenantSimpleInfo(token, tenantIds);
                            Optional<TenantVO> tenantVO = tenantSimpleInfos.stream().findAny();
                            if (tenantVO.isPresent()) {
                                redisTemplate.opsForValue().set(key, String.valueOf(tenantVO.get().getSid()), 30, TimeUnit.DAYS);
                                tenantSid = String.valueOf(tenantVO.get().getSid());
                            } else
                                return;
                        }
                        Tenant tenant = Tenant.builder()
                                .sid(Long.valueOf(tenantSid))
                                .id(o.getCustomerServiceCode())
                                .name(o.getCustomerName())
                                .customer_id(o.getCustomerServiceCode())
                                .taxCode(o.getTaxNo())
                                .status(1)
                                .build();
                        syncMapper.insertTenant(tenant);
                        SupplierTenantMap supplierTenantMap = SupplierTenantMap.builder()
                                .id(SnowFlake.getInstance().newId())
                                .sid(defaultSid)
                                .eid(tenant.getSid())
                                .serviceCode(o.getCustomerServiceCode())
                                .build();
                        syncMapper.insertSupplierTenantMap(supplierTenantMap);
                    } catch (Exception ex) {
                        log.error("syncTenantData error tenantId:" + o.getCustomerServiceCode(), ex);
                    }
                });
            });
        } finally {
            executorService.shutdown();
        }
    }

    private TenantContract getTenantContract(long tenantSid, EsCustomerContract esCustomerContract) {
        TenantContract tenantContract = new TenantContract();
        tenantContract.setId(SnowFlake.getInstance().newId());
        tenantContract.setEid(tenantSid);
        tenantContract.setSid(defaultSid);
        tenantContract.setProductCode(esCustomerContract.getProductCode());
        tenantContract.setProductShortName(esCustomerContract.getProductCategory());
        tenantContract.setProductVersion(esCustomerContract.getProductVersion());
        tenantContract.setHasOwnerService(esCustomerContract.getHasOwnerService());
        tenantContract.setHasTextService(esCustomerContract.getHasTextService());
        tenantContract.setHasOwnerIssueService(esCustomerContract.getHasOwnerIssueService());

        String esUserId = esCustomerContract.getUserId();
        IamAuthoredUser iamAuthoredUser;
        if (!StringUtils.isEmpty(esUserId)) {
            String key = SyncConst.USER_ID + esUserId;
            String iamUser = redisTemplate.opsForValue().get(key);
            if (!StringUtils.isEmpty(iamUser)) {
                iamAuthoredUser = JSON.parseObject(iamUser, IamAuthoredUser.class);
            } else {
                iamAuthoredUser = iamUtils.getIamUserInfo(esUserId, SyncConst.TENANT_ID_DIGIWIN);
            }
            Optional.ofNullable(iamAuthoredUser).ifPresent(o -> {
                tenantContract.setServiceStaffId(o.getSid());
                tenantContract.setServiceStaffName(o.getUserName());
            });
        }
        tenantContract.setContractState(esCustomerContract.getContractState());
        if (!StringUtils.isEmpty(esCustomerContract.getContractStartDate())) {
            try {
                Date date = DateUtils.parseDate(esCustomerContract.getContractStartDate(), new String[]{"yyyyMMdd"});
                tenantContract.setContractStartDate(date);
            } catch (ParseException e) {
                log.error("parseContractStartDate productCode:" + esCustomerContract.getProductCode() + " customerServiceCode:" +
                        esCustomerContract.getCustomerServiceCode(), e);
                tenantContract.setContractStartDate(null);
            }
        }
        if (!StringUtils.isEmpty(esCustomerContract.getContractExprityDate())) {
            try {
                Date date = DateUtils.parseDate(esCustomerContract.getContractExprityDate(), new String[]{"yyyyMMdd"});
                tenantContract.setContractExpiryDate(date);
            } catch (ParseException e) {
                log.error("parseContractExprityDate productCode:" + esCustomerContract.getProductCode() + " customerServiceCode:" +
                        esCustomerContract.getCustomerServiceCode(), e);
                tenantContract.setContractExpiryDate(null);
            }
        }

        String areaCode = esCustomerContract.getAreaCode();
        if (!StringUtils.isEmpty(areaCode)) {
            Long aioAreaId;
            String areakey = SyncConst.SUPPLIER_AREA_CODE + areaCode;
            String areaId = redisTemplate.opsForValue().get(areakey);
            if (!StringUtils.isEmpty(areaId)) {
                aioAreaId = Long.valueOf(areaId);
            } else {
                aioAreaId = syncBaseDataMapper.getSupplierAreaId(esCustomerContract.getAreaCode());
                redisTemplate.opsForValue().set(areakey, Long.toString(aioAreaId), 30, TimeUnit.DAYS);
            }
            tenantContract.setAreaId(aioAreaId);
        }

        String industryCode = esCustomerContract.getIndustryCode();
        if (!StringUtils.isEmpty(industryCode)) {
            Long aioIndustryId;
            String industryKey = SyncConst.SUPPLIER_INDUSTRY_CODE + industryCode;
            String industryId = redisTemplate.opsForValue().get(industryKey);
            if (!StringUtils.isEmpty(industryId)) {
                aioIndustryId = Long.valueOf(industryId);
            } else {
                aioIndustryId = syncBaseDataMapper.getSupplierIndustryId(esCustomerContract.getIndustryCode());
                redisTemplate.opsForValue().set(industryKey, Long.toString(aioIndustryId), 30, TimeUnit.DAYS);
            }
            tenantContract.setIndustryId(aioIndustryId);
        }
        return tenantContract;
    }

    @Override
    public void syncTenantContractData(String serviceCode) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(() -> {
                //全量更新
                Map<String, Object> map = new HashMap<>();
                map.put("serviceCode", serviceCode);
                map.put("escloudDBName", escloudDBName);
                List<EsCustomerContract> esCustomerContracts = syncMapper.getEsCustomerContracts(map);
                if (CollectionUtils.isEmpty(esCustomerContracts))
                    return;
                String token = iamService.grantAccessToken(verifyUserId, tenantId);
                List<String> tenantIds = new ArrayList<>();
                esCustomerContracts.stream().forEach(o -> {
                    try {
                        String key = SyncConst.TENANT_ID + o.getCustomerServiceCode();
                        String tenantSid = redisTemplate.opsForValue().get(key);
                        if (StringUtils.isEmpty(tenantSid)) {
                            tenantIds.clear();
                            tenantIds.add(o.getCustomerServiceCode());
                            List<TenantVO> tenantSimpleInfos = iamService.getTenantSimpleInfo(token, tenantIds);
                            Optional<TenantVO> tenantVO = tenantSimpleInfos.stream().findAny();
                            if (tenantVO.isPresent()) {
                                redisTemplate.opsForValue().set(key, String.valueOf(tenantVO.get().getSid()), 30, TimeUnit.DAYS);
                                tenantSid = String.valueOf(tenantVO.get().getSid());
                            } else
                                return;
                        }
                        TenantContract tenantContract = getTenantContract(Long.parseLong(tenantSid), o);
                        syncMapper.insertTenantContract(tenantContract);
                    } catch (Exception ex) {
                        log.error("syncTenantData error tenantId:" + o.getCustomerServiceCode(), ex);
                    }
                });
            });
        } finally {
            executorService.shutdown();
        }
    }

    @Override
    public ResponseBase syncTenantAll(String serviceCode) {
        try {
            List<EsCustomer> esCustomers = syncTenantService.getEsCustomers(serviceCode);
            if (CollectionUtils.isEmpty(esCustomers)) {
                return ResponseBase.error(ResponseCode.TENANT_ES_NULL.toString(), ResponseCode.TENANT_ES_NULL.getMsg());
            }
            if (esCustomers.get(0).getEid() == null || esCustomers.get(0).getEid() == 0) {
                return ResponseBase.error(ResponseCode.TENANT_IAM_NULL.toString(), ResponseCode.TENANT_IAM_NULL.getMsg());
            }
            String token = iamService.grantAccessToken(verifyUserId, tenantId);
            String esCustomerSid = syncTenantService.getEsCustomerSid(esCustomers.get(0), token);
            if (StringUtils.isEmpty(esCustomerSid)) {
                return ResponseBase.error(ResponseCode.TENANT_IAM_NULL.toString(), ResponseCode.TENANT_IAM_NULL.getMsg());
            }
            syncTenantService.saveEsCustomer(esCustomers.get(0), token, esCustomerSid);
            syncTenantContractService.handleTenantContractData(serviceCode);
            return ResponseBase.ok(Long.parseLong(esCustomerSid));
        } catch (Exception e) {
            log.error("syncTenantAllData", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public ResponseBase syncUser(String userId) {
        try {
            List<EsUser> esUsers = syncUserService.getEsUsers(userId);
            if (CollectionUtils.isEmpty(esUsers)) {
                return ResponseBase.error(ResponseCode.USER_ES_NULL);
            }
            IamAuthoredUser iamUserInfo = iamUtils.getIamUserInfo(esUsers.get(0).getId(), esUsers.get(0).getCustomerServiceCode());
            if (ObjectUtils.isEmpty(iamUserInfo)) {
                return ResponseBase.error(ResponseCode.USER_IAM_NULL);
            }
            syncUserService.saveEsUser(esUsers.get(0), iamUserInfo);
            return ResponseBase.ok(iamUserInfo.getSid());
        } catch (Exception e) {
            log.error("syncUser", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public void syncBatchTenant(List<String> serviceCodes, boolean contractOnly) {
        //如果contractOnly=true,不能异步，如果异步，后面订阅模组的授权日期就不能同步更新
        if(CollectionUtil.isNotEmpty(serviceCodes)){
            if(contractOnly){
                serviceCodes.forEach(o -> {
                    syncTenantContractService.handleTenantContractData(o);
                });
            }else {
                Runnable runnable = () ->
                        serviceCodes.forEach(o -> {
                            syncTenantAll(o);
                            sleepMillisecond(200);
                        });

                commonUtils.asyncRun(runnable);
            }
        }

        /*Runnable runnable = () ->
                serviceCodes.forEach(o -> {
                    if (contractOnly) {
                        syncTenantContractService.handleTenantContractData(o);
                    } else {
                        syncTenantAll(o);
                    }
                    sleepMillisecond(200);
                });

        commonUtils.asyncRun(runnable);*/
    }

    private void syncTenant(String serviceCode, boolean contractOnly) {
        //如果contractOnly=true,不能异步，如果异步，后面订阅模组的授权日期就不能同步更新
        if (!StringUtils.isEmpty(serviceCode)) {
            if (contractOnly) {
                syncTenantContractService.handleTenantContractData(serviceCode);
            } else {
                //todo 新增租户 更新合约
                syncTenantAll(serviceCode);
            }
        }
    }

    @Async
    @Override
    public void syncAllTenantContract(List<String> serviceCodeList) {
        int checkLimit = syncAllTenantStatusCheckLimit;
        long checkInterval = LongUtil.isEmpty(syncAllTenantStatusCheckInterval) ? 2000L :
                syncAllTenantStatusCheckInterval;

        syncAllTenantContractCore(serviceCodeList, "syncAllTenantContract", (stc) -> {
            List<String> currentList = stc.getCurrentList();
            BaseResponse response = aioUserFeignClient.transferEsclientContractSetting(
                    false, true, 1, currentList);
            if (!response.checkIsSuccess()) {
                StringBuilder sbLog = stc.getSbLog();
                sbLog.append(stc.getSupplierSbContent().get());
                sbLog.append(" transferEsclientContractSetting code:");
                sbLog.append(response.getCode());
                sbLog.append(" not zero");
                sbLog.append(" errMsg:");
                sbLog.append(response.getErrMsg());
                return;
            }
            int times = 0;
            do {
                sleepMillisecond(checkInterval);
                BaseResponse<String> watchRes =
                        aioUserFeignClient.watchTransferEsclientContractSettingTask("0", currentList);
                if (!response.checkIsSuccess()) {
                    StringBuilder sbLog = stc.getSbLog();
                    sbLog.append(stc.getSupplierSbContent().get());
                    sbLog.append(" watchTransferEsclientContractSettingTask code:");
                    sbLog.append(response.getCode());
                    sbLog.append(" not zero");
                    sbLog.append(" errMsg:");
                    sbLog.append(response.getErrMsg());
                    return;
                }
                String data = watchRes.getData();
                if (StringUtils.isEmpty(data) || data.startsWith(statusEnd())) {
                    //如果状态取到空，也认为成功了
                    return;
                }
                String errorKey = statusError();
                int errorIndex = data.indexOf(errorKey);
                if (errorIndex > -1) {
                    //表示失败了，再取一次实际的状态(先固定取1的错误内容就好，因为分批做异常理应不会太多)
                    watchRes = aioUserFeignClient.watchTransferEsclientContractSettingTask("1", currentList);
                    if (!response.checkIsSuccess()) {
                        StringBuilder sbLog = stc.getSbLog();
                        sbLog.append(stc.getSupplierSbContent().get());
                        sbLog.append(" watchTransferEsclientContractSettingTask get statusIndex:1 code:");
                        sbLog.append(response.getCode());
                        sbLog.append(" not zero");
                        sbLog.append(" errMsg:");
                        sbLog.append(response.getErrMsg());
                        return;
                    }
                    String watchResData = watchRes.getData();
                    int startIndex = errorIndex + errorKey.length();
                    StringBuilder sbLog = stc.getSbLog();
                    if (StringUtils.isEmpty(watchResData) || watchResData.length() < startIndex) {
                        sbLog.append(data.substring(startIndex));
                    } else {
                        sbLog.append(watchResData.substring(startIndex));
                    }
                    return;
                }
                if (checkLimit > 0 && times > checkLimit) {
                    //表示超过检测上限，失败了
                    StringBuilder sbLog = stc.getSbLog();
                    sbLog.append(stc.getSupplierSbContent().get());
                    sbLog.append(" watchTransferEsclientContractSettingTask up to limit:");
                    sbLog.append(times);
                    return;
                }
                if (checkIsStop(stringRedisTemplate, stc.getKey())) {
                    log.debug(stc.getSupplierSbContent().get().append(" stop").toString());
                    return;
                }
                times++;
            } while (true);
        });
    }

    private void syncAllTenantContractCore(List<String> serviceCodeList, String sourceMethod,
                                           Consumer<SyncTenantContext> otherStep) {
        String key = getSyncAllTenantKey(serviceCodeList);
        if (!CollectionUtils.isEmpty(serviceCodeList)) {
            //不是全部，要检查是否存在全部的在执行中
            String allKey = getSyncAllTenantKey(null);
            String allKeyStatus = stringRedisTemplate.opsForValue().get(allKey);
            if (checkIsRunningCore(allKeyStatus)) {
                //如果存在全键(且在执行中)，那就离开
                log.warn(String.format("%s stop by status key: %s is exist, status:%s", sourceMethod,
                        allKey, allKeyStatus));
                return;
            }
        }
        String status = stringRedisTemplate.opsForValue().get(key);
        if (checkIsRunningCore(status)) {
            //如果键已经存在了(且在执行中)，就离开
            log.warn(String.format("%s stop by status key: %s is exist, status:%s", sourceMethod, key, status));
            return;
        }
        setStart(stringRedisTemplate, key);
        // 找出所有的租户
        List<Tenant> tenantList = syncTenantService.getAllTenant(serviceCodeList);

        // 每N笔做一次同步
        int batchSize = IntegerUtil.isEmpty(syncAllTenantBatchSize) ? 10 : syncAllTenantBatchSize;
        SyncTenantContext stc = new SyncTenantContext(key);
        try {
            //获取新客数据
            List<String> newCustomerList = Optional.ofNullable(queryServiceCodeByContractTime(serviceCodeList)).orElse(new ArrayList<>());

            Lists.partition(serviceCodeList.stream().collect(Collectors.toList()), batchSize).forEach(x->{
                stc.setCurrentList(x);
                try {
                    if (checkIsStop(stringRedisTemplate, key)) {
                        return;
                    }
                    setRunning(stringRedisTemplate, key, String.join(",", x));
                    //因为来源是tenant表已经存在的租户，因此只针对产品合约做修正，其他租户新增等行为都不做
                    //判断当前异动数据是否是否属于新客数据
                    List<String> customerIdList = Optional.of(tenantList.stream().map(Tenant::getId).collect(Collectors.toList())).orElse(new ArrayList<>());

                    x.forEach(serviceCode -> {
                        if (newCustomerList.contains(serviceCode)) {
                            this.syncTenant(serviceCode, customerIdList.contains(serviceCode));
                        } else {
                            this.syncTenant(serviceCode, true);
                        }
                    });

                    if (checkIsStop(stringRedisTemplate, key)) {
                        return;
                    }
                    //如果有其他步骤，在此进行
                    if (otherStep != null) {
                        otherStep.accept(stc);
                    }
                } catch (Exception ex) {
                    StringBuilder sbLog = stc.getSbLog();
                    sbLog.append(stc.getSupplierSbContent().get());
                    sbLog.append(" exception:");
                    sbLog.append(ex);
                    sbLog.append("\n");
                }

            });

            StringBuilder sbLog = stc.getSbLog();
            if (sbLog.length() > 0) {
                setError(stringRedisTemplate, key, sbLog.toString());
            } else {
                setEnd(stringRedisTemplate, key);
            }
        } catch (Exception ex) {
            StringBuilder sbError = new StringBuilder(sourceMethod);
            sbError.append(" ");
            sbError.append(stc.getSupplierSbContent().get());
            log.error(sbError.toString(), ex);
            sbError.append(" exception:");
            sbError.append(ex);
            setError(stringRedisTemplate, key, sbError.toString());
        }
    }

    private void sleepMillisecond(long millisecond) {
        try {
            Thread.sleep(millisecond);
        } catch (Exception e) { //修复漏洞/bug InterruptedException 改成 Exception
            e.printStackTrace();
        }
    }

    @Override
    public BaseResponse getSyncAllTenantContractKeyList() {
        return BaseResponse.ok(stringRedisTemplate.keys(getSyncAllTenantKeyPattern()));
    }

    @Override
    public BaseResponse getSyncAllTenantContractStatus(List<String> serviceCodeList) {
        return getSyncAllTenantContractStatusByKey(getSyncAllTenantKey(serviceCodeList));
    }

    @Override
    public BaseResponse getSyncAllTenantContractStatusByKey(String key) {
        return BaseResponse.ok(stringRedisTemplate.opsForValue().get(key));
    }

    @Override
    public BaseResponse stopSyncAllTenantContract(List<String> serviceCodeList) {
        return stopSyncAllTenantContractByKey(getSyncAllTenantKey(serviceCodeList));
    }

    @Override
    public BaseResponse stopSyncAllTenantContractByKey(String key) {
        if (checkIsRunning(stringRedisTemplate, key)) {
            return BaseResponse.ok(setStop(stringRedisTemplate, key));
        }
        return BaseResponse.ok(false);
    }

    @Override
    public void syncBatchUser(List<String> userIds) {
        Runnable runnable = () -> {
            userIds.stream().forEach(o -> {
                syncUser(o);
            });
        };
        commonUtils.asyncRun(runnable);
    }

    @Override
    public BaseResponse getSyncServcieCodeByRecentTime(String endTime, long nearlyHours) {
        try {
            List<String> serviceCodeList = new ArrayList<>();
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date now = new Date();
            String date = dateFormat.format(now);
            HashMap<String, Object> map = new HashMap<>();
            map.put("endTime", StringUtils.isEmpty(endTime) ? date : endTime);
            map.put("nearlyHours", nearlyHours);
            map.put("escloudDBName", escloudDBName);
            serviceCodeList = syncMapper.getSyncServcieCodeByRecentTime(map);
            return BaseResponse.ok(serviceCodeList);
        } catch (Exception e) {
            log.error("getSyncServcieCodeByRecentTime", e);
            return BaseResponse.error(e);
        }
    }

    /**
     * 获取所有新客数据
     *
     * @return
     */
    private List<String> queryServiceCodeByContractTime(List<String> serviceCodeList) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("serviceCodeList", serviceCodeList);
        param.put("escloudDBName", escloudDBName);
        return syncMapper.queryServiceCodeByContractTime(param);
    }

    @Async
    @Override
    public void syncAllTenantContract_CN(List<String> serviceCodeList, Map<String, TenantContract> tenantContractMap) {
        List<TenantModeContract> inserttenantModuleContracts = new ArrayList<>();
        List<TenantModeContract> tenantModuleContractdetails = new ArrayList<>();
        List<TenantModeContract> tenantModuleContracts = new ArrayList<>();
        List<TenantModeContract> newInsertTenantModuleContracts = new ArrayList<>();
        // 查询当前的服务模块对象数组
        syncAllTenantContractCore(serviceCodeList, "syncAllTenantContract_CN", (stc) -> {
            List<String> currentList = stc.getCurrentList();
            List<TenantModeContract> allTenantList = syncTenantService.getAllTenantContract(currentList);
            //新增逻辑处理实例授权状态 新增 要去除147产品线  147 产品线的合约单独处理
            List<TenantModeContract> tenantList = holdTenantList(allTenantList);

            List<TenantModeContract> tenant147Contracts = get147ProductTenantList(allTenantList);
            if (CollectionUtils.isEmpty(tenantList) && CollectionUtils.isEmpty(tenant147Contracts)) {
                log.warn("{} stop by not found any tenantcontract, serviceCodeList:{}", "syncAllTenantContract_CN",
                        StringUtil.getStringListMergeString(serviceCodeList));
                return;
            }

            Map<String, Long> employeeSidMap = getEmployeeSid(currentList);
            Set<String> set = new HashSet<>();
            Set<Long> set2 = new HashSet<>();
            //获取tmcid存在的对象并且状态不包含1,3
            List<TenantModeContract> tmcIdnotNullList = tenantList.stream()
                    .filter(o -> o.getTmcId() != null
                            && set2.add(o.getTmcId()))
                    .map(o -> {
                        TenantModeContract notnullTenant = new TenantModeContract();
                        notnullTenant.setTmcId(o.getTmcId());
                        notnullTenant.setEid(o.getEid());
                        notnullTenant.setSid(o.getSid());
                        notnullTenant.setModuleId(o.getModuleId());
                        notnullTenant.setStatus(o.getStatus());
                        notnullTenant.setContractStartDate(o.getContractStartDate());
                        notnullTenant.setContractExpiryDate(o.getContractExpiryDate());
                        notnullTenant.setModuleCode(o.getModuleCode());
                        notnullTenant.setServiceCode(o.getServiceCode());
                        return notnullTenant;
                    })
                    .collect(Collectors.toList());

            //获取tmcid不存在的对象并且状态不包含1,3，并且根据eid和moduleid去重
            List<TenantModeContract> tmcIdnullList = tenantList.stream()
                    .filter(o -> o.getTmcId() == null &&
                            set.add(o.getEid() + "|" + o.getModuleId()))
                    .map(o -> {
                        TenantModeContract nullTenant = new TenantModeContract();
                        nullTenant.setEid(o.getEid());
                        nullTenant.setModuleId(o.getModuleId());
                        nullTenant.setSid(o.getSid());
                        nullTenant.setContractStartDate(o.getContractStartDate());
                        nullTenant.setContractExpiryDate(o.getContractExpiryDate());
                        nullTenant.setModuleCode(o.getModuleCode());
                        nullTenant.setServiceCode(o.getServiceCode());
                        return nullTenant;
                    })
                    .collect(Collectors.toList());

            tmcIdnullList.forEach(o -> {
                TenantModeContract instenantModuleContract = new TenantModeContract();
                TenantModeContract tenantModuleContractdetail = new TenantModeContract();
                Date date = null;
                try {
                    date = DateUtils.parseDate(defaultStartDate, new String[]{"yyyy-MM-dd"});
                } catch (ParseException e) {
                    date = null;
                }

                instenantModuleContract.setStartDate(Optional.ofNullable(o.getContractStartDate()).orElse(date));
                instenantModuleContract.setEndDate(o.getContractExpiryDate());
                instenantModuleContract.setModuleId(o.getModuleId());
                instenantModuleContract.setSid(o.getSid());
                instenantModuleContract.setEid(o.getEid());
                instenantModuleContract.setServiceCode(o.getServiceCode());
                    instenantModuleContract.setStatus(ModuleContractStatus.ANNUAL_MAINTENANCE_SERVICES.getIndex());
                    instenantModuleContract.setStaffId(RequestUtil.getHeaderSidOrDefault(defaultstaffId));
                long tmcId = SnowFlake.getInstance().newId();
                instenantModuleContract.setId(tmcId);
                newInsertTenantModuleContracts.add(instenantModuleContract);
                //添加到租户模组合约明细集合
                String classCode = "";
                //判断是否是主机
                if (o.getModuleId() == 1) {
                    classCode = "HOST";
                }
                Long samcid = syncTenantService.getSupplierAiopsmModuleClassId(o.getModuleId(), classCode);
                //根据运维商+租户+模组Id唯一索引查询tenant_module_contract表有没有存在的数据，
                // 存在就不新增tenant_module_contract_detail表
                //只更新tenant_module_contract表
                // int count = syncTenantService.getTenantModulecontractnum(o.getEid(), o.getModuleId());
                // if (count == 0) {
                //添加数据到tenantModuleContractdetails
                tenantModuleContractdetail.setTmcId(tmcId);
                tenantModuleContractdetail.setId(SnowFlake.getInstance().newId());
                tenantModuleContractdetail.setSamcId(samcid);
                tenantModuleContractdetail.setUsedCount(0);
                //todo 需要新增初始模组主机数量
                Integer availableCount = getAvailableCount(o.getModuleCode());
                tenantModuleContractdetail.setAvailableCount(availableCount);
                tenantModuleContractdetails.add(tenantModuleContractdetail);
                //}
                //添加到inserttenantModuleContracts集合中
                inserttenantModuleContracts.add(instenantModuleContract);
            });
            tmcIdnotNullList.forEach(o -> {
                TenantModeContract tenantModuleContract = new TenantModeContract();
                Date date = null;
                try {
                    date = DateUtils.parseDate(defaultStartDate, new String[]{"yyyy-MM-dd"});
                } catch (ParseException e) {
                    date = null;
                }
                tenantModuleContract.setTmcId(o.getTmcId());
                tenantModuleContract.setStatus(ModuleContractStatus.ANNUAL_MAINTENANCE_SERVICES.getIndex());
                tenantModuleContract.setStartDate(Optional.ofNullable(o.getContractStartDate()).orElse(date));
                tenantModuleContract.setEndDate(o.getContractExpiryDate());
                tenantModuleContract.setServiceCode(o.getServiceCode());
                tenantModuleContract.setSid(o.getSid());
                tenantModuleContract.setEid(o.getEid());
                tenantModuleContract.setModuleId(o.getModuleId());

                tenantModuleContracts.add(tenantModuleContract);
            });

            for (TenantModeContract tmc147 : tenant147Contracts) {
                String redisKey = SEND_MAIL_TENANT_SID_KEY + tmc147.getServiceCode();
                Object redisValue = redisTemplate.opsForValue().get(redisKey);
                if (redisValue == null) {
                    //如果key不存在,添加key,并设置过期时间到第二天凌晨
                    LocalDateTime midnight = LocalDateTime.now().toLocalDate().atTime(LocalTime.MIDNIGHT).plusDays(1);
                    long seconds = Duration.between(LocalDateTime.now(), midnight).getSeconds();
                    redisTemplate.opsForValue().set(redisKey, "1", seconds, TimeUnit.SECONDS);
                }
                if (LongUtil.isEmpty(tmc147.getTmcId())) {
                    // 处理新增的合约
                    processNewContract(tmc147, tenantModuleContractdetails, inserttenantModuleContracts, 
                            employeeSidMap, defaultstaffId, defaultStartDate);
                } else {
                    // 处理更新的合约
                    processExistingContract(tmc147, tenantContractMap, tenantModuleContracts,
                            employeeSidMap, defaultStartDate);
                }
            }

            Set<Long> tenantSids = new HashSet<>();
            if (CollectionUtil.isNotEmpty(tenantModuleContracts)) {
                //批量更新tenant_module_contract表
                syncMapper.updateTenantModuleContractsBySync(tenantModuleContracts);
                tenantSids.addAll(tenantModuleContracts.stream()
                        .map(TenantModeContract::getEid)
                        .collect(Collectors.toSet()));
            }
            if (CollectionUtil.isNotEmpty(inserttenantModuleContracts)) {
                //批量插入tenant_module_contract表
                syncMapper.insertTenantModuleContractsBySync(inserttenantModuleContracts);
                tenantSids.addAll(inserttenantModuleContracts.stream()
                        .map(TenantModeContract::getEid)
                        .collect(Collectors.toSet()));
            }
            if (CollectionUtil.isNotEmpty(tenantModuleContractdetails)) {
                //批量插入tenant_module_contract_detail表
                syncMapper.insertTenantModuleContractsDetailBySync(tenantModuleContractdetails);
                tenantSids.addAll(tenantModuleContractdetails.stream()
                        .map(TenantModeContract::getEid)
                        .collect(Collectors.toSet()));
            }
            // 更新tenant表的installed的狀態
            if (!tenantSids.isEmpty()) {
                syncMapper.updateTenantInstalled(new ArrayList<>(tenantSids));
            }

            //147 产品线通知服务人员邮件的合约模组
            List<TenantModeContract> updateTmcList = tenantModuleContracts
                    .stream()
                    .filter(tmc -> PRODUCT_147.equals(tmc.getProductCode()))
                    .collect(Collectors.toList());
            List<TenantModeContract> mailNoticeMailList = inserttenantModuleContracts
                    .stream()
                    .filter(tmc -> PRODUCT_147.equals(tmc.getProductCode()))
                    .collect(Collectors.toList());

            mailNoticeMailList.addAll(updateTmcList);
            processOther(mailNoticeMailList);
            recoverAuth(currentList);
        });
    }

    private void processExistingContract(TenantModeContract tmc147, Map<String, TenantContract> tenantContractMap,
                                         List<TenantModeContract> tenantModuleContracts, Map<String, Long> employeeSidMap,
                                         String defaultStartDate) {
        // 只处理IT模组且存在合约的情况
        if (!IT_CODE.equals(tmc147.getModuleId()) || !tenantContractMap.containsKey(tmc147.getServiceCode())) {
            return;
        }

        TenantModeContract updateTmc = new TenantModeContract();
        TenantContract tc = tenantContractMap.get(tmc147.getServiceCode());
        
        // 检查并更新状态
        if (tmc147.getStatus() != SUBSCRIBED.getIndex() && tmc147.getStatus() != TRIALING.getIndex() 
            && DateUtil.compare(tc.getContractExpiryDate(), new Date()) >= 0) {
            updateTmc.setStatus(SUBSCRIBED.getIndex());
        }else {
            updateTmc.setStatus(tmc147.getStatus());
        }
        Date date = parseDefaultDate(defaultStartDate);

        // 设置基本信息
        updateTmc.setTmcId(tmc147.getTmcId());
        updateTmc.setStartDate(Optional.ofNullable(tmc147.getContractStartDate()).orElse(date));
        updateTmc.setEndDate(tmc147.getContractExpiryDate());
        updateTmc.setServiceCode(tmc147.getServiceCode());
        updateTmc.setSid(tmc147.getSid());
        updateTmc.setEid(tmc147.getEid());
        updateTmc.setModuleId(tmc147.getModuleId());
        updateTmc.setProductCode(PRODUCT_147);
        updateTmc.setStaffId(LongUtil.objectToLong(employeeSidMap.get(tmc147.getServiceCode())));

        tenantModuleContracts.add(updateTmc);
    }

    private void processNewContract(TenantModeContract tmc147, List<TenantModeContract> tenantModuleContractdetails,
                                    List<TenantModeContract> inserttenantModuleContracts,
                                    Map<String, Long> employeeSidMap, long defaultstaffId, String defaultStartDate) {
        // 只处理IT模组
        if (!IT_CODE.equals(tmc147.getModuleId())) {
            return;
        }

        // 解析默认日期
        Date date = parseDefaultDate(defaultStartDate);

        // 创建并设置合约信息
        TenantModeContract product147Tmc = createContract(tmc147, date, employeeSidMap, defaultstaffId);
        
        // 创建并设置合约明细
        TenantModeContract product147Tmcd = createContractDetail(tmc147, product147Tmc.getId());

        // 添加到相应集合
        tenantModuleContractdetails.add(product147Tmcd);
        inserttenantModuleContracts.add(product147Tmc);
    }

    private Date parseDefaultDate(String defaultStartDate) {
        try {
            return DateUtils.parseDate(defaultStartDate, new String[]{"yyyy-MM-dd"});
        } catch (ParseException e) {
            return null;
        }
    }

    private TenantModeContract createContract(TenantModeContract tmc147, Date date, Map<String, Long> employeeSidMap,
                                              long defaultstaffId) {
        TenantModeContract contract = new TenantModeContract();
        contract.setStartDate(Optional.ofNullable(tmc147.getContractStartDate()).orElse(date));
        contract.setEndDate(tmc147.getContractExpiryDate());
        contract.setModuleId(tmc147.getModuleId());
        contract.setSid(tmc147.getSid());
        contract.setEid(tmc147.getEid());
        contract.setServiceCode(tmc147.getServiceCode());
        contract.setStatus(ModuleContractStatus.SUBSCRIBED.getIndex());
        contract.setProductCode(PRODUCT_147);
        contract.setStaffId(employeeSidMap.getOrDefault(tmc147.getServiceCode(), RequestUtil.getHeaderSidOrDefault(defaultstaffId)));
        contract.setId(SnowFlake.getInstance().newId());
        return contract;
    }

    private TenantModeContract createContractDetail(TenantModeContract tmc147, long tmcId) {
        TenantModeContract detail = new TenantModeContract();
        detail.setTmcId(tmcId);
        detail.setId(SnowFlake.getInstance().newId());
        detail.setSamcId(syncTenantService.getSupplierAiopsmModuleClassId(tmc147.getModuleId(), "HOST"));
        detail.setUsedCount(0);
        detail.setAvailableCount(getAvailableCount(tmc147.getModuleCode()));
        return detail;
    }


    // 获取个数
    private static int getAvailableCount(String moduleCode) {
        Map<String, Integer> moduleCodeMap = new HashMap<>();
        moduleCodeMap.put("IT", 3);
        moduleCodeMap.put("MSSQLDB", 1);
        moduleCodeMap.put("08", 1);
        moduleCodeMap.put("37", 1);
        moduleCodeMap.put("100", 1);
        moduleCodeMap.put("ORACLEDB", 1);
        moduleCodeMap.put("06", 1);
        moduleCodeMap.put("164", 1);
        moduleCodeMap.put("165", 1);
        return moduleCodeMap.getOrDefault(moduleCode, 0);
    }


    private List<TenantModeContract> holdTenantList(List<TenantModeContract> tenantList) {

        return tenantList.stream().filter(t -> SUBSCRIBED.getIndex() != t.getStatus())
                .filter(t -> TRIALING.getIndex() != t.getStatus())
                // 拿到没有模组合约 或者 模组合约 已过期


                .filter(t -> !PRODUCT_147.equals(t.getProductCode()))
                .collect(Collectors.toList());
    }

    private List<TenantModeContract> get147ProductTenantList(List<TenantModeContract> tenantList) {

        return tenantList.stream()
                .filter(t -> PRODUCT_147.equals(t.getProductCode()))
                .collect(Collectors.toList());
    }

    /**
     * 10766【後端API】【Athena稳态自动更新】稳态自动更新的授权起讫同步逻辑调整，支持稳态根据敏态的合约管控自动更新
     * 處理標記為自動更新的應用程式的授權復原。
     * 此方法檢索代表需要的應用程式的 ID 列表
     * 自動更新的授權恢復。這些 ID 是從
     * 使用syncMapper的getAppAutoUpdateRecoverAuthIds()方法的資料來源。
     * 檢索到列表後，呼叫recoverAuthCore方法進行處理
     * 恢復這些應用程式的授權。
     */
    public void recoverAuthForAppAutoUpdate() {
        List<Long> needRecoverAuthTmcIdList = this.syncMapper.getAppAutoUpdateRecoverAuthIds();
        this.recoverAuthCore(needRecoverAuthTmcIdList);
    }


    /**
     * 根據提供的租戶識別碼清單恢復租戶的授權。
     * 檢索所有租戶合同，根據特定條件（例如狀態、對過濾後的清單進行授權恢復操作。
     *
     * @param currentList 入参 是全部租户合约数据
     */
    private void recoverAuth(List<String> currentList) {
        List<TenantModeContract> allTenantList = syncTenantService.getAllTenantContract(currentList);
        List<Long> needRecoverAuthTmcIdList = allTenantList.stream().filter(tmc -> Objects.nonNull(tmc.getTmcId()))
                .filter(tmc -> tmc.getStatus() != 0)
                .filter(tmc -> tmc.getStatus() == ModuleContractStatus.ANNUAL_MAINTENANCE_SERVICES.getIndex()
                        || tmc.getStatus() == SUBSCRIBED.getIndex()
                        || tmc.getStatus() == TRIALING.getIndex())
                .map(TenantModeContract::getTmcId)
                .collect(Collectors.toList());

        // 執行授權回復的操作
        this.recoverAuthCore(needRecoverAuthTmcIdList);
    }

    /**
     * 3.1.当入参tenantModuleContract.status为1、3、5时，多增加恢复aiops_instance表里面tmcdId相等且recoverableAuth=1的实例为已授权
     * 3.2.若因已购数量调整(如：3改为1)，造成无法恢复，则根据ai.__version__倒排序，恢复最近的可恢复数(1)的实例为已授权
     * 3.3. 3.1和3.2走完之后 recoverableAuth更新为0
     * 3.4 更新 tmcd useCount
     * @param needRecoverAuthTmcIdList 入参 要處理的 tmcIds
     * @return
     */
    private void recoverAuthCore(List<Long> needRecoverAuthTmcIdList) {
        try {
            ResponseBase<List<TenantModuleContractDetail>> tmcdListRes
                    = aioUserFeignClient.getTmcdListByTmcIdList(needRecoverAuthTmcIdList);

            if (tmcdListRes.checkIsSuccess() && !CollectionUtils.isEmpty(tmcdListRes.getData())) {
                List<TenantModuleContractDetail> tmcdList = tmcdListRes.getData();
                List<AiopsInstanceFixParam> fixParamList = tmcdList.stream().map(tmcd -> {
                    AiopsInstanceFixParam fixParam = new AiopsInstanceFixParam();
                    fixParam.setTmcdId(tmcd.getId());
                    fixParam.setAvailableCount(tmcd.getAvailableCount());
                    return fixParam;
                }).collect(Collectors.toList());

                aioItmsFeignClient.fixAiopsInstanceStatusByTmcdIdList(fixParamList);
            }
        } catch (Exception e) {
            log.error("[dealAiopsInstance] error {}", e.getMessage(), e);
        }
    }

    private Map<String, Long>  getEmployeeSid(List<String> serviceCodeList) {
        ServiceStaffRequest request = new ServiceStaffRequest();
        request.setServiceCodeList(serviceCodeList);
        request.setProductLine("147");
        BaseResponse<List<User>> moduleStaffRb = aioUserFeignClient.getModuleStaff(request);
        if (!moduleStaffRb.checkIsSuccess()) {
            return new HashMap<>();
        }
        List<User> userList = moduleStaffRb.getData();
        Map<String, Long> userMap = userList.stream().collect(Collectors.toMap(User::getServiceCode, User::getSid, (v1, v2) -> v1));

        return userMap;
    }

    @Override
    public Map<String,TenantContract> get147TenantContract(List<String> serviceCodeList) {
        List<TenantContractEx> product147Tce = syncMapper.select147TenantContractByDatetime(serviceCodeList,escloudDBName);
        Map<String, TenantContract> collect = product147Tce.stream()
                .filter(i -> LongUtil.isNotEmpty(i.getId()))
                .collect(Collectors.toMap(
                        TenantContractEx::getServiceCode,
                        i -> {
                            TenantContract tc = new TenantContract();
                            BeanUtils.copyProperties(i, tc);
                            return tc;
                        },
                        (v1, v2) -> v1
                ));
        log.info("[get147TenantContract] modify : {} ", JSONObject.toJSONString(collect));
        return collect;

    }

    @Override
    public BaseResponse<List<String>> onceSync147TenantContract(List<String> serviceCodeList, String updateTime) {
        String dUpdateTime = org.apache.commons.lang3.StringUtils.isEmpty(updateTime) ? "2025-01-15 00:00:00" : updateTime;
        List<TenantContractEx> all147TcList = syncMapper.selectAll147TenantContract(escloudDBName, serviceCodeList, dUpdateTime);
        Map<String, TenantContract> all147TcMap = all147TcList.stream()
                .collect(Collectors.toMap(
                        TenantContractEx::getServiceCode,
                        i -> {
                            TenantContract tc = new TenantContract();
                            BeanUtils.copyProperties(i, tc);
                            return tc;
                        },
                        (v1, v2) -> v1
                ));
        List<String> execServiceCodeList = new ArrayList<>(all147TcMap.keySet());
        log.info("[onceSync147TenantContract] all147 : {} ", JSONObject.toJSONString(all147TcMap));
        syncAllTenantContract_CN(execServiceCodeList, all147TcMap);
        return BaseResponse.okT(execServiceCodeList);
    }

    @Override
    public Map<String,TenantContract> getIncomplatente147Tenant() {
        List<TenantContractEx> tenantContractExes = syncMapper.select147TenantContractByInstalled(escloudDBName);
        Map<String, TenantContract> collect = tenantContractExes.stream()
                .filter(i -> LongUtil.isNotEmpty(i.getId()))
                .collect(Collectors.toMap(
                        TenantContractEx::getServiceCode,
                        i -> {
                            TenantContract tc = new TenantContract();
                            BeanUtils.copyProperties(i, tc);
                            return tc;
                        },
                        (v1, v2) -> v1
                ));

        collect.entrySet().removeIf(entry -> {
            String redisKey = SEND_MAIL_TENANT_SID_KEY + entry.getKey();
            Object redisValue = redisTemplate.opsForValue().get(redisKey);
            if (redisValue == null) {
                //如果key不存在,添加key,并设置过期时间到第二天凌晨
                LocalDateTime midnight = LocalDateTime.now().toLocalDate().atTime(LocalTime.MIDNIGHT).plusDays(1);
                long seconds = Duration.between(LocalDateTime.now(), midnight).getSeconds();
                redisTemplate.opsForValue().set(redisKey, "1", seconds, TimeUnit.SECONDS);
                return false; // 不移除
            }
            return true; // 移除
        });
        log.info("[getIncomplatente147Tenant] incomplatente : {} ", JSONObject.toJSONString(collect));
        return collect;
    }

    private void processOther(List<TenantModeContract> mailNoticeMailList) {
        Long sid = mailNoticeMailList.stream().map(TenantModeContract::getSid).findFirst().orElse(null);
        Map<Long, List<TenantModeContract>> staffIdMap = mailNoticeMailList.stream()
                .collect(Collectors.groupingBy(TenantModeContract::getStaffId));

        Long headerEid = RequestUtil.getHeaderEid();
        if (LongUtil.isEmpty(headerEid)) {
            headerEid = LongUtil.objectToLong(SyncConst.TENANT_ID_DIGIWIN);
        }

        String headerToken = RequestUtil.getHeaderToken();
        if (org.apache.commons.lang3.StringUtils.isEmpty(headerToken)) {
            headerToken = iamService.grantAccessToken(verifyUserId, tenantId);
        }


        for (Map.Entry<Long, List<TenantModeContract>> entry : staffIdMap.entrySet()) {
            ProductLine147NoticeMailParams mailParams = new ProductLine147NoticeMailParams();
            mailParams.setUserSid(entry.getKey());
            mailParams.setSid(sid);
            List<TenantModeContract> tmcList = entry.getValue();

            List<ProductLine147NoticeMailParams.TenantModuleInfo> tmiList = tmcList.stream().map(tmc -> {
                ProductLine147NoticeMailParams.TenantModuleInfo tmi = new ProductLine147NoticeMailParams.TenantModuleInfo();
                tmi.setEid(tmc.getEid());
                tmi.setModuleId(tmc.getModuleId());
                tmi.setServiceCode(tmc.getServiceCode());
                return tmi;
            }).collect(Collectors.toList());

            mailParams.setTenantModuleInfoList(tmiList);
            mailParams.setToken(headerToken);
            mailParams.setHeaderEid(headerEid);

            log.info("[processOther] mailParams {}", JSONObject.toJSONString(mailParams));
            aioUserFeignClient.sendMailAndProcessOther(mailParams);
        }


    }
}
