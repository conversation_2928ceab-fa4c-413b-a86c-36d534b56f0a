package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 资产变更申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@ApiModel(value = "AssetChangeApplication对象", description = "资产变更申请单")
public class AssetChangeApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("eid")
    private Long eid;

    @ApiModelProperty("申请单编号（唯一索引）")
    private String applicationNumber;

    @ApiModelProperty("申请日期")
    private LocalDateTime applicationDate;

    @ApiModelProperty("申请人用户ID")
    private String applicantUserId;

    @ApiModelProperty("申请人名称")
    private String applicantName;

    @ApiModelProperty("申请人单位")
    private String applicantUnit;

    @ApiModelProperty("申请类别")
    private String applicationCategory;

    @ApiModelProperty("申请单状态")
    private String applicationStatus;

    @ApiModelProperty("最近一次审批意见")
    private String latestApprovalComment;

    @ApiModelProperty("最近一次验收意见")
    private String latestAcceptanceComment;

    @ApiModelProperty("变更背景原因")
    private String changeBackgroundReason;

    @ApiModelProperty("变更内容描述")
    private String changeContentDescription;

    @ApiModelProperty("变更优先级")
    private String changePriority;

    @ApiModelProperty("风险评估")
    private String riskAssessment;

    @ApiModelProperty("还原计划")
    private String rollbackPlan;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEid() {
        return eid;
    }

    public void setEid(Long eid) {
        this.eid = eid;
    }

    public String getApplicationNumber() {
        return applicationNumber;
    }

    public void setApplicationNumber(String applicationNumber) {
        this.applicationNumber = applicationNumber;
    }

    public LocalDateTime getApplicationDate() {
        return applicationDate;
    }

    public void setApplicationDate(LocalDateTime applicationDate) {
        this.applicationDate = applicationDate;
    }

    public String getApplicantUserId() {
        return applicantUserId;
    }

    public void setApplicantUserId(String applicantUserId) {
        this.applicantUserId = applicantUserId;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getApplicantUnit() {
        return applicantUnit;
    }

    public void setApplicantUnit(String applicantUnit) {
        this.applicantUnit = applicantUnit;
    }

    public String getApplicationCategory() {
        return applicationCategory;
    }

    public void setApplicationCategory(String applicationCategory) {
        this.applicationCategory = applicationCategory;
    }

    public String getApplicationStatus() {
        return applicationStatus;
    }

    public void setApplicationStatus(String applicationStatus) {
        this.applicationStatus = applicationStatus;
    }

    public String getLatestApprovalComment() {
        return latestApprovalComment;
    }

    public void setLatestApprovalComment(String latestApprovalComment) {
        this.latestApprovalComment = latestApprovalComment;
    }

    public String getLatestAcceptanceComment() {
        return latestAcceptanceComment;
    }

    public void setLatestAcceptanceComment(String latestAcceptanceComment) {
        this.latestAcceptanceComment = latestAcceptanceComment;
    }

    public String getChangeBackgroundReason() {
        return changeBackgroundReason;
    }

    public void setChangeBackgroundReason(String changeBackgroundReason) {
        this.changeBackgroundReason = changeBackgroundReason;
    }

    public String getChangeContentDescription() {
        return changeContentDescription;
    }

    public void setChangeContentDescription(String changeContentDescription) {
        this.changeContentDescription = changeContentDescription;
    }

    public String getChangePriority() {
        return changePriority;
    }

    public void setChangePriority(String changePriority) {
        this.changePriority = changePriority;
    }

    public String getRiskAssessment() {
        return riskAssessment;
    }

    public void setRiskAssessment(String riskAssessment) {
        this.riskAssessment = riskAssessment;
    }

    public String getRollbackPlan() {
        return rollbackPlan;
    }

    public void setRollbackPlan(String rollbackPlan) {
        this.rollbackPlan = rollbackPlan;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AssetChangeApplication{" +
            "id = " + id +
            ", eid = " + eid +
            ", applicationNumber = " + applicationNumber +
            ", applicationDate = " + applicationDate +
            ", applicantUserId = " + applicantUserId +
            ", applicantName = " + applicantName +
            ", applicantUnit = " + applicantUnit +
            ", applicationCategory = " + applicationCategory +
            ", applicationStatus = " + applicationStatus +
            ", latestApprovalComment = " + latestApprovalComment +
            ", latestAcceptanceComment = " + latestAcceptanceComment +
            ", changeBackgroundReason = " + changeBackgroundReason +
            ", changeContentDescription = " + changeContentDescription +
            ", changePriority = " + changePriority +
            ", riskAssessment = " + riskAssessment +
            ", rollbackPlan = " + rollbackPlan +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
        "}";
    }
}
