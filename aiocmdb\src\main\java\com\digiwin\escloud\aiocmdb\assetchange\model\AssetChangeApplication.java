package com.digiwin.escloud.aiocmdb.assetchange.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 资产变更申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Data
@ApiModel(value = "AssetChangeApplication对象", description = "资产变更申请单")
public class AssetChangeApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("eid")
    private Long eid;

    @ApiModelProperty("申请单编号（唯一索引）")
    private String applicationNumber;

    @ApiModelProperty("申请日期")
    private LocalDateTime applicationDate;

    @ApiModelProperty("申请人用户ID")
    private String applicantUserId;

    @ApiModelProperty("申请人名称")
    private String applicantName;

    @ApiModelProperty("申请人单位")
    private String applicantUnit;

    @ApiModelProperty("申请类别")
    private String applicationCategory;

    private String changeRange;

    @ApiModelProperty("申请单状态")
    private String applicationStatus;

    @ApiModelProperty("最近一次审批意见")
    private String latestApprovalComment;

    @ApiModelProperty("最近一次验收意见")
    private String latestAcceptanceComment;

    @ApiModelProperty("变更背景原因")
    private String changeBackgroundReason;

    @ApiModelProperty("变更内容描述")
    private String changeContentDescription;

    @ApiModelProperty("变更优先级")
    private String changePriority;

    @ApiModelProperty("风险评估")
    private String riskAssessment;

    @ApiModelProperty("还原计划")
    private String rollbackPlan;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;



    @Override
    public String toString() {
        return "AssetChangeApplication{" +
            "id = " + id +
            ", eid = " + eid +
            ", applicationNumber = " + applicationNumber +
            ", applicationDate = " + applicationDate +
            ", applicantUserId = " + applicantUserId +
            ", applicantName = " + applicantName +
            ", applicantUnit = " + applicantUnit +
            ", applicationCategory = " + applicationCategory +
            ", applicationStatus = " + applicationStatus +
            ", latestApprovalComment = " + latestApprovalComment +
            ", latestAcceptanceComment = " + latestAcceptanceComment +
            ", changeBackgroundReason = " + changeBackgroundReason +
            ", changeContentDescription = " + changeContentDescription +
            ", changePriority = " + changePriority +
            ", riskAssessment = " + riskAssessment +
            ", rollbackPlan = " + rollbackPlan +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
        "}";
    }
}
