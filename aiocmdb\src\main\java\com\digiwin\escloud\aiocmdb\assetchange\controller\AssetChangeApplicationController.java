package com.digiwin.escloud.aiocmdb.assetchange.controller;

import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationSaveRequest;
import com.digiwin.escloud.aiocmdb.assetchange.service.IAssetChangeApplicationService;
import com.digiwin.escloud.common.model.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 资产变更申请单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Api(tags = "资产变更申请单管理")
@RestController
@RequestMapping("/asset/assetChangeApplication")
public class AssetChangeApplicationController {

    @Autowired
    private IAssetChangeApplicationService assetChangeApplicationService;

    @ApiOperation(value = "新增/保存资产变更申请单")
    @PostMapping("/save")
    public ResponseBase saveAssetChangeApplication(@RequestBody AssetChangeApplicationSaveRequest request) {
        return assetChangeApplicationService.saveAssetChangeApplication(request);
    }
}
